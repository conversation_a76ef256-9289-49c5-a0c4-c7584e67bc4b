{"datasource": {"type": "stackdriver", "uid": "${stackdriver_uid}"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "light-yellow", "mode": "palette-classic-by-name"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 2, "gradientMode": "hue", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 1, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "max": 1, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 105}, "id": 68, "interval": "15s", "options": {"legend": {"calcs": [], "displayMode": "table", "placement": "right", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.0.0-85518.patch3-85676", "targets": [{"datasource": {"type": "stackdriver", "uid": "${stackdriver_uid}"}, "queryType": "timeSeriesList", "refId": "A", "timeSeriesList": {"alignmentPeriod": "cloud-monitoring-auto", "crossSeriesReducer": "REDUCE_NONE", "filters": ["metric.label.instance_name", "=~", "^${prefix}orch-api-.*$", "AND", "metric.type", "=", "compute.googleapis.com/instance/cpu/utilization"], "groupBys": ["metric.label.instance_name"], "perSeriesAligner": "ALIGN_MEAN", "preprocessor": "none", "projectName": "${gcp_project_id}", "view": "FULL"}}], "title": "API Nodes CPU Utilization", "transformations": [{"id": "renameByRegex", "options": {"regex": "compute.googleapis.com/instance/cpu/utilization (.*)", "renamePattern": "$1"}}], "type": "timeseries"}