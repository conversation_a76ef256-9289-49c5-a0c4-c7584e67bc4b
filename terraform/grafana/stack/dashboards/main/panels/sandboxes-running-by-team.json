{"datasource": {"type": "prometheus", "uid": "grafanacloud-prom"}, "description": "This might be deceptive because of the 15s interval it does not show sandboxes if they are started and stopped in the meantime.", "fieldConfig": {"defaults": {"color": {"fixedColor": "green", "mode": "palette-classic-by-name"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": 1, "barWidthFactor": 1, "drawStyle": "bars", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 0, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "fieldMinMax": false, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "sbxs"}, "overrides": []}, "gridPos": {"h": 8, "w": 10, "x": 6, "y": 126}, "id": 9, "interval": "15s", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"hideZeros": false, "maxHeight": 600, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.0-83314", "targets": [{"datasource": {"type": "prometheus", "uid": "grafanacloud-prom"}, "disableTextWrap": false, "editorMode": "builder", "exemplar": false, "expr": "sum by(team_id) (api_env_instance_running{team_id=~\"$team_id\"} > 0)", "format": "time_series", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "interval": "0", "key": "Q-3fe1cdb0-a7c3-4c0c-a782-965069f7682e-0", "legendFormat": "{{team_id}}", "range": true, "refId": "A", "useBackend": false}], "title": "Running sandboxes (by team)", "type": "timeseries"}