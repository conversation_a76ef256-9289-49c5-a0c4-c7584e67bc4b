{"datasource": {"type": "prometheus", "uid": "grafanacloud-prom"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic-by-name"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": -1, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 0, "pointSize": 1, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "fieldMinMax": false, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "sbx/s"}, "overrides": []}, "gridPos": {"h": 8, "w": 18, "x": 6, "y": 2}, "id": 36, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Last *", "sortDesc": true}, "tooltip": {"hideZeros": false, "maxHeight": 600, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.0.0-85518.patch7-85777", "targets": [{"datasource": {"type": "prometheus", "uid": "grafanacloud-prom"}, "disableTextWrap": false, "editorMode": "code", "exemplar": false, "expr": "sum by(team_id) (irate(api_env_instance_started_total{team_id=~\"$team_id\"}[$__rate_interval])) > 0", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "{{team_id}}", "range": true, "refId": "A", "useBackend": false}], "title": "Sandbox start rate (by team)", "type": "timeseries"}