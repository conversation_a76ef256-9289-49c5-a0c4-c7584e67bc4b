{"datasource": {"type": "prometheus", "uid": "grafanacloud-prom"}, "description": "Shows rates for errors during sandbox pause\n\nWe are excluding the E2B testing team here.", "fieldConfig": {"defaults": {"color": {"fixedColor": "red", "mode": "shades"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "bars", "fillOpacity": 50, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 0, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "4xx errors"}, "properties": [{"id": "color", "value": {"fixedColor": "yellow", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "5xx errors"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "id": 85, "interval": "60s", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.0-85518.patch7-85777", "targets": [{"datasource": {"type": "prometheus", "uid": "grafanacloud-prom"}, "editorMode": "code", "expr": "# 9. HTTP 4xx Error Count (Exact counts per interval)\nhistogram_count(sum (\n    increase(orchestration_api_http_server_duration_milliseconds{http_method=\"POST\", http_route=\"/sandboxes/:sandboxID/pause\", http_status_code=\"400\"}[$__interval])\n))", "format": "time_series", "instant": false, "interval": "60s", "legendFormat": "4xx errors", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "grafanacloud-prom"}, "editorMode": "code", "expr": "# 9. HTTP 5xx Error Count (Exact counts per interval)\nhistogram_count(sum (\n    increase(orchestration_api_http_server_duration_milliseconds{http_method=\"POST\", http_route=\"/sandboxes/:sandboxID/pause\", http_status_code=\"500\"}[$__interval])\n))", "hide": false, "instant": false, "interval": "60", "legendFormat": "5xx errors", "range": true, "refId": "B"}], "title": "Errors For PAUSE Sandbox", "type": "timeseries"}