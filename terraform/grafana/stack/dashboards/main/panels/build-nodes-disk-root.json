{"datasource": {"type": "prometheus", "uid": "grafanacloud-prom"}, "description": "", "fieldConfig": {"defaults": {"color": {"fixedColor": "super-light-red", "mode": "fixed"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 2, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 1, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "gbytes"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Max"}, "properties": [{"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}, {"id": "custom.fillOpacity", "value": 0}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 123}, "id": 67, "interval": "15s", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "asc"}}, "pluginVersion": "11.6.0-83314", "targets": [{"datasource": {"type": "prometheus", "uid": "grafanacloud-prom"}, "disableTextWrap": false, "editorMode": "builder", "expr": "nomad_client_host_disk_available{disk=\"/dev/sda1\", node_pool=\"build\"} / 1073741824", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "{{node_id}}", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "grafanacloud-prom"}, "disableTextWrap": false, "editorMode": "builder", "expr": "max(nomad_client_host_disk_size{disk=\"/dev/sda1\", node_pool=\"build\"}) / 1073741824", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "Max", "range": true, "refId": "B", "useBackend": false}], "title": "Build Nodes Root Disk", "type": "timeseries"}