{"datasource": {"type": "prometheus", "uid": "grafanacloud-prom"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "yellow", "mode": "fixed"}, "fieldMinMax": false, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "sbx/s"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 1}, "id": 51, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": true, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.0-85518.patch7-85777", "targets": [{"datasource": {"type": "prometheus", "uid": "grafanacloud-prom"}, "disableTextWrap": false, "editorMode": "builder", "exemplar": false, "expr": "sum(irate(api_env_instance_started_total{team_id=~\"$team_id\"}[$__rate_interval])) > 0", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "{{team_id}}", "range": true, "refId": "A", "useBackend": false}], "title": "Sandbox start rate", "transparent": true, "type": "stat"}