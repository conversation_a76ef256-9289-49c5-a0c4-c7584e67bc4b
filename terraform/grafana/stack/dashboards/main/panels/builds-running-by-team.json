{"datasource": {"type": "prometheus", "uid": "grafanacloud-prom"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "bars", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "stepBefore", "lineWidth": 0, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "fieldMinMax": false, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 124}, "id": 20, "interval": "15s", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.0-83314", "targets": [{"datasource": {"type": "prometheus", "uid": "grafanacloud-prom"}, "disableTextWrap": false, "editorMode": "builder", "exemplar": false, "expr": "api_env_build_running{team_id=~\"$team_id\"} == 1", "format": "time_series", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "interval": "0", "key": "Q-3fe1cdb0-a7c3-4c0c-a782-965069f7682e-0", "legendFormat": "Env ID {{env_id}}, Build ID {{build_id}}", "range": true, "refId": "A", "useBackend": false}], "title": "Builds", "type": "timeseries"}