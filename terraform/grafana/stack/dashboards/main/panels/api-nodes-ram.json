{"datasource": {"type": "stackdriver", "uid": "${stackdriver_uid}"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "purple", "mode": "fixed"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 2, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 1, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "fieldMinMax": false, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "gbytes"}, "overrides": [{"matcher": {"id": "byFrameRefID", "options": "B"}, "properties": [{"id": "custom.drawStyle", "value": "line"}, {"id": "custom.fillOpacity", "value": 0}, {"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 104}, "id": 38, "interval": "15s", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"hideZeros": false, "maxHeight": 600, "mode": "multi", "sort": "asc"}}, "pluginVersion": "12.0.0-85518.patch3-85676", "targets": [{"aliasBy": "", "datasource": {"type": "stackdriver", "uid": "${stackdriver_uid}"}, "queryType": "timeSeriesList", "refId": "A", "timeSeriesList": {"alignmentPeriod": "cloud-monitoring-auto", "crossSeriesReducer": "REDUCE_NONE", "filters": ["metric.label.instance_name", "=~", "^${prefix}orch-api-.*$", "AND", "metric.type", "=", "compute.googleapis.com/instance/memory/balloon/ram_used"], "groupBys": ["metric.label.instance_name"], "perSeriesAligner": "ALIGN_MEAN", "preprocessor": "none", "projectName": "${gcp_project_id}"}}, {"aliasBy": "Max", "datasource": {"type": "stackdriver", "uid": "${stackdriver_uid}"}, "hide": false, "queryType": "timeSeriesList", "refId": "B", "timeSeriesList": {"alignmentPeriod": "cloud-monitoring-auto", "crossSeriesReducer": "REDUCE_MEAN", "filters": ["metric.label.instance_name", "=~", "^${prefix}orch-api-.*$", "AND", "metric.type", "=", "compute.googleapis.com/instance/memory/balloon/ram_size"], "groupBys": ["resource.label.project_id"], "perSeriesAligner": "ALIGN_MEAN", "preprocessor": "none", "projectName": "${gcp_project_id}", "view": "FULL"}}], "title": "API Nodes RAM", "transformations": [{"id": "renameByRegex", "options": {"regex": "compute.googleapis.com/instance/memory/balloon/ram_used (.*)", "renamePattern": "$1"}}], "type": "timeseries"}