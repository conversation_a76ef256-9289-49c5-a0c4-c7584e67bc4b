{"datasource": {"type": "tempo", "uid": "grafanacloud-traces"}, "fieldConfig": {"defaults": {"custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 125}, "id": 19, "options": {"cellHeight": "sm", "footer": {"countRows": false, "enablePagination": true, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Start time"}]}, "pluginVersion": "11.6.0-83314", "targets": [{"datasource": {"type": "tempo", "uid": "grafanacloud-traces"}, "filters": [{"id": "span-name", "operator": "=", "scope": "span", "tag": "name", "value": ["background-build-env"], "valueType": "string"}, {"id": "c91af756", "operator": "=", "scope": "span"}], "hide": false, "limit": 50, "metricsQueryType": "range", "query": "{name=\"background-build-env\"}", "queryType": "traceqlSearch", "refId": "A", "spss": 50, "tableType": "traces"}], "title": "BUILD Template Requests", "transformations": [{"id": "organize", "options": {"excludeByName": {"Name": true, "Service": true, "Start time": false, "nested": true}, "includeByName": {}, "indexByName": {"Duration": 1, "Name": 4, "Service": 3, "Start time": 2, "Trace ID": 0, "nested": 5}, "renameByName": {"Start time": ""}}}], "type": "table"}