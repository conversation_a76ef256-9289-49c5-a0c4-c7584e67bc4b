{"datasource": {"type": "prometheus", "uid": "grafanacloud-prom"}, "description": "Sandboxes started in the selected interval by each team.\n\nWe should refine the calculation to not approximate.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "fieldMinMax": false, "mappings": [], "unit": "sbx"}, "overrides": []}, "gridPos": {"h": 8, "w": 18, "x": 6, "y": 9}, "id": 53, "options": {"displayLabels": ["value"], "legend": {"displayMode": "table", "placement": "right", "showLegend": true, "values": ["value", "percent"]}, "pieType": "donut", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.0-84214", "targets": [{"datasource": {"type": "prometheus", "uid": "grafanacloud-prom"}, "disableTextWrap": false, "editorMode": "builder", "exemplar": false, "expr": "sum by(team_id) (increase(api_env_instance_started_total{team_id=~\"$team_id\"}[$__range])) > 0", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "{{team_id}}", "range": true, "refId": "A", "useBackend": false}], "title": "Started sandboxes (by team, in interval)", "transparent": true, "type": "piechart"}