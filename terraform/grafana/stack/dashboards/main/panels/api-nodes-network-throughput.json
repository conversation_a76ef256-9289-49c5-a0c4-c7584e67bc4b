{"datasource": {"type": "stackdriver", "uid": "${stackdriver_uid}"}, "description": "", "fieldConfig": {"defaults": {"color": {"fixedColor": "#56A64B", "mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": true, "axisColorMode": "text", "axisLabel": "bytes sent (-) / received (+)", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 3, "gradientMode": "hue", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "stepBefore", "lineWidth": 1, "pointSize": 1, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "fieldMinMax": true, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "<PERSON><PERSON>"}, "properties": [{"id": "color", "value": {"fixedColor": "blue", "mode": "fixed"}}, {"id": "custom.transform", "value": "negative-Y"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Received"}, "properties": [{"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 106}, "id": 81, "interval": "15s", "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.0.0-85518.patch3-85676", "targets": [{"datasource": {"type": "stackdriver", "uid": "${stackdriver_uid}"}, "queryType": "timeSeriesList", "refId": "A", "timeSeriesList": {"alignmentPeriod": "cloud-monitoring-auto", "crossSeriesReducer": "REDUCE_SUM", "filters": ["metric.label.instance_name", "=~", "^${prefix}orch-api-.*$", "AND", "metric.type", "=", "compute.googleapis.com/instance/network/sent_bytes_count"], "groupBys": [], "perSeriesAligner": "ALIGN_NONE", "preprocessor": "rate", "projectName": "${gcp_project_id}", "view": "FULL"}}, {"aliasBy": "", "datasource": {"type": "stackdriver", "uid": "${stackdriver_uid}"}, "hide": false, "queryType": "timeSeriesList", "refId": "B", "timeSeriesList": {"alignmentPeriod": "cloud-monitoring-auto", "crossSeriesReducer": "REDUCE_SUM", "filters": ["metric.label.instance_name", "=~", "^${prefix}orch-api-.*$", "AND", "metric.type", "=", "compute.googleapis.com/instance/network/received_bytes_count"], "groupBys": [], "perSeriesAligner": "ALIGN_NONE", "preprocessor": "rate", "projectName": "${gcp_project_id}", "view": "FULL"}}], "title": "API Nodes Network Throughput ", "transformations": [{"id": "renameByRegex", "options": {"regex": "compute.googleapis.com/instance/network/sent_bytes_count", "renamePattern": "<PERSON><PERSON>"}}, {"id": "renameByRegex", "options": {"regex": "compute.googleapis.com/instance/network/received_bytes_count", "renamePattern": "Received"}}], "type": "timeseries"}