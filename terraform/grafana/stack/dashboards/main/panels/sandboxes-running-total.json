{"datasource": {"type": "prometheus", "uid": "grafanacloud-prom"}, "description": "This number might be deceptive, because of the 1m interval it does not show sandboxes running for less than that.", "fieldConfig": {"defaults": {"color": {"fixedColor": "dark-orange", "mode": "thresholds"}, "fieldMinMax": false, "mappings": [], "noValue": "no running sandboxes", "thresholds": {"mode": "absolute", "steps": [{"color": "super-light-green"}, {"color": "light-green", "value": 25}, {"color": "green", "value": 100}, {"color": "dark-green", "value": 250}, {"color": "yellow", "value": 500}, {"color": "orange", "value": 1000}, {"color": "red", "value": 2500}]}, "unit": "sbxs"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 125}, "id": 8, "interval": "60s", "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.0-83314", "targets": [{"datasource": {"type": "prometheus", "uid": "grafanacloud-prom"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sum(api_env_instance_running{team_id=~\"$team_id\"}) > 0", "fullMetaSearch": false, "includeNullMetadata": false, "instant": false, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}], "title": "Running sandboxes", "transparent": true, "type": "stat"}