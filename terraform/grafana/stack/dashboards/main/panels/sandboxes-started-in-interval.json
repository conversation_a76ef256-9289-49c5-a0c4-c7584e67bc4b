{"datasource": {"type": "prometheus", "uid": "grafanacloud-prom"}, "description": "We should refine the calculation to not approximate.", "fieldConfig": {"defaults": {"color": {"fixedColor": "orange", "mode": "fixed"}, "fieldMinMax": false, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "orange"}]}, "unit": "sbxs"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 9}, "id": 54, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.0-84214", "targets": [{"datasource": {"type": "prometheus", "uid": "grafanacloud-prom"}, "disableTextWrap": false, "editorMode": "builder", "exemplar": false, "expr": "sum(increase(api_env_instance_started_total{team_id=~\"$team_id\"}[$__range]))", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "{{team_id}}", "range": true, "refId": "A", "useBackend": false}], "title": "Started sandboxes (in interval)", "transparent": true, "type": "stat"}