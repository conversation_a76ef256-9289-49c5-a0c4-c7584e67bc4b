{"datasource": {"type": "prometheus", "uid": "grafanacloud-prom"}, "description": "Connections from client proxies to orchestrators.\n\nThe sum can differ from the sum orchestrator server connections, because the client dial can be left unused,  if another established connection becomes available in the meantime.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic-by-name"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "conns"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 1}, "id": 5, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.0-86898", "targets": [{"disableTextWrap": false, "editorMode": "builder", "expr": "client_proxy_proxy_pool_connections_open", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "{{instance}}", "range": true, "refId": "A", "useBackend": false}], "title": "Client Proxy Pool Connections", "type": "timeseries"}