name: "Build Packages"
description: "Compiles and builds Go services."

runs:
  using: "composite"
  steps:
    - name: Setup Go
      uses: ./.github/actions/go-setup-cache

    - name: Go Build Cache Path
      id: go-cache-paths
      run: |
        echo "go-build=$(go env GOCACHE)" >> $GITHUB_OUTPUT
      shell: bash

    - name: Go Build Cache
      uses: actions/cache@v4
      with:
        path: ${{ steps.go-cache-paths.outputs.go-build }}
        key: ${{ runner.os }}-go-build-${{ hashFiles('**/go.sum', '**/go.mod') }}

    - name: Build Packages
      run: |
        make -C tests/integration build-debug
        make -C packages/db build-debug
        make -C packages/orchestrator build-debug
        make -C packages/api build-debug
        make -C packages/envd build-debug
      shell: bash
