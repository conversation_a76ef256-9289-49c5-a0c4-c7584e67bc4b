name: "Go Setup"
description: "Sets up go and gets modules from the cache"

runs:
  using: "composite"
  steps:
    - name: Setup Golang
      uses: actions/setup-go@v5
      with:
        go-version-file: 'go.work'
        cache: false

    - name: Go Mod Cache
      uses: actions/cache/restore@v4
      with:
        fail-on-cache-miss: true
        path: |
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-mod-${{ hashFiles('**/go.sum', '**/go.mod', 'go.work') }}
        restore-keys: |
          ${{ runner.os }}-go-mod-