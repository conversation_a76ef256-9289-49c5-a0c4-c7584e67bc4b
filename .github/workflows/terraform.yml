name: Terraform

on:
  workflow_call:
    secrets:
      service_account_email:
        required: true
      workload_identity_provider:
        required: true
      server_cluster_size:
        required: true
      server_machine_type:
        required: true
      client_cluster_size:
        required: true
      client_machine_type:
        required: true
      gcp_region:
        required: true
      gcp_zone:
        required: true
      gcp_project_id:
        required: true
      domain_name:
        required: true
      terraform_prefix:
        required: true
      terraform_state_bucket:
        required: true

env:
  TF_PLUGIN_CACHE_DIR: ${{ github.workspace }}/.terraform.d/plugin-cache

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Setup Service Account
        uses: google-github-actions/auth@v1
        with:
          create_credentials_file: true
          workload_identity_provider: ${{ secrets.workload_identity_provider }}
          service_account: ${{ secrets.service_account_email }}

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: 1.5.7

      - name: Create Terraform Plugin Cache Dir
        run: mkdir --parents $TF_PLUGIN_CACHE_DIR

      - name: Init Terraform
        run: make init
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          CLIENT_MACHINE_TYPE: ${{ secrets.client_machine_type }}
          CLIENT_CLUSTER_SIZE: ${{ secrets.client_cluster_size }}
          SERVER_MACHINE_TYPE: ${{ secrets.server_machine_type }}
          SERVER_CLUSTER_SIZE: ${{ secrets.server_cluster_size }}
          GCP_REGION: ${{ secrets.gcp_region }}
          GCP_ZONE: ${{ secrets.gcp_zone }}
          GCP_PROJECT_ID: ${{ secrets.gcp_project_id }}
          EXCLUDE_GITHUB: 0
          TERRAFORM_STATE_BUCKET: ${{ secrets.terraform_state_bucket }}
          PREFIX: ${{ secrets.terraform_prefix }}
          DOMAIN_NAME: ${{ secrets.domain_name }}
          TERRAFORM_ENVIRONMENT: prod

      - name: Plan deploy
        # Terraform plan returns a non-zero exit code
        run: make plan || true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          CLIENT_MACHINE_TYPE: ${{ secrets.client_machine_type }}
          CLIENT_CLUSTER_SIZE: ${{ secrets.client_cluster_size }}
          SERVER_MACHINE_TYPE: ${{ secrets.server_machine_type }}
          SERVER_CLUSTER_SIZE: ${{ secrets.server_cluster_size }}
          GCP_REGION: ${{ secrets.gcp_region }}
          GCP_ZONE: ${{ secrets.gcp_zone }}
          GCP_PROJECT_ID: ${{ secrets.gcp_project_id }}
          EXCLUDE_GITHUB: 0
          TERRAFORM_STATE_BUCKET: ${{ secrets.terraform_state_bucket }}
          PREFIX: ${{ secrets.terraform_prefix }}
          DOMAIN_NAME: ${{ secrets.domain_name }}
          TERRAFORM_ENVIRONMENT: prod

      - name: Deploy Terraform
        run: make apply
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          CLIENT_MACHINE_TYPE: ${{ secrets.client_machine_type }}
          CLIENT_CLUSTER_SIZE: ${{ secrets.client_cluster_size }}
          SERVER_MACHINE_TYPE: ${{ secrets.server_machine_type }}
          SERVER_CLUSTER_SIZE: ${{ secrets.server_cluster_size }}
          GCP_REGION: ${{ secrets.gcp_region }}
          GCP_ZONE: ${{ secrets.gcp_zone }}
          GCP_PROJECT_ID: ${{ secrets.gcp_project_id }}
          EXCLUDE_GITHUB: 0
          TERRAFORM_STATE_BUCKET: ${{ secrets.terraform_state_bucket }}
          PREFIX: ${{ secrets.terraform_prefix }}
          DOMAIN_NAME: ${{ secrets.domain_name }}
          TERRAFORM_ENVIRONMENT: prod
