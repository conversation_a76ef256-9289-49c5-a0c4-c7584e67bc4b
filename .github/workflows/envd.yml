name: Envd

on:
  workflow_call:
    secrets:
      service_account_email:
        required: true
      workload_identity_provider:
        required: true
      version:
        required: true
      gcp_project_id:
        required: true

jobs:
  publish:
    name: Build & upload
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up Go
        uses: actions/setup-go@v3
        with:
          go-version-file: ./packages/envd/go.mod
          cache: true
          cache-dependency-path: ./packages/envd/go.sum

      - name: Run GoReleaser
        uses: goreleaser/goreleaser-action@v3
        with:
          workdir: ./packages/envd
          version: latest
          args: release
        env:
          GITHUB_TOKEN: ${{ github.token  }}
          GORELEASER_CURRENT_TAG: ${{ secrets.version }}

      - name: Setup Service Account
        uses: google-github-actions/auth@v1
        with:
          workload_identity_provider: ${{ secrets.workload_identity_provider }}
          service_account: ${{ secrets.service_account_email }}

      - name: Upload envd
        uses: "google-github-actions/upload-cloud-storage@v1"
        with:
          path: "./packages/envd/dist/envd_linux_amd64_v1/envd"
          destination: "${{ secrets.gcp_project_id }}-fc-env-pipeline"
          gzip: false
