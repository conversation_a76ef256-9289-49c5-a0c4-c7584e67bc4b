name: Release

on: workflow_call

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}

permissions:
  id-token: write
  contents: write

jobs:
  changes:
    name: Repository changes
    runs-on: ubuntu-22.04
    outputs:
      get-version: ${{ steps.getversion.outputs.version }}
      cluster-disk-image: ${{ steps.filter.outputs.cluster-disk-image }}
      api: ${{ steps.filter.outputs.api }}
      docker-reverse-proxy: ${{ steps.filter.outputs.docker-reverse-proxy }}
      envd: ${{ steps.filter.outputs.envd }}
      fc-kernels: ${{ steps.filter.outputs.fc-kernels }}
      fc-versions: ${{ steps.filter.outputs.fc-versions }}
      template-manager: ${{ steps.filter.outputs.template-manager }}
      orchestrator: ${{ steps.filter.outputs.orchestrator }}
      version: ${{ steps.filter.outputs.version }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Get the last release
        id: last_release
        uses: cardinalby/git-get-release-action@v1
        env:
          GITHUB_TOKEN: ${{ github.token }}
        with:
          latest: true
          prerelease: false
          draft: false

      - name: Find changes since the last release
        uses: dorny/paths-filter@v2
        id: filter
        with:
          base: ${{ steps.last_release.outputs.tag_name }}
          filters: |
            cluster-disk-image:
              - 'packages/cluster-disk-image/**'
              - '.github/workflows/cluster-disk-image.yml'
            api:
              - 'packages/shared/**'
              - 'packages/api/**'
              - '.github/workflows/api.yml'
              - 'api.Dockerfile'
              - '.dockerignore'
            docker-reverse-proxy:
              - 'packages/shared/**'
              - 'packages/docker-reverse-proxy/**'
              - '.github/workflows/docker-reverse-proxy.yml'
            envd:
              - 'packages/shared/**'
              - 'packages/envd/**'
              - '.github/workflows/envd.yml'
            orchestrator:
              - 'packages/shared/**'
              - 'packages/orchestrator/**'
              - '.github/workflows/orchestrator.yml'
            template-manager:
              - 'packages/shared/**'
              - 'packages/template-manager/**'
              - '.github/workflows/template-manager.yml'
            fc-kernels:
              - 'packages/fc-kernels/**'
              - '.github/workflows/fc-kernels.yml'
            fc-versions:
              - 'packages/fc-versions/**'
              - '.github/workflows/fc-versions.yml'
            version:
              - 'VERSION'

      - name: Read Version file
        id: getversion
        run: echo "::set-output name=version::$(cat VERSION)"

  orchestrator:
    name: Orchestrator
    needs: changes
    if: |
      (!cancelled()) &&
      needs.changes.outputs.version == 'true' &&
      needs.changes.outputs.orchestrator == 'true'
    uses: ./.github/workflows/orchestrator.yml
    secrets:
      workload_identity_provider: ${{ secrets.E2B_WORKLOAD_IDENTITY_PROVIDER }}
      service_account_email: ${{ secrets.E2B_SERVICE_ACCOUNT_EMAIL }}
      gcp_project_id: ${{ secrets.E2B_GCP_PROJECT_ID}}

  template-manager:
    name: Template delete task driver
    needs: changes
    if: |
      (!cancelled()) &&
      needs.changes.outputs.version == 'true' &&
      needs.changes.outputs.template-manager == 'true'
    uses: ./.github/workflows/template-manager.yml
    secrets:
      workload_identity_provider: ${{ secrets.E2B_WORKLOAD_IDENTITY_PROVIDER }}
      service_account_email: ${{ secrets.E2B_SERVICE_ACCOUNT_EMAIL }}
      gcp_project_id: ${{ secrets.E2B_GCP_PROJECT_ID }}

  cluster-disk-image:
    name: Cluster disk image
    needs: changes
    if: |
      (!cancelled()) &&
      needs.changes.outputs.version == 'true' &&
      needs.changes.outputs.cluster-disk-image == 'true'
    uses: ./.github/workflows/cluster-disk-image.yml
    secrets:
      workload_identity_provider: ${{ secrets.E2B_WORKLOAD_IDENTITY_PROVIDER }}
      service_account_email: ${{ secrets.E2B_SERVICE_ACCOUNT_EMAIL }}

  envd:
    name: Env Daemon
    needs: changes
    if: |
      (!cancelled()) &&
      needs.changes.outputs.version == 'true' &&
      needs.changes.outputs.envd == 'true'
    uses: ./.github/workflows/envd.yml
    secrets:
      workload_identity_provider: ${{ secrets.E2B_WORKLOAD_IDENTITY_PROVIDER }}
      service_account_email: ${{ secrets.E2B_SERVICE_ACCOUNT_EMAIL }}
      gcp_project_id: ${{ secrets.E2B_GCP_PROJECT_ID }}
      version: ${{ needs.changes.outputs.get-version }}

  api:
    name: API image
    needs: changes
    if: |
      (!cancelled()) &&
      needs.changes.outputs.version == 'true' &&
      needs.changes.outputs.api == 'true'
    uses: ./.github/workflows/api.yml
    secrets:
      workload_identity_provider: ${{ secrets.E2B_WORKLOAD_IDENTITY_PROVIDER }}
      service_account_email: ${{ secrets.E2B_SERVICE_ACCOUNT_EMAIL }}
      gcp_project_id: ${{ secrets.E2B_GCP_PROJECT_ID }}
      gcp_region: ${{ secrets.E2B_GCP_REGION }}

  docker-reverse-proxy:
    name: Docker reverse proxy image
    needs: changes
    if: |
      (!cancelled()) &&
      needs.changes.outputs.version == 'true' &&
      needs.changes.outputs.docker-reverse-proxy == 'true'
    uses: ./.github/workflows/docker-reverse-proxy.yml
    secrets:
      workload_identity_provider: ${{ secrets.E2B_WORKLOAD_IDENTITY_PROVIDER }}
      service_account_email: ${{ secrets.E2B_SERVICE_ACCOUNT_EMAIL }}
      gcp_project_id: ${{ secrets.E2B_GCP_PROJECT_ID }}
      gcp_region: ${{ secrets.E2B_GCP_REGION }}

  fc-kernels:
    name: FC Kernels
    needs: changes
    if: |
      (!cancelled()) &&
      needs.changes.outputs.version == 'true' &&
      needs.changes.outputs.fc-kernels == 'true'
    uses: ./.github/workflows/fc-kernels.yml
    secrets:
      workload_identity_provider: ${{ secrets.E2B_WORKLOAD_IDENTITY_PROVIDER }}
      service_account_email: ${{ secrets.E2B_SERVICE_ACCOUNT_EMAIL }}
      gcp_project_id: ${{ secrets.E2B_GCP_PROJECT_ID }}

  fc-versions:
    name: FC Versions
    needs: changes
    if: |
      (!cancelled()) &&
      needs.changes.outputs.version == 'true' &&
      needs.changes.outputs.fc-versions == 'true'
    uses: ./.github/workflows/fc-versions.yml
    secrets:
      workload_identity_provider: ${{ secrets.E2B_WORKLOAD_IDENTITY_PROVIDER }}
      service_account_email: ${{ secrets.E2B_SERVICE_ACCOUNT_EMAIL }}
      gcp_project_id: ${{ secrets.E2B_GCP_PROJECT_ID }}

  terraform:
    name: Terraform
    needs:
      [
        changes,
        cluster-disk-image,
        fc-kernels,
        fc-versions,
        api,
        docker-reverse-proxy,
        envd,
        orchestrator,
        template-manager,
      ]
    if: |
      (!cancelled()) &&
      needs.changes.outputs.version == 'true' &&
      needs.changes.result == 'success' &&
      (needs.cluster-disk-image.result == 'success' || needs.cluster-disk-image.result == 'skipped') &&
      (needs.envd.result == 'success' || needs.envd.result == 'skipped') &&
      (needs.fc-kernels.result == 'success' || needs.fc-kernels.result == 'skipped') &&
      (needs.fc-versions.result == 'success' || needs.fc-versions.result == 'skipped') &&
      (needs.orchestrator.result == 'success' || needs.orchestrator.result == 'skipped') &&
      (needs.template-manager.result == 'success' || needs.template-manager.result == 'skipped') &&
      (needs.api.result == 'success' || needs.api.result == 'skipped') &&
      (needs.docker-reverse-proxy.result == 'success' || needs.docker-reverse-proxy.result == 'skipped')
    uses: ./.github/workflows/terraform.yml
    secrets:
      workload_identity_provider: ${{ secrets.E2B_WORKLOAD_IDENTITY_PROVIDER }}
      service_account_email: ${{ secrets.E2B_SERVICE_ACCOUNT_EMAIL }}
      client_machine_type: ${{ secrets.CLIENT_MACHINE_TYPE }}
      client_cluster_size: ${{ secrets.CLIENT_CLUSTER_SIZE }}
      server_machine_type: ${{ secrets.SERVER_MACHINE_TYPE }}
      server_cluster_size: ${{ secrets.SERVER_CLUSTER_SIZE }}
      gcp_region: ${{ secrets.E2B_GCP_REGION }}
      gcp_zone: ${{ secrets.E2B_GCP_ZONE }}
      gcp_project_id: ${{ secrets.E2B_GCP_PROJECT_ID }}
      domain_name: ${{ secrets.E2B_DOMAIN_NAME }}
      terraform_prefix: ${{ secrets.E2B_TERRAFORM_PREFIX }}
      terraform_state_bucket: ${{ secrets.E2B_TERRAFORM_STATE_BUCKET }}

  # The last successful release is used for determining which changed and what should be deployed in this release.
  release:
    name: Release
    needs: [changes, terraform]
    if: |
      (!cancelled()) &&
      needs.terraform.result == 'success' &&
      needs.changes.outputs.version == 'true'
    runs-on: ubuntu-22.04
    steps:
      - name: Create release
        uses: ncipollo/release-action@v1
        with:
          name: API v${{ needs.changes.outputs.get-version }}
          tag: v${{ needs.changes.outputs.get-version }}
          commit: main
          generateReleaseNotes: true