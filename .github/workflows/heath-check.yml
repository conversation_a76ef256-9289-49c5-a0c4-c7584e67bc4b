name: Health check

# TODO: Delete this workflow and all associated files.
on: workflow_call

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}

permissions:
  contents: read

jobs:
  changes:
    name: Repository changes
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: '20.x'
          registry-url: 'https://registry.npmjs.org'
          cache: npm
          cache-dependency-path: tests/package-lock.json

      - name: Install dependencies
        working-directory: tests
        run: |
          npm install
          npm install -g @e2b/cli

      - name: Test build
        working-directory: tests
        run: make test
        env:
          E2B_API_KEY: ${{ secrets.E2B_API_KEY }}
          E2B_ACCESS_TOKEN: ${{ secrets.E2B_ACCESS_TOKEN }}

      - name: Release Failed - Slack Notification
        uses: rtCamp/action-slack-notify@v2
        if: failure()
        env:
          SLACK_COLOR: "#ff0000"
          SLACK_MESSAGE: ":here-we-go-again: :bob-the-destroyer: We need :fix-parrot: ASAP :pray:"
          SLACK_TITLE: "Health check failed"
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
