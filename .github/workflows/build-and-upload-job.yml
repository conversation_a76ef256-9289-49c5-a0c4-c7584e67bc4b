name: Build And Upload Job

on:
  workflow_dispatch:
    inputs:
      commit_sha:
        description: 'Specific commit SHA to checkout'
        required: true
        type: string
      tracking_id:
        description: 'Unique tracking ID used for identifying the workflow run'
        required: false
        type: string
      environment:
        description: 'Target environment for deployment, e.g. staging'
        required: true
        type: string
      job_names:
        description: 'Name of the jobs to build-and-upload, e.g. api, template-manager, separated by ;'
        required: true
        type: string

concurrency:
  group: deploy-${{ inputs.environment }}
  cancel-in-progress: false

jobs:
  deploy:
    name: Build and upload job to the ${{ inputs.environment }} environment
    runs-on: ubuntu-22.04
    permissions:
      contents: read
      id-token: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.commit_sha }}

      - name: Setup environment
        uses: ./.github/actions/deploy-setup
        with:
          environment: ${{ inputs.environment }}
          env_vars: ${{ secrets[format('env_{0}', inputs.environment)] }}

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version-file: 'go.work'
          cache: false

      - name: Go Mod Cache
        uses: actions/cache@v4
        with:
          path: |
            ~/go/pkg/mod
          key: ${{ runner.os }}-go-mod-${{ hashFiles('**/go.sum', '**/go.mod', 'go.work') }}
          restore-keys: |
            ${{ runner.os }}-go-mod-

      - name: Build and upload jobs
        env:
          AUTO_CONFIRM_DEPLOY: true
        run: |
          # Parse semicolon-separated job names
          IFS=';' read -ra JOBS <<< "${{ inputs.job_names }}"
          
          # Build and upload each job
          for job_name in "${JOBS[@]}"; do
            # Trim whitespace
            job_name=$(echo "$job_name" | xargs)
          
            if [ -n "$job_name" ]; then
              echo "::group::Building and uploading job: $job_name"
              make build-and-upload/$job_name
              echo "::endgroup::"
            fi
          done
        
