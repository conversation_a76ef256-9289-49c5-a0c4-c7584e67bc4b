name: Deploy Job
on:
  workflow_dispatch:
    inputs:
      commit_sha:
        description: 'Specific commit SHA to checkout'
        required: true
        type: string
      tracking_id:
        description: 'Unique tracking ID used for identifying the workflow run'
        required: false
        type: string
      environment:
        description: 'Target environment for deployment, e.g. staging'
        required: true
        type: string
      job_names:
        description: 'Name of the jobs to deploy, e.g. api, template-manager, separated by ;'
        required: true
        type: string
      plan_only:
        description: 'Only plan the changes without applying them'
        required: false
        type: string
        default: "false"

concurrency:
  group: deploy-${{ inputs.environment }}
  cancel-in-progress: false

jobs:
  deploy:
    name: Deploy job to the ${{ inputs.environment }} environment
    runs-on: ubuntu-22.04
    permissions:
      contents: read
      id-token: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.commit_sha }}

      - name: Setup environment
        uses: ./.github/actions/deploy-setup
        with:
          environment: ${{ inputs.environment }}
          env_vars: ${{ secrets[format('env_{0}', inputs.environment)] }}

      - name: Deploy jobs
        env:
          AUTO_CONFIRM_DEPLOY: true
        run: |
          # Parse semicolon-separated job names
          IFS=';' read -ra JOBS <<< "${{ inputs.job_names }}"
          
          # Deploy each job
          for job_name in "${JOBS[@]}"; do
            # Trim whitespace
            job_name=$(echo "$job_name" | xargs)
          
            if [ -n "$job_name" ]; then
              echo "::group::Deploying job: $job_name"
              make plan-only-jobs/$job_name

              # Apply only if plan_only is not true
              if [ "${{ inputs.plan_only }}" == "false" ]; then
                make apply
              else
                echo "Skipping apply, plan_only is true"
              fi
              echo "::endgroup::"
            fi
          done
        
