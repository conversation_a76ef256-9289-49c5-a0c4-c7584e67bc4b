name: API

on:
  workflow_call:
    secrets:
      service_account_email:
        required: true
      workload_identity_provider:
        required: true
      gcp_project_id:
        required: true
      gcp_region:
        required: true
env:
  IMAGE_TAG:  ${{ secrets.gcp_region }}-docker.pkg.dev/${{ secrets.gcp_project_id }}/e2b-orchestration/api:latest

jobs:
  publish:
    name: Build & push
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Setup Service Account
        uses: google-github-actions/auth@v1
        with:
          workload_identity_provider: ${{ secrets.workload_identity_provider }}
          service_account: ${{ secrets.service_account_email }}

      - name: Configure Docker
        run: gcloud --quiet auth configure-docker ${{ secrets.gcp_region }}-docker.pkg.dev

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Build and Push
        uses: docker/build-push-action@v3
        with:
          push: true
          file: "api.Dockerfile"
          tags: ${{ env.IMAGE_TAG }}
          cache-from: type=gha,scope=api
          cache-to: type=gha,scope=api
