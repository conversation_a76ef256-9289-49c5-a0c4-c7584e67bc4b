# Development

### Remote development setup (VSCode)


1. Run `gcloud compute config-ssh`
2. Install "Remote SSH" extension
3. Go to the "Tunnels/SSH" tab in "Remote Explorer" extension
4. Click on config button in the SSH section
5. Select the (usually first) option: `/Users/<USER>/.ssh/config`
6. Refresh the list of SSHs (you may need to reload VSCode)
7. Click on the "Connect in new window" next to the orch-client instance. This will open the remote VSCode
8. Pull this repo from GitHub via the Source control VSCode tab
9. *You may need to install `sudo apt install make` and `sudo snap install go --classic` until we add this to the default instance image*
10. The Go debugger should be attachable to the remote instance too
11. Configure git identifiers

- `git config --global user.email "<EMAIL>"`
- `git config --global user.name "NAME"`
