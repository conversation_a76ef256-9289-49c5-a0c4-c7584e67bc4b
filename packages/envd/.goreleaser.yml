version: 2
project_name: envd
before:
  hooks:
    - go mod tidy

builds:
  - id: envd
    binary: envd
    ldflags:
      - -X main.Version={{ .Version }}
    env:
      - CGO_ENABLED=0
    targets:
      - linux_amd64

archives:
  - id: latest
    builds:
      - envd
    wrap_in_directory: false
    format: tar.gz
    name_template: >-
      envd_
      {{- title .Os }}_
      {{- if eq .Arch "amd64" }}x86_64
      {{- else }}{{ .Arch }}{{ end }}{{ if .Arm }}v{{ .Arm }}{{ end }}{{ if .Mips }}_{{ .Mips }}{{ end }}

changelog:
  use: github-native
  disable: true

release:
  disable: true
  # If set to auto, will mark the release as not ready for production
  # in case there is an indicator for this in the tag e.g. v1.0.0-rc1
  # If set to true, will mark the release as not ready for production.
  prerelease: auto

snapshot:
  name_template: "{{ .Env.GORELEASER_CURRENT_TAG }}+{{ .ShortCommit }}"

checksum:
  name_template: checksums.txt
