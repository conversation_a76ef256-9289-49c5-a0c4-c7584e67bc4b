// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: get_snapshots_with_cursor.sql

package queries

import (
	"context"

	"github.com/e2b-dev/infra/packages/db/types"
	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgtype"
)

const getSnapshotsWithCursor = `-- name: GetSnapshotsWithCursor :many
SELECT COALESCE(ea.aliases, ARRAY[]::text[])::text[] AS aliases, s.created_at, s.env_id, s.sandbox_id, s.id, s.metadata, s.base_env_id, s.sandbox_started_at, s.env_secure, eb.id, eb.created_at, eb.updated_at, eb.finished_at, eb.status, eb.dockerfile, eb.start_cmd, eb.vcpu, eb.ram_mb, eb.free_disk_size_mb, eb.total_disk_size_mb, eb.kernel_version, eb.firecracker_version, eb.env_id, eb.envd_version, eb.ready_cmd
FROM "public"."snapshots" s
JOIN "public"."envs" e ON e.id = s.env_id
LEFT JOIN LATERAL (
    SELECT ARRAY_AGG(alias ORDER BY alias) AS aliases
    FROM "public"."env_aliases"
    WHERE env_id = s.base_env_id
) ea ON TRUE
JOIN LATERAL (
    SELECT eb.id, eb.created_at, eb.updated_at, eb.finished_at, eb.status, eb.dockerfile, eb.start_cmd, eb.vcpu, eb.ram_mb, eb.free_disk_size_mb, eb.total_disk_size_mb, eb.kernel_version, eb.firecracker_version, eb.env_id, eb.envd_version, eb.ready_cmd
    FROM "public"."env_builds" eb
    WHERE
        eb.env_id = s.env_id
        AND eb.status = 'success'
    ORDER BY eb.created_at DESC
    LIMIT 1
) eb ON TRUE
WHERE
    e.team_id = $2
    AND s.metadata @> $3
    AND (
        s.created_at < $4
        OR
        (s.created_at = $4 AND s.sandbox_id > $5)
    )
    AND NOT (s.sandbox_id = ANY ($6::text[]))
ORDER BY s.created_at DESC, s.sandbox_id
LIMIT $1
`

type GetSnapshotsWithCursorParams struct {
	Limit                 int32
	TeamID                uuid.UUID
	Metadata              types.JSONBStringMap
	CursorTime            pgtype.Timestamptz
	CursorID              string
	SnapshotExcludeSbxIds []string
}

type GetSnapshotsWithCursorRow struct {
	Aliases  []string
	Snapshot Snapshot
	EnvBuild EnvBuild
}

func (q *Queries) GetSnapshotsWithCursor(ctx context.Context, arg GetSnapshotsWithCursorParams) ([]GetSnapshotsWithCursorRow, error) {
	rows, err := q.db.Query(ctx, getSnapshotsWithCursor,
		arg.Limit,
		arg.TeamID,
		arg.Metadata,
		arg.CursorTime,
		arg.CursorID,
		arg.SnapshotExcludeSbxIds,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetSnapshotsWithCursorRow
	for rows.Next() {
		var i GetSnapshotsWithCursorRow
		if err := rows.Scan(
			&i.Aliases,
			&i.Snapshot.CreatedAt,
			&i.Snapshot.EnvID,
			&i.Snapshot.SandboxID,
			&i.Snapshot.ID,
			&i.Snapshot.Metadata,
			&i.Snapshot.BaseEnvID,
			&i.Snapshot.SandboxStartedAt,
			&i.Snapshot.EnvSecure,
			&i.EnvBuild.ID,
			&i.EnvBuild.CreatedAt,
			&i.EnvBuild.UpdatedAt,
			&i.EnvBuild.FinishedAt,
			&i.EnvBuild.Status,
			&i.EnvBuild.Dockerfile,
			&i.EnvBuild.StartCmd,
			&i.EnvBuild.Vcpu,
			&i.EnvBuild.RamMb,
			&i.EnvBuild.FreeDiskSizeMb,
			&i.EnvBuild.TotalDiskSizeMb,
			&i.EnvBuild.KernelVersion,
			&i.EnvBuild.FirecrackerVersion,
			&i.EnvBuild.EnvID,
			&i.EnvBuild.EnvdVersion,
			&i.EnvBuild.ReadyCmd,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
