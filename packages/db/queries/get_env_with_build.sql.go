// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: get_env_with_build.sql

package queries

import (
	"context"
)

const getEnvWithBuild = `-- name: GetEnvWithBuild :one
WITH s AS NOT MATERIALIZED (
    SELECT ea.env_id as env_id
    FROM public.env_aliases as ea
    WHERE ea.alias = $1
    UNION
    SELECT $1 as env_id
)

SELECT e.id, e.created_at, e.updated_at, e.public, e.build_count, e.spawn_count, e.last_spawned_at, e.team_id, e.created_by, eb.id, eb.created_at, eb.updated_at, eb.finished_at, eb.status, eb.dockerfile, eb.start_cmd, eb.vcpu, eb.ram_mb, eb.free_disk_size_mb, eb.total_disk_size_mb, eb.kernel_version, eb.firecracker_version, eb.env_id, eb.envd_version, eb.ready_cmd, aliases
FROM s
JOIN public.envs AS e ON e.id = s.env_id
JOIN public.env_builds AS eb ON eb.env_id = e.id
AND eb.status = 'uploaded'
CROSS JOIN LATERAL (
    SELECT array_agg(alias)::text[] AS aliases
    FROM public.env_aliases
    WHERE env_id = e.id
) AS al
ORDER BY eb.finished_at DESC
LIMIT 1
`

type GetEnvWithBuildRow struct {
	Env      Env
	EnvBuild EnvBuild
	Aliases  []string
}

// get the env_id when querying by alias; if not, @alias_or_env_id should be env_id
func (q *Queries) GetEnvWithBuild(ctx context.Context, aliasOrEnvID string) (GetEnvWithBuildRow, error) {
	row := q.db.QueryRow(ctx, getEnvWithBuild, aliasOrEnvID)
	var i GetEnvWithBuildRow
	err := row.Scan(
		&i.Env.ID,
		&i.Env.CreatedAt,
		&i.Env.UpdatedAt,
		&i.Env.Public,
		&i.Env.BuildCount,
		&i.Env.SpawnCount,
		&i.Env.LastSpawnedAt,
		&i.Env.TeamID,
		&i.Env.CreatedBy,
		&i.EnvBuild.ID,
		&i.EnvBuild.CreatedAt,
		&i.EnvBuild.UpdatedAt,
		&i.EnvBuild.FinishedAt,
		&i.EnvBuild.Status,
		&i.EnvBuild.Dockerfile,
		&i.EnvBuild.StartCmd,
		&i.EnvBuild.Vcpu,
		&i.EnvBuild.RamMb,
		&i.EnvBuild.FreeDiskSizeMb,
		&i.EnvBuild.TotalDiskSizeMb,
		&i.EnvBuild.KernelVersion,
		&i.EnvBuild.FirecrackerVersion,
		&i.EnvBuild.EnvID,
		&i.EnvBuild.EnvdVersion,
		&i.EnvBuild.ReadyCmd,
		&i.Aliases,
	)
	return i, err
}
