// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: teams___tiers__usersteams.sql

package queries

import (
	"context"

	"github.com/google/uuid"
)

const getTeamsWithUsersTeamsWithTier = `-- name: GetTeamsWithUsersTeamsWithTier :many
SELECT t.id, t.created_at, t.is_blocked, t.name, t.tier, t.email, t.is_banned, t.blocked_reason, ut.id, ut.user_id, ut.team_id, ut.is_default, ut.added_by, ut.created_at, tier.id, tier.name, tier.disk_mb, tier.concurrent_instances, tier.max_length_hours, tier.max_vcpu, tier.max_ram_mb
FROM "public"."teams" t
JOIN "public"."tiers" tier ON t.tier = tier.id
JOIN "public"."users_teams" ut ON ut.team_id = t.id
WHERE ut.user_id = $1
`

type GetTeamsWithUsersTeamsWithTierRow struct {
	Team      Team
	UsersTeam UsersTeam
	Tier      Tier
}

func (q *Queries) GetTeamsWithUsersTeamsWithTier(ctx context.Context, userID uuid.UUID) ([]GetTeamsWithUsersTeamsWithTierRow, error) {
	rows, err := q.db.Query(ctx, getTeamsWithUsersTeamsWithTier, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetTeamsWithUsersTeamsWithTierRow
	for rows.Next() {
		var i GetTeamsWithUsersTeamsWithTierRow
		if err := rows.Scan(
			&i.Team.ID,
			&i.Team.CreatedAt,
			&i.Team.IsBlocked,
			&i.Team.Name,
			&i.Team.Tier,
			&i.Team.Email,
			&i.Team.IsBanned,
			&i.Team.BlockedReason,
			&i.UsersTeam.ID,
			&i.UsersTeam.UserID,
			&i.UsersTeam.TeamID,
			&i.UsersTeam.IsDefault,
			&i.UsersTeam.AddedBy,
			&i.UsersTeam.CreatedAt,
			&i.Tier.ID,
			&i.Tier.Name,
			&i.Tier.DiskMb,
			&i.Tier.ConcurrentInstances,
			&i.Tier.MaxLengthHours,
			&i.Tier.MaxVcpu,
			&i.Tier.MaxRamMb,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
