// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: get_last_snapshot.sql

package queries

import (
	"context"

	"github.com/google/uuid"
)

const getLastSnapshot = `-- name: GetLastSnapshot :one
SELECT COALESCE(ea.aliases, ARRAY[]::text[])::text[] AS aliases, s.created_at, s.env_id, s.sandbox_id, s.id, s.metadata, s.base_env_id, s.sandbox_started_at, s.env_secure, eb.id, eb.created_at, eb.updated_at, eb.finished_at, eb.status, eb.dockerfile, eb.start_cmd, eb.vcpu, eb.ram_mb, eb.free_disk_size_mb, eb.total_disk_size_mb, eb.kernel_version, eb.firecracker_version, eb.env_id, eb.envd_version, eb.ready_cmd
FROM "public"."snapshots" s
JOIN "public"."envs" e ON s.env_id  = e.id
JOIN "public"."env_builds" eb ON e.id = eb.env_id
LEFT JOIN LATERAL (
    SELECT ARRAY_AGG(alias ORDER BY alias) AS aliases
    FROM "public"."env_aliases"
    WHERE env_id = s.base_env_id
) ea ON TRUE
WHERE s.sandbox_id = $1 AND eb.status = 'success' AND e.team_id = $2
ORDER BY eb.finished_at DESC
LIMIT 1
`

type GetLastSnapshotParams struct {
	SandboxID string
	TeamID    uuid.UUID
}

type GetLastSnapshotRow struct {
	Aliases  []string
	Snapshot Snapshot
	EnvBuild EnvBuild
}

func (q *Queries) GetLastSnapshot(ctx context.Context, arg GetLastSnapshotParams) (GetLastSnapshotRow, error) {
	row := q.db.QueryRow(ctx, getLastSnapshot, arg.SandboxID, arg.TeamID)
	var i GetLastSnapshotRow
	err := row.Scan(
		&i.Aliases,
		&i.Snapshot.CreatedAt,
		&i.Snapshot.EnvID,
		&i.Snapshot.SandboxID,
		&i.Snapshot.ID,
		&i.Snapshot.Metadata,
		&i.Snapshot.BaseEnvID,
		&i.Snapshot.SandboxStartedAt,
		&i.Snapshot.EnvSecure,
		&i.EnvBuild.ID,
		&i.EnvBuild.CreatedAt,
		&i.EnvBuild.UpdatedAt,
		&i.EnvBuild.FinishedAt,
		&i.EnvBuild.Status,
		&i.EnvBuild.Dockerfile,
		&i.EnvBuild.StartCmd,
		&i.EnvBuild.Vcpu,
		&i.EnvBuild.RamMb,
		&i.EnvBuild.FreeDiskSizeMb,
		&i.EnvBuild.TotalDiskSizeMb,
		&i.EnvBuild.KernelVersion,
		&i.EnvBuild.FirecrackerVersion,
		&i.EnvBuild.EnvID,
		&i.EnvBuild.EnvdVersion,
		&i.EnvBuild.ReadyCmd,
	)
	return i, err
}
