version: "2"
sql:
  - engine: "postgresql"
    queries: "queries"
    schema: "migrations"
    gen:
      go:
        emit_pointers_for_null_types: true
        package: "queries"
        out: "queries/"
        sql_package: "pgx/v5"
        overrides:
          - db_type: "uuid"
            go_type:
              import: "github.com/google/uuid"
              type: "UUID"
          - db_type: "uuid"
            nullable: true
            go_type:
              import: "github.com/google/uuid"
              type: "UUID"
              pointer: true

          - db_type: "pg_catalog.numeric"
            go_type: "github.com/shopspring/decimal.Decimal"
          - db_type: "pg_catalog.numeric"
            nullable: true
            go_type: "*github.com/shopspring/decimal.Decimal"

          - db_type: "timestamptz"
            go_type: "time.Time"
          - db_type: "timestamptz"
            go_type:
              import: "time"
              type: "Time"
              pointer: true
            nullable: true

          - db_type: "jsonb"
            go_type: "github.com/e2b-dev/infra/packages/db/types.JSONBStringMap"
            nullable: true
