// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v5.27.3
// source: collector.proto

package analyticscollector

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AnalyticsCollectorClient is the client API for AnalyticsCollector service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AnalyticsCollectorClient interface {
	InstanceStarted(ctx context.Context, in *InstanceStartedEvent, opts ...grpc.CallOption) (*emptypb.Empty, error)
	RunningInstances(ctx context.Context, in *RunningInstancesEvent, opts ...grpc.CallOption) (*emptypb.Empty, error)
	InstanceStopped(ctx context.Context, in *InstanceStoppedEvent, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type analyticsCollectorClient struct {
	cc grpc.ClientConnInterface
}

func NewAnalyticsCollectorClient(cc grpc.ClientConnInterface) AnalyticsCollectorClient {
	return &analyticsCollectorClient{cc}
}

func (c *analyticsCollectorClient) InstanceStarted(ctx context.Context, in *InstanceStartedEvent, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/AnalyticsCollector/InstanceStarted", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsCollectorClient) RunningInstances(ctx context.Context, in *RunningInstancesEvent, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/AnalyticsCollector/RunningInstances", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsCollectorClient) InstanceStopped(ctx context.Context, in *InstanceStoppedEvent, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/AnalyticsCollector/InstanceStopped", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AnalyticsCollectorServer is the server API for AnalyticsCollector service.
// All implementations must embed UnimplementedAnalyticsCollectorServer
// for forward compatibility
type AnalyticsCollectorServer interface {
	InstanceStarted(context.Context, *InstanceStartedEvent) (*emptypb.Empty, error)
	RunningInstances(context.Context, *RunningInstancesEvent) (*emptypb.Empty, error)
	InstanceStopped(context.Context, *InstanceStoppedEvent) (*emptypb.Empty, error)
	mustEmbedUnimplementedAnalyticsCollectorServer()
}

// UnimplementedAnalyticsCollectorServer must be embedded to have forward compatible implementations.
type UnimplementedAnalyticsCollectorServer struct {
}

func (UnimplementedAnalyticsCollectorServer) InstanceStarted(context.Context, *InstanceStartedEvent) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InstanceStarted not implemented")
}
func (UnimplementedAnalyticsCollectorServer) RunningInstances(context.Context, *RunningInstancesEvent) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RunningInstances not implemented")
}
func (UnimplementedAnalyticsCollectorServer) InstanceStopped(context.Context, *InstanceStoppedEvent) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InstanceStopped not implemented")
}
func (UnimplementedAnalyticsCollectorServer) mustEmbedUnimplementedAnalyticsCollectorServer() {}

// UnsafeAnalyticsCollectorServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AnalyticsCollectorServer will
// result in compilation errors.
type UnsafeAnalyticsCollectorServer interface {
	mustEmbedUnimplementedAnalyticsCollectorServer()
}

func RegisterAnalyticsCollectorServer(s grpc.ServiceRegistrar, srv AnalyticsCollectorServer) {
	s.RegisterService(&AnalyticsCollector_ServiceDesc, srv)
}

func _AnalyticsCollector_InstanceStarted_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InstanceStartedEvent)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsCollectorServer).InstanceStarted(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/AnalyticsCollector/InstanceStarted",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsCollectorServer).InstanceStarted(ctx, req.(*InstanceStartedEvent))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsCollector_RunningInstances_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RunningInstancesEvent)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsCollectorServer).RunningInstances(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/AnalyticsCollector/RunningInstances",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsCollectorServer).RunningInstances(ctx, req.(*RunningInstancesEvent))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsCollector_InstanceStopped_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InstanceStoppedEvent)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsCollectorServer).InstanceStopped(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/AnalyticsCollector/InstanceStopped",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsCollectorServer).InstanceStopped(ctx, req.(*InstanceStoppedEvent))
	}
	return interceptor(ctx, in, info, handler)
}

// AnalyticsCollector_ServiceDesc is the grpc.ServiceDesc for AnalyticsCollector service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AnalyticsCollector_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "AnalyticsCollector",
	HandlerType: (*AnalyticsCollectorServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "InstanceStarted",
			Handler:    _AnalyticsCollector_InstanceStarted_Handler,
		},
		{
			MethodName: "RunningInstances",
			Handler:    _AnalyticsCollector_RunningInstances_Handler,
		},
		{
			MethodName: "InstanceStopped",
			Handler:    _AnalyticsCollector_InstanceStopped_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "collector.proto",
}
