// Package api provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package api

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"fmt"
	"net/url"
	"path"
	"strings"

	"github.com/getkin/kin-openapi/openapi3"
)

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+xd3W/bOpb/VwjtPuwCbuzmdi52AsxDmvbOFrftBE3aWaA3WNDSccyJROqSVBIj8P8+",
	"4JdESdSHHcdJ2jy1kSjy8Jwfzwd5Dn0XxSzLGQUqRXR0F+WY4wwkcP0XjmMQ4pxdAf3wTj0gNDqKciyX",
	"0SSiOIPoqNFmEnH4syAckuhI8gImkYiXkGH1sVzl6gMhOaGX0Xo9iXBOfodVd9fu9Wa9zguSJp2dureb",
	"9UlZAp1d2peb9SgwTebstrPT6v1m/UrI8hTLbmq9Bpv0vFaNRc6oAI2NN7OZ+idmVAKVGi15npIYS8Lo",
	"9F+CUfWs6u8/OSyio+g/phXgpuatmL7nnHEzRgIi5iRXnURH0VucIEUiCBmtJ9Gb2euHH/O4kEug0vaK",
	"wLRTg795+ME/M4kWrKCJGfGvDz/iCaOLlMSav3/Zh0zPgF8Dd3xdO8xpUJ2cfj1hhRm6QebpVxQzDgIt",
	"GEdyCcgukGgSLRjPsIyOIkLlL4fRJMoIJVmRRUevJw7HhEq4BC3IEw5YQnJcKS6t+TjLgUti0B3bNgFK",
	"zkkGQuIsR2yBjPZDUvWC9EeqkUdSgiW8kiSDaNJcUpOIJO3uPyQKfAsCXPWvJuqP4XddFCQJ9ZphcTUk",
	"nWqUT1hcEXr5DiQmqVDfGz3RpOszzqCDohYF0jG1wbkloEWRpitk2TvQ0drXT98jPVtNnBvBznXiieui",
	"EvA54Oz49MPvsNpevsenH9AVrDYXrR3grR4bp+k/FtHR936ZKHq/CoXRi0lEizTF8xSMYh6NFUvvGJhc",
	"Gb7Ue/yCb9A1Tgtod9jqIMVCfhUQoOsjFhIpziC5JKJk4g0WqFAfdDCxPudHQXbndENYNA0tBC0w60h8",
	"T6+/YetLJQlRA+L0tIbEOi3v6TXhjGZAJbrGnCh2hFRemzqjb9tAZ0lgyrox0u8C6rOtMjMQAl92dTTI",
	"LTuQ60VxplNMrRko/kLyTYHylMOC3LapMM81thChyHyBroELZcGtaI2OZrwLzt44Z8UiOI55fs9x8v5J",
	"yCWWiDjuiFaXSHcY6Fcv249AL+UysCL1834SS3k3pGcJro8wCcglxEMl649ESEjOLHhbAsYpwYGlcKwe",
	"lxRbpzWoalMCVBp/d0g/mrbBXvKidD369EvpoqwnEdBOC4JulkD9RYtuSJoiuM0Jh9FWJIOM8dWnt0NE",
	"fXLt9DcSJ1gOOmhWHp9c82ZMMsTKTmU0iYTEvMe6BniDBbIfjeaNkAoP4yZ5ptu2oqOhKbrWaMFZhm6W",
	"JF4iImqUW30/qAJrUZcf25Xo9dnmwdEDgQOcm7taW588hNSnY940bYfSW7qrHqf58H9CNuAz3PS6zPd1",
	"GxsM091dmHG7VUch2SkuhB14gYtURkcLnAoIhHUswyqsUw5orj6qSxIvJBhWKcSxwtMSc8ZSwNQs+dKi",
	"90Y/ttk91yPEBQ8w9Uw/RzhNkVgJCRmKWZYV1MWsN0Qu2+vTm8Vmy8CJpVcLO6b5gnj9l0lo+UuGUnIN",
	"IWgKiBlNxEEvQGeDJsubn4VQXzCwO7ewAq11u5qWLmWxUhcnp18DAxbZ3DC9bIfKsHecn1Z+aNUCCeiF",
	"40zplfowRsVo3UDejhtKIY6EAnX93DGO8XgJQnIsQ26ic5d/c55fF0Pq2hYtdHs/xiFU/vomSGe1czcE",
	"c2q81LahMYN3bEy0iASBeEEpoZeIUb/jEUwVpbniktDL4SFtQ3Tmxm6MEx5FYlkMqjAF4TPTUnmXxslt",
	"E/Ot7v32C7y5XNzGqaWowetJfcEE4V2HUAcHK/JL3LpFasKOQNyE4yUkbwuSJgFkKo9Wzdi0QnpPWSCS",
	"NCROJGQisJla8gVzjlc/5WqCHq4OLaSSrX3wrQcdAZY//VWgV3MNiE20d8L6rJxcw3PQzxsyAqqs6veI",
	"A06UjUs4JortultKIZbmj4IuAadyuYouAoKthj1ZYnoZMH+bc7zBKduBmuQXEEXWF1Tu3TN8ZAdI8cSs",
	"FsuTfxK5/ASSk1i8xNxPN+bOKhGN0mtVF5zEQb32nIL4HyIeV/roie9uAb1uHng16PEPsvQGp1JO6rN6",
	"bNnV+beRllH36Mw6oYMg3BGYnzTOfP55WOpyTV90d6c6ea4wf9nnfdnn7d/ntRP8yC4DcRO7REAlX5mN",
	"P1keYGOaoJRQxda6CtEPg/2oN8glgHRs8+nOBw7OFe9SR9dIWTdZWQ41MQTX+RDwalP7tDUt0QbzJs6W",
	"4nrL02pQq8f2KPzkLc9xZ7/ui8GFVxtEeYGhrjiJNwSFr9G7tg423I+M8+KrgOQ07sjoKQS+BJQDj4FK",
	"fFlT9IuUYQ+CVNNgleU5kzgN7m7qN737mR07GxlkitRgp/YoxZ37ju5zk8WSeSK7/3rx1Iong9os64z0",
	"kHvmFG576wDa2Cw3D4yxU6jCOskjtEdwDjgLODQ6zzNgrG3eiAuTpfo6lEYl3rmAu9nFP5cgl1B97tS6",
	"jdAbXXrR/PCZQBc16vlYsxPqoWVQdHdlgolllj/rC8vZl2SnzmSnnz5XyaInmC9XyqKFHMhsBNLI+lGP",
	"HRmF+nLr5Eb79YAAQzMytBn6bbATDpWgK1iCULg0fs9e7/oPWkytletepsKW+liOM6JePvsQNxXMkSh0",
	"kLMoUnMwofXnJblWvnhfWLhFQDegWaoAoDb3ypd+JPWi2HSW4xu6MemawX16YSehXV7M05BbV7dmliwi",
	"kGmPGEeMpiubZ0HmKaD5KmBpPDMnFBe2xXCTDz2O0FbhWIidRZ5sgTgjNvPpls6VH9dVNSTh8M3Kz18f",
	"PuU+optgrImkpmN8TaePg9rqbgNNoZsGLWUZQ1mX6vtFqypDaxXdcBN9KUadSXnCd66lptX4ljeY2MMo",
	"d1hl6ggudrbnty0SysO7MhCsCeuLrWLZ/W7eFmo7YfEV8AVJAx7Gu/Kd53d3D7+NetOiO8kC/sEX9QbF",
	"S4iv9H6cilUlQ3ALcaFUXWNpVwdznXDWey3BsXSKwI5G2bHP78nHB9JXrUE6kbQvq6GoNaRskVQFN0i9",
	"KbG1YWaVS4ojcnWmkGXG8rZ8jwuT/jwHzIH/5hS9mdz/u9RDjUo9Kd2sGn0pZa4EepxkhNY61JV0S8CJ",
	"bm5r6f7vlW746rye0mhjNNWP/t9QH6cfXv3u86D6/qzI8RwLeD2GFte4mxzX4lBLbmxvNRi4zpQoCF0w",
	"rfqJVKoken/4VgnUS0w4imYHrw9mamyWA8U5iY6iXw5mBzO9USCXWn5TI55XWjwGy0yEtoxMwgpGFG6a",
	"2aQKezpq/ZBER9EpE9JDhbCljiDkW5asdlbk1siJXddRa73NWtnk4Q5LGAOFbKF6xlaJGiRejJCuvMrK",
	"0Ggl+VPVqKoS7G+rGvmrVXvsITR/v1AuusTK6/ge1YGg13sdHNO7WsHx2oAkhdCO1Tv9HGHajxXTzEfL",
	"caOm2a+K7gg8qibTekW0ml0DAW8GzqXMfO4nJFutOtT2zaMINCevrmCluXEJsiMRDKep2bKzJkK0BPd3",
	"kEa/muVd4/FmhayjDgM8a9c+DGiXuXrCQxxkwSkkgUk98uIL2oSGCJ24lC8yQjH78wsrZk9oD6KTfUk9",
	"ikpuEtAIVD0GPUmNvBko/CU9vXOXNozSzP1YsYrZoOW4ugxiQ3XsPhyniWvCee6aeOPVjWUcqB403v6Q",
	"uE7VxzuW1u7VQytyGaUhZgNAsZs7PwlQ1Io3qcCdJvx/9WsTyocMt3kfjWG0DXhN8kjJ3824q4U8pSyB",
	"EV6HaRYg+rN9sRtfY9xOtq4kWl/cy+MwE9qbUWkGzw0cqbcWRJqw6Z3JgF93SubvIPUckA44uwTz2eXR",
	"b6ZxbPp9wDrs7jIUr9JktODKBP0nqUbGybjTX9QVAkiUO7/Y1SK0vcWdyfYBXM1mycO6fU9S2MmwsnUc",
	"0KdiuovnYELGr+9asU+/0nXJkn7tS2ud+2VuDSR05FL9WYDL3JEMLUjq9nKrIqP/goPLA/RHVAjgf8Pz",
	"+I9iNjv8Fef533LOkj+i/z5A73G81HYe08TcjyJQVgiJ5oC+fvmIgMYsgeQgmpjdND1qtZlWpmr2XbB1",
	"sV+70qiPup+BaQtPg3E2BoyzPRombzf2+8V6cg9vqJrpiKjYJZHq9NLGiUpb4fkgf6AAuRT7fqPj2rBt",
	"jehnCXeHxT8JqGrqc+pVCW2oRk36p/u+T6d+Ktu8qNZ7qdbuOrxdq9m6cJ/D8hiF9rsycb53F+l3kqaV",
	"du3YPirhfeYl42/mRVZp/CO3kBq67Iqk6fNw7B7KPnZGdZVtnK+QTuvr1k8PJMDZrs3bNoGeqOrUfxpY",
	"dK75qctB6oSNA43NQRqBmY+m5da4mQTTR5QyloGyF2GucRNLVqSJsmSlsAlFGUlTYuu8O6yazlqpmbRW",
	"kl3/bTgto41vVWtEy5y+Pio7qEpJRupUVYXus9ls04r1PSxFLfVtFqJB1stqVKtxyPf0F+QYP7Nck50O",
	"5/7U+S5K7reBV81l++kRlruLOsJxtL7Ho1F21BM2l/gy93/s29sz5U81b0/HNjGmRsfqi0seUvL23vCh",
	"tn99ZijhsOAgltCTIPbFNKktNbiVQBNdhy2FrfQx96+MhNGXctz7Qmm7vZx6XmVSGIIDh+r2jc6tNEXM",
	"Ph8qM38FuURY30BT3Tiji4NujbX+5VdlzQfu27OP2PxfEMvRO98N1Wg4uycf+AkgWK39Pviq91toOvPh",
	"I+Gzdyukfk3T091vtGp5b0H6D6qjvUuwwhA/A+nfpNW8AusAnYevAEG3TlF5u+ikKoa04D1AJzhNdVi2",
	"JEK5WUuWoKxIJclTsIVM7Br4DSfS1jSdn3+cIMCxKZJHhTCfA4oLzoFKv3beXgrhYr+cEfWeoQywKDjU",
	"puY09cHIRXxeXi72+FamdplZs8hKTa4yHJU8fH7ZqolOM9S+2GWba14tlRc7sUbCQtNR6nr/2Xx0CTgb",
	"mZsbDPTO7Yt9brnrGoV77q6bCe1vY7xZtNInxtoBu3rmRGXOEUeJyzUNiqx62VA+oY2g8k4Cfydoq7qn",
	"i33DxJ673hsqjl9PHy4VraPzt3sOp32kPITDGCyaHOU2Hu6chi6/0VTVK68RxzHk0kXzT+7obReQqamZ",
	"6V1V/To2wbsDTKZFCadzv6p2M//HK8gdv2NTKw/fRZr346/s3tzt7kWtPnsQMTyccqgXwm6dwN26z6Az",
	"ifuHXNmTzl0Io+AwHWkKngdonqNF+QGsxNRcjj+9s1clrHv2BnRFvl9oPwp05m70t+VNDNsjcDLY2t33",
	"EDA0h2ENY0S79C6+/GElO61u+Og8KCwVruFLV0L/kJjP3G0bexF26zj9A03gtrwLz+0Gzd29KJ2n/+Yq",
	"xsaFU6GTdnYp/rFYCOg4bn9SZ+31S2k2Og4t2fA091h2tX6uDzfJhO/NgP92+CPnwLcW2m+G2IrQ+Qox",
	"CohxlDFu6ic0J+A2T/VvLtmfeOjIqtErbrvj/+pu4da9Rit9+4VakQFdcVJwwbjivCjtW6pkveAs62AW",
	"hVt57t9VMo5b7SwfPUE1tll2KAeOcnPR6o4yfNyZoXnf98vLLxUOzzcZXY/Dr52iKXhqL8sRR9MpzskB",
	"HM4PcJ5HXg93zV+cF3p11H/fvv5Qb236f9duj/BfuGLU9cX63wEAAP//EslEScSAAAA=",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
