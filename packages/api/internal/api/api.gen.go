// Package api provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package api

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/oapi-codegen/runtime"
)

// ServerInterface represents all server handlers.
type ServerInterface interface {

	// (POST /access-tokens)
	PostAccessTokens(c *gin.Context)

	// (DELETE /access-tokens/{accessTokenID})
	DeleteAccessTokensAccessTokenID(c *gin.Context, accessTokenID AccessTokenID)

	// (GET /api-keys)
	GetApiKeys(c *gin.Context)

	// (POST /api-keys)
	PostApiKeys(c *gin.Context)

	// (DELETE /api-keys/{apiKeyID})
	DeleteApiKeysApiKeyID(c *gin.Context, apiKeyID ApiKeyID)

	// (PATCH /api-keys/{apiKeyID})
	PatchApiKeysApiKeyID(c *gin.Context, apiKeyID ApiKeyID)

	// (GET /health)
	GetHealth(c *gin.Context)

	// (GET /nodes)
	GetNodes(c *gin.Context)

	// (GET /nodes/{nodeID})
	GetNodesNodeID(c *gin.Context, nodeID NodeID)

	// (POST /nodes/{nodeID})
	PostNodesNodeID(c *gin.Context, nodeID NodeID)

	// (GET /sandboxes)
	GetSandboxes(c *gin.Context, params GetSandboxesParams)

	// (POST /sandboxes)
	PostSandboxes(c *gin.Context)

	// (GET /sandboxes/metrics)
	GetSandboxesMetrics(c *gin.Context, params GetSandboxesMetricsParams)

	// (DELETE /sandboxes/{sandboxID})
	DeleteSandboxesSandboxID(c *gin.Context, sandboxID SandboxID)

	// (GET /sandboxes/{sandboxID})
	GetSandboxesSandboxID(c *gin.Context, sandboxID SandboxID)

	// (GET /sandboxes/{sandboxID}/logs)
	GetSandboxesSandboxIDLogs(c *gin.Context, sandboxID SandboxID, params GetSandboxesSandboxIDLogsParams)

	// (GET /sandboxes/{sandboxID}/metrics)
	GetSandboxesSandboxIDMetrics(c *gin.Context, sandboxID SandboxID)

	// (POST /sandboxes/{sandboxID}/pause)
	PostSandboxesSandboxIDPause(c *gin.Context, sandboxID SandboxID)

	// (POST /sandboxes/{sandboxID}/refreshes)
	PostSandboxesSandboxIDRefreshes(c *gin.Context, sandboxID SandboxID)

	// (POST /sandboxes/{sandboxID}/resume)
	PostSandboxesSandboxIDResume(c *gin.Context, sandboxID SandboxID)

	// (POST /sandboxes/{sandboxID}/timeout)
	PostSandboxesSandboxIDTimeout(c *gin.Context, sandboxID SandboxID)

	// (GET /teams)
	GetTeams(c *gin.Context)

	// (GET /templates)
	GetTemplates(c *gin.Context, params GetTemplatesParams)

	// (POST /templates)
	PostTemplates(c *gin.Context)

	// (DELETE /templates/{templateID})
	DeleteTemplatesTemplateID(c *gin.Context, templateID TemplateID)

	// (PATCH /templates/{templateID})
	PatchTemplatesTemplateID(c *gin.Context, templateID TemplateID)

	// (POST /templates/{templateID})
	PostTemplatesTemplateID(c *gin.Context, templateID TemplateID)

	// (POST /templates/{templateID}/builds/{buildID})
	PostTemplatesTemplateIDBuildsBuildID(c *gin.Context, templateID TemplateID, buildID BuildID)

	// (GET /templates/{templateID}/builds/{buildID}/status)
	GetTemplatesTemplateIDBuildsBuildIDStatus(c *gin.Context, templateID TemplateID, buildID BuildID, params GetTemplatesTemplateIDBuildsBuildIDStatusParams)

	// (GET /v2/sandboxes)
	GetV2Sandboxes(c *gin.Context, params GetV2SandboxesParams)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandler       func(*gin.Context, error, int)
}

type MiddlewareFunc func(c *gin.Context)

// PostAccessTokens operation middleware
func (siw *ServerInterfaceWrapper) PostAccessTokens(c *gin.Context) {

	c.Set(Supabase1TokenAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostAccessTokens(c)
}

// DeleteAccessTokensAccessTokenID operation middleware
func (siw *ServerInterfaceWrapper) DeleteAccessTokensAccessTokenID(c *gin.Context) {

	var err error

	// ------------- Path parameter "accessTokenID" -------------
	var accessTokenID AccessTokenID

	err = runtime.BindStyledParameterWithOptions("simple", "accessTokenID", c.Param("accessTokenID"), &accessTokenID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter accessTokenID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(Supabase1TokenAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteAccessTokensAccessTokenID(c, accessTokenID)
}

// GetApiKeys operation middleware
func (siw *ServerInterfaceWrapper) GetApiKeys(c *gin.Context) {

	c.Set(Supabase1TokenAuthScopes, []string{})

	c.Set(Supabase2TeamAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetApiKeys(c)
}

// PostApiKeys operation middleware
func (siw *ServerInterfaceWrapper) PostApiKeys(c *gin.Context) {

	c.Set(Supabase1TokenAuthScopes, []string{})

	c.Set(Supabase2TeamAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostApiKeys(c)
}

// DeleteApiKeysApiKeyID operation middleware
func (siw *ServerInterfaceWrapper) DeleteApiKeysApiKeyID(c *gin.Context) {

	var err error

	// ------------- Path parameter "apiKeyID" -------------
	var apiKeyID ApiKeyID

	err = runtime.BindStyledParameterWithOptions("simple", "apiKeyID", c.Param("apiKeyID"), &apiKeyID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter apiKeyID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(Supabase1TokenAuthScopes, []string{})

	c.Set(Supabase2TeamAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteApiKeysApiKeyID(c, apiKeyID)
}

// PatchApiKeysApiKeyID operation middleware
func (siw *ServerInterfaceWrapper) PatchApiKeysApiKeyID(c *gin.Context) {

	var err error

	// ------------- Path parameter "apiKeyID" -------------
	var apiKeyID ApiKeyID

	err = runtime.BindStyledParameterWithOptions("simple", "apiKeyID", c.Param("apiKeyID"), &apiKeyID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter apiKeyID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(Supabase1TokenAuthScopes, []string{})

	c.Set(Supabase2TeamAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PatchApiKeysApiKeyID(c, apiKeyID)
}

// GetHealth operation middleware
func (siw *ServerInterfaceWrapper) GetHealth(c *gin.Context) {

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetHealth(c)
}

// GetNodes operation middleware
func (siw *ServerInterfaceWrapper) GetNodes(c *gin.Context) {

	c.Set(AdminTokenAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetNodes(c)
}

// GetNodesNodeID operation middleware
func (siw *ServerInterfaceWrapper) GetNodesNodeID(c *gin.Context) {

	var err error

	// ------------- Path parameter "nodeID" -------------
	var nodeID NodeID

	err = runtime.BindStyledParameterWithOptions("simple", "nodeID", c.Param("nodeID"), &nodeID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter nodeID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AdminTokenAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetNodesNodeID(c, nodeID)
}

// PostNodesNodeID operation middleware
func (siw *ServerInterfaceWrapper) PostNodesNodeID(c *gin.Context) {

	var err error

	// ------------- Path parameter "nodeID" -------------
	var nodeID NodeID

	err = runtime.BindStyledParameterWithOptions("simple", "nodeID", c.Param("nodeID"), &nodeID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter nodeID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AdminTokenAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostNodesNodeID(c, nodeID)
}

// GetSandboxes operation middleware
func (siw *ServerInterfaceWrapper) GetSandboxes(c *gin.Context) {

	var err error

	c.Set(ApiKeyAuthScopes, []string{})

	c.Set(Supabase1TokenAuthScopes, []string{})

	c.Set(Supabase2TeamAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetSandboxesParams

	// ------------- Optional query parameter "metadata" -------------

	err = runtime.BindQueryParameter("form", true, false, "metadata", c.Request.URL.Query(), &params.Metadata)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter metadata: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetSandboxes(c, params)
}

// PostSandboxes operation middleware
func (siw *ServerInterfaceWrapper) PostSandboxes(c *gin.Context) {

	c.Set(ApiKeyAuthScopes, []string{})

	c.Set(Supabase1TokenAuthScopes, []string{})

	c.Set(Supabase2TeamAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostSandboxes(c)
}

// GetSandboxesMetrics operation middleware
func (siw *ServerInterfaceWrapper) GetSandboxesMetrics(c *gin.Context) {

	var err error

	c.Set(ApiKeyAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetSandboxesMetricsParams

	// ------------- Optional query parameter "metadata" -------------

	err = runtime.BindQueryParameter("form", true, false, "metadata", c.Request.URL.Query(), &params.Metadata)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter metadata: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetSandboxesMetrics(c, params)
}

// DeleteSandboxesSandboxID operation middleware
func (siw *ServerInterfaceWrapper) DeleteSandboxesSandboxID(c *gin.Context) {

	var err error

	// ------------- Path parameter "sandboxID" -------------
	var sandboxID SandboxID

	err = runtime.BindStyledParameterWithOptions("simple", "sandboxID", c.Param("sandboxID"), &sandboxID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter sandboxID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(ApiKeyAuthScopes, []string{})

	c.Set(Supabase1TokenAuthScopes, []string{})

	c.Set(Supabase2TeamAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteSandboxesSandboxID(c, sandboxID)
}

// GetSandboxesSandboxID operation middleware
func (siw *ServerInterfaceWrapper) GetSandboxesSandboxID(c *gin.Context) {

	var err error

	// ------------- Path parameter "sandboxID" -------------
	var sandboxID SandboxID

	err = runtime.BindStyledParameterWithOptions("simple", "sandboxID", c.Param("sandboxID"), &sandboxID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter sandboxID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(ApiKeyAuthScopes, []string{})

	c.Set(Supabase1TokenAuthScopes, []string{})

	c.Set(Supabase2TeamAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetSandboxesSandboxID(c, sandboxID)
}

// GetSandboxesSandboxIDLogs operation middleware
func (siw *ServerInterfaceWrapper) GetSandboxesSandboxIDLogs(c *gin.Context) {

	var err error

	// ------------- Path parameter "sandboxID" -------------
	var sandboxID SandboxID

	err = runtime.BindStyledParameterWithOptions("simple", "sandboxID", c.Param("sandboxID"), &sandboxID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter sandboxID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(ApiKeyAuthScopes, []string{})

	c.Set(Supabase1TokenAuthScopes, []string{})

	c.Set(Supabase2TeamAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetSandboxesSandboxIDLogsParams

	// ------------- Optional query parameter "start" -------------

	err = runtime.BindQueryParameter("form", true, false, "start", c.Request.URL.Query(), &params.Start)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter start: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", c.Request.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter limit: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetSandboxesSandboxIDLogs(c, sandboxID, params)
}

// GetSandboxesSandboxIDMetrics operation middleware
func (siw *ServerInterfaceWrapper) GetSandboxesSandboxIDMetrics(c *gin.Context) {

	var err error

	// ------------- Path parameter "sandboxID" -------------
	var sandboxID SandboxID

	err = runtime.BindStyledParameterWithOptions("simple", "sandboxID", c.Param("sandboxID"), &sandboxID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter sandboxID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(ApiKeyAuthScopes, []string{})

	c.Set(Supabase1TokenAuthScopes, []string{})

	c.Set(Supabase2TeamAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetSandboxesSandboxIDMetrics(c, sandboxID)
}

// PostSandboxesSandboxIDPause operation middleware
func (siw *ServerInterfaceWrapper) PostSandboxesSandboxIDPause(c *gin.Context) {

	var err error

	// ------------- Path parameter "sandboxID" -------------
	var sandboxID SandboxID

	err = runtime.BindStyledParameterWithOptions("simple", "sandboxID", c.Param("sandboxID"), &sandboxID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter sandboxID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(ApiKeyAuthScopes, []string{})

	c.Set(Supabase1TokenAuthScopes, []string{})

	c.Set(Supabase2TeamAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostSandboxesSandboxIDPause(c, sandboxID)
}

// PostSandboxesSandboxIDRefreshes operation middleware
func (siw *ServerInterfaceWrapper) PostSandboxesSandboxIDRefreshes(c *gin.Context) {

	var err error

	// ------------- Path parameter "sandboxID" -------------
	var sandboxID SandboxID

	err = runtime.BindStyledParameterWithOptions("simple", "sandboxID", c.Param("sandboxID"), &sandboxID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter sandboxID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(ApiKeyAuthScopes, []string{})

	c.Set(Supabase1TokenAuthScopes, []string{})

	c.Set(Supabase2TeamAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostSandboxesSandboxIDRefreshes(c, sandboxID)
}

// PostSandboxesSandboxIDResume operation middleware
func (siw *ServerInterfaceWrapper) PostSandboxesSandboxIDResume(c *gin.Context) {

	var err error

	// ------------- Path parameter "sandboxID" -------------
	var sandboxID SandboxID

	err = runtime.BindStyledParameterWithOptions("simple", "sandboxID", c.Param("sandboxID"), &sandboxID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter sandboxID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(ApiKeyAuthScopes, []string{})

	c.Set(Supabase1TokenAuthScopes, []string{})

	c.Set(Supabase2TeamAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostSandboxesSandboxIDResume(c, sandboxID)
}

// PostSandboxesSandboxIDTimeout operation middleware
func (siw *ServerInterfaceWrapper) PostSandboxesSandboxIDTimeout(c *gin.Context) {

	var err error

	// ------------- Path parameter "sandboxID" -------------
	var sandboxID SandboxID

	err = runtime.BindStyledParameterWithOptions("simple", "sandboxID", c.Param("sandboxID"), &sandboxID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter sandboxID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(ApiKeyAuthScopes, []string{})

	c.Set(Supabase1TokenAuthScopes, []string{})

	c.Set(Supabase2TeamAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostSandboxesSandboxIDTimeout(c, sandboxID)
}

// GetTeams operation middleware
func (siw *ServerInterfaceWrapper) GetTeams(c *gin.Context) {

	c.Set(AccessTokenAuthScopes, []string{})

	c.Set(Supabase1TokenAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetTeams(c)
}

// GetTemplates operation middleware
func (siw *ServerInterfaceWrapper) GetTemplates(c *gin.Context) {

	var err error

	c.Set(AccessTokenAuthScopes, []string{})

	c.Set(Supabase1TokenAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetTemplatesParams

	// ------------- Optional query parameter "teamID" -------------

	err = runtime.BindQueryParameter("form", true, false, "teamID", c.Request.URL.Query(), &params.TeamID)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter teamID: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetTemplates(c, params)
}

// PostTemplates operation middleware
func (siw *ServerInterfaceWrapper) PostTemplates(c *gin.Context) {

	c.Set(AccessTokenAuthScopes, []string{})

	c.Set(Supabase1TokenAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTemplates(c)
}

// DeleteTemplatesTemplateID operation middleware
func (siw *ServerInterfaceWrapper) DeleteTemplatesTemplateID(c *gin.Context) {

	var err error

	// ------------- Path parameter "templateID" -------------
	var templateID TemplateID

	err = runtime.BindStyledParameterWithOptions("simple", "templateID", c.Param("templateID"), &templateID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter templateID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AccessTokenAuthScopes, []string{})

	c.Set(Supabase1TokenAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteTemplatesTemplateID(c, templateID)
}

// PatchTemplatesTemplateID operation middleware
func (siw *ServerInterfaceWrapper) PatchTemplatesTemplateID(c *gin.Context) {

	var err error

	// ------------- Path parameter "templateID" -------------
	var templateID TemplateID

	err = runtime.BindStyledParameterWithOptions("simple", "templateID", c.Param("templateID"), &templateID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter templateID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AccessTokenAuthScopes, []string{})

	c.Set(Supabase1TokenAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PatchTemplatesTemplateID(c, templateID)
}

// PostTemplatesTemplateID operation middleware
func (siw *ServerInterfaceWrapper) PostTemplatesTemplateID(c *gin.Context) {

	var err error

	// ------------- Path parameter "templateID" -------------
	var templateID TemplateID

	err = runtime.BindStyledParameterWithOptions("simple", "templateID", c.Param("templateID"), &templateID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter templateID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AccessTokenAuthScopes, []string{})

	c.Set(Supabase1TokenAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTemplatesTemplateID(c, templateID)
}

// PostTemplatesTemplateIDBuildsBuildID operation middleware
func (siw *ServerInterfaceWrapper) PostTemplatesTemplateIDBuildsBuildID(c *gin.Context) {

	var err error

	// ------------- Path parameter "templateID" -------------
	var templateID TemplateID

	err = runtime.BindStyledParameterWithOptions("simple", "templateID", c.Param("templateID"), &templateID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter templateID: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "buildID" -------------
	var buildID BuildID

	err = runtime.BindStyledParameterWithOptions("simple", "buildID", c.Param("buildID"), &buildID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter buildID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AccessTokenAuthScopes, []string{})

	c.Set(Supabase1TokenAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostTemplatesTemplateIDBuildsBuildID(c, templateID, buildID)
}

// GetTemplatesTemplateIDBuildsBuildIDStatus operation middleware
func (siw *ServerInterfaceWrapper) GetTemplatesTemplateIDBuildsBuildIDStatus(c *gin.Context) {

	var err error

	// ------------- Path parameter "templateID" -------------
	var templateID TemplateID

	err = runtime.BindStyledParameterWithOptions("simple", "templateID", c.Param("templateID"), &templateID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter templateID: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "buildID" -------------
	var buildID BuildID

	err = runtime.BindStyledParameterWithOptions("simple", "buildID", c.Param("buildID"), &buildID, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter buildID: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(AccessTokenAuthScopes, []string{})

	c.Set(Supabase1TokenAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetTemplatesTemplateIDBuildsBuildIDStatusParams

	// ------------- Optional query parameter "logsOffset" -------------

	err = runtime.BindQueryParameter("form", true, false, "logsOffset", c.Request.URL.Query(), &params.LogsOffset)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter logsOffset: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetTemplatesTemplateIDBuildsBuildIDStatus(c, templateID, buildID, params)
}

// GetV2Sandboxes operation middleware
func (siw *ServerInterfaceWrapper) GetV2Sandboxes(c *gin.Context) {

	var err error

	c.Set(ApiKeyAuthScopes, []string{})

	c.Set(Supabase1TokenAuthScopes, []string{})

	c.Set(Supabase2TeamAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetV2SandboxesParams

	// ------------- Optional query parameter "metadata" -------------

	err = runtime.BindQueryParameter("form", true, false, "metadata", c.Request.URL.Query(), &params.Metadata)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter metadata: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "state" -------------

	err = runtime.BindQueryParameter("form", false, false, "state", c.Request.URL.Query(), &params.State)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter state: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "nextToken" -------------

	err = runtime.BindQueryParameter("form", true, false, "nextToken", c.Request.URL.Query(), &params.NextToken)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter nextToken: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", c.Request.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter limit: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetV2Sandboxes(c, params)
}

// GinServerOptions provides options for the Gin server.
type GinServerOptions struct {
	BaseURL      string
	Middlewares  []MiddlewareFunc
	ErrorHandler func(*gin.Context, error, int)
}

// RegisterHandlers creates http.Handler with routing matching OpenAPI spec.
func RegisterHandlers(router gin.IRouter, si ServerInterface) {
	RegisterHandlersWithOptions(router, si, GinServerOptions{})
}

// RegisterHandlersWithOptions creates http.Handler with additional options
func RegisterHandlersWithOptions(router gin.IRouter, si ServerInterface, options GinServerOptions) {
	errorHandler := options.ErrorHandler
	if errorHandler == nil {
		errorHandler = func(c *gin.Context, err error, statusCode int) {
			c.JSON(statusCode, gin.H{"msg": err.Error()})
		}
	}

	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandler:       errorHandler,
	}

	router.POST(options.BaseURL+"/access-tokens", wrapper.PostAccessTokens)
	router.DELETE(options.BaseURL+"/access-tokens/:accessTokenID", wrapper.DeleteAccessTokensAccessTokenID)
	router.GET(options.BaseURL+"/api-keys", wrapper.GetApiKeys)
	router.POST(options.BaseURL+"/api-keys", wrapper.PostApiKeys)
	router.DELETE(options.BaseURL+"/api-keys/:apiKeyID", wrapper.DeleteApiKeysApiKeyID)
	router.PATCH(options.BaseURL+"/api-keys/:apiKeyID", wrapper.PatchApiKeysApiKeyID)
	router.GET(options.BaseURL+"/health", wrapper.GetHealth)
	router.GET(options.BaseURL+"/nodes", wrapper.GetNodes)
	router.GET(options.BaseURL+"/nodes/:nodeID", wrapper.GetNodesNodeID)
	router.POST(options.BaseURL+"/nodes/:nodeID", wrapper.PostNodesNodeID)
	router.GET(options.BaseURL+"/sandboxes", wrapper.GetSandboxes)
	router.POST(options.BaseURL+"/sandboxes", wrapper.PostSandboxes)
	router.GET(options.BaseURL+"/sandboxes/metrics", wrapper.GetSandboxesMetrics)
	router.DELETE(options.BaseURL+"/sandboxes/:sandboxID", wrapper.DeleteSandboxesSandboxID)
	router.GET(options.BaseURL+"/sandboxes/:sandboxID", wrapper.GetSandboxesSandboxID)
	router.GET(options.BaseURL+"/sandboxes/:sandboxID/logs", wrapper.GetSandboxesSandboxIDLogs)
	router.GET(options.BaseURL+"/sandboxes/:sandboxID/metrics", wrapper.GetSandboxesSandboxIDMetrics)
	router.POST(options.BaseURL+"/sandboxes/:sandboxID/pause", wrapper.PostSandboxesSandboxIDPause)
	router.POST(options.BaseURL+"/sandboxes/:sandboxID/refreshes", wrapper.PostSandboxesSandboxIDRefreshes)
	router.POST(options.BaseURL+"/sandboxes/:sandboxID/resume", wrapper.PostSandboxesSandboxIDResume)
	router.POST(options.BaseURL+"/sandboxes/:sandboxID/timeout", wrapper.PostSandboxesSandboxIDTimeout)
	router.GET(options.BaseURL+"/teams", wrapper.GetTeams)
	router.GET(options.BaseURL+"/templates", wrapper.GetTemplates)
	router.POST(options.BaseURL+"/templates", wrapper.PostTemplates)
	router.DELETE(options.BaseURL+"/templates/:templateID", wrapper.DeleteTemplatesTemplateID)
	router.PATCH(options.BaseURL+"/templates/:templateID", wrapper.PatchTemplatesTemplateID)
	router.POST(options.BaseURL+"/templates/:templateID", wrapper.PostTemplatesTemplateID)
	router.POST(options.BaseURL+"/templates/:templateID/builds/:buildID", wrapper.PostTemplatesTemplateIDBuildsBuildID)
	router.GET(options.BaseURL+"/templates/:templateID/builds/:buildID/status", wrapper.GetTemplatesTemplateIDBuildsBuildIDStatus)
	router.GET(options.BaseURL+"/v2/sandboxes", wrapper.GetV2Sandboxes)
}
