root = "."
tmp_dir = ".air"

[build]
cmd = "CGO_ENABLED=1 go build -race -gcflags=all=\"-N -l\" -o bin/api ."
bin = "bin/api"
full_bin = "ENV=$(cat ../../.last_used_env || echo \"not-set\") && . ../../.env.${ENV} && POSTGRES_CONNECTION_STRING=${POSTGRES_CONNECTION_STRING} SUPABASE_JWT_SECRETS=${SUPABASE_JWT_SECRETS} GOTRACEBACK=crash GODEBUG=madvdontneed=1 TEMPLATE_BUCKET_NAME=${TEMPLATE_BUCKET_NAME} ENVIRONMENT=${ENVIRONMENT} ORCHESTRATOR_PORT=5008 bin/api --port 3000"
include_ext = ["go", "yml", "yaml"]
exclude_dir = ["assets", ".air", "vendor", "bin"]
exclude_regex = ["_test\\.go"]
exclude_unchanged = true
delay = 500
send_interrupt = true # not sure this is actually needed

[log]
time = true

[screen]
clear_on_rebuild = false
