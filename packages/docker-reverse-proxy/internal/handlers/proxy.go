package handlers

import (
	"fmt"
	"log"
	"net/http"
	"strings"

	"github.com/e2b-dev/infra/packages/docker-reverse-proxy/internal/constants"
	"github.com/e2b-dev/infra/packages/docker-reverse-proxy/internal/utils"
	"github.com/e2b-dev/infra/packages/shared/pkg/consts"
)

func (a *APIStore) Proxy(w http.ResponseWriter, req *http.Request) {
	// Check if the request is for the correct repository
	path := req.URL.String()

	// Validate the token by checking if the generated token is in the cache
	authHeader := req.Header.Get("Authorization")
	e2bToken := strings.TrimPrefix(authHeader, "Bearer ")
	token, err := a.AuthCache.Get(e2bToken)
	if err != nil {
		log.Printf("Error while getting token for %s: %s, header: %s\n", path, err, authHeader)
		utils.SetDockerUnauthorizedHeaders(w)

		return
	}

	// Set the Authorization header for the request to the real docker registry
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token.DockerToken))

	// Allow access to the GCP artifact uploads.
	// The url is generated by repository and sent as a Location header from the /blobs/upload request
	// https://distribution.github.io/distribution/spec/api/#starting-an-upload
	// Other methods than PATCH require the Authorization header
	if strings.HasPrefix(path, constants.GCPArtifactUploadPrefix) {
		a.ServeHTTP(w, req)
		return
	}

	repoPrefix := "/v2/e2b/custom-envs/"
	realRepoPrefix := fmt.Sprintf("/v2/%s/%s/", consts.GCPProject, consts.DockerRegistry)
	if !strings.HasPrefix(path, repoPrefix) && !strings.HasPrefix(path, realRepoPrefix) {
		// The request shouldn't need any other endpoints, we deny access
		log.Printf("No matching route found for path: %s\n", path)

		w.WriteHeader(http.StatusForbidden)
		return
	}

	templateID := token.TemplateID

	// Uploading blobs doesn't have the template ID in the path
	if strings.HasPrefix(path, fmt.Sprintf("%spkg/blobs/uploads/", realRepoPrefix)) {
		a.ServeHTTP(w, req)

		return
	}

	pathInRepo := strings.TrimPrefix(path, repoPrefix)
	templateWithBuildID := strings.Split(strings.Split(pathInRepo, "/")[0], ":")

	// If the template ID in the path is different from the token template ID, deny access
	if templateWithBuildID[0] != templateID {
		w.WriteHeader(http.StatusForbidden)
		log.Printf("Access denied for template: %s\n", templateID)

		return
	}

	// Set the host and access token for the real docker registry
	req.URL.Path = strings.Replace(req.URL.Path, repoPrefix, realRepoPrefix, 1)

	a.ServeHTTP(w, req)
}
