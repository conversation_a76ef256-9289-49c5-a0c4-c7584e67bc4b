module github.com/e2b-dev/infra/packages/docker-reverse-proxy

go 1.24.3

replace github.com/e2b-dev/infra/packages/shared v0.0.0 => ../shared

require (
	github.com/e2b-dev/infra/packages/shared v0.0.0
	github.com/jellydator/ttlcache/v3 v3.3.1-0.20250207140243-aefc35918359
)

require (
	ariga.io/atlas v0.15.0 // indirect
	entgo.io/ent v0.12.5 // indirect
	github.com/agext/levenshtein v1.2.3 // indirect
	github.com/apparentlymart/go-textseg/v15 v15.0.0 // indirect
	github.com/dchest/uniuri v1.2.0 // indirect
	github.com/go-openapi/inflect v0.19.0 // indirect
	github.com/go-test/deep v1.0.8 // indirect
	github.com/google/go-cmp v0.7.0 // indirect
	github.com/google/uuid v1.6.0 // indirect
	github.com/hashicorp/hcl/v2 v2.19.1 // indirect
	github.com/kr/pretty v0.3.1 // indirect
	github.com/lib/pq v1.10.9 // indirect
	github.com/mattn/go-sqlite3 v1.14.22 // indirect
	github.com/mitchellh/go-wordwrap v1.0.1 // indirect
	github.com/rogpeppe/go-internal v1.13.1 // indirect
	github.com/zclconf/go-cty v1.14.1 // indirect
	golang.org/x/mod v0.24.0 // indirect
	golang.org/x/sync v0.14.0 // indirect
	golang.org/x/text v0.25.0 // indirect
)
