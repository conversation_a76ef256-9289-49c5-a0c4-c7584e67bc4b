syntax = "proto3";

import "google/protobuf/empty.proto";

option go_package = "https://github.com/e2b-dev/infra/template-manager";


message TemplateConfig {
  string templateID = 1;
  string buildID = 2;

  int32 memoryMB = 3;
  int32 vCpuCount = 4;
  int32 diskSizeMB = 5;

  string kernelVersion = 6;
  string firecrackerVersion = 7;
  string startCommand = 8;
  bool hugePages = 9;

  string readyCommand = 10;
}

message TemplateCreateRequest {
  TemplateConfig template = 1;
}

message TemplateStatusRequest {
  string templateID = 1;
  string buildID = 2;
}

// Data required for deleting a template.
message TemplateBuildDeleteRequest {
  string buildID = 1;
  string templateID = 2;
}

message TemplateBuildMetadata {
  int32 rootfsSizeKey = 1;
  string envdVersionKey = 2;
}

enum TemplateBuildState {
  Building = 0;
  Failed = 1;
  Completed = 2;
}

// Logs from template build
message TemplateBuildStatusResponse {
  TemplateBuildState status = 1;
  TemplateBuildMetadata metadata = 2;
}

enum HealthState {
  Healthy = 0;
  Draining = 1;
}

message HealthStatusResponse {
  HealthState status = 1;
}

// Interface exported by the server.
service TemplateService {
  // TemplateCreate is a gRPC service that creates a new template
  rpc TemplateCreate (TemplateCreateRequest) returns (google.protobuf.Empty);

  // TemplateStatus is a gRPC service that streams the status of a template build
  rpc TemplateBuildStatus (TemplateStatusRequest) returns (TemplateBuildStatusResponse);

  // TemplateBuildDelete is a gRPC service that deletes files associated with a template build
  rpc TemplateBuildDelete (TemplateBuildDeleteRequest) returns (google.protobuf.Empty);

  // todo (2025-05): this is deprecated, please use InfoService that is used for both orchestrator and template manager
  rpc HealthStatus (google.protobuf.Empty) returns (HealthStatusResponse);
}
