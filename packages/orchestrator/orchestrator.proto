syntax = "proto3";

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";

option go_package = "https://github.com/e2b-dev/infra/orchestrator";

message SandboxConfig {
  // Data required for creating a new sandbox.
  string template_id = 1;
  string build_id = 2;
  
  string kernel_version = 3;
  string firecracker_version = 4;
  
  bool huge_pages = 5;
  
  string sandbox_id = 6;
  map<string, string> env_vars = 7;
  
  // Metadata about the sandbox.
  map<string, string> metadata = 8;
  optional string alias = 9;
  string envd_version = 10;
  
  int64 vcpu = 11;
  int64 ram_mb = 12;

  string team_id = 13;
  // Maximum length of the sandbox in Hours.
  int64 max_sandbox_length = 14;

  int64 total_disk_size_mb = 15;

  bool snapshot = 16;
  string base_template_id = 17;

  optional bool auto_pause = 18;

  optional string envd_access_token = 19;
  string execution_id = 20;
}

message SandboxCreateRequest {
  SandboxConfig sandbox = 1;

  google.protobuf.Timestamp start_time = 2;
  google.protobuf.Timestamp end_time = 3;
}

message SandboxCreateResponse {
  string client_id = 1;
}

message SandboxUpdateRequest {
  string sandbox_id = 1;

  google.protobuf.Timestamp end_time = 2;
}

message SandboxDeleteRequest {
  string sandbox_id = 1;
}

message SandboxPauseRequest {
  string sandbox_id = 1;
  string template_id = 2;
  string build_id = 3;
}

message RunningSandbox {
  SandboxConfig config = 1;
  string client_id = 2;

  google.protobuf.Timestamp start_time = 3;
  google.protobuf.Timestamp end_time = 4;
}

message SandboxListResponse {
  repeated RunningSandbox sandboxes = 1;
}

message CachedBuildInfo {
  string build_id = 1;
  google.protobuf.Timestamp expiration_time = 2;
}

message SandboxListCachedBuildsResponse {
  repeated CachedBuildInfo builds = 1;
}

service SandboxService {
  rpc Create(SandboxCreateRequest) returns (SandboxCreateResponse);
  rpc Update(SandboxUpdateRequest) returns (google.protobuf.Empty);
  rpc List(google.protobuf.Empty) returns (SandboxListResponse);
  rpc Delete(SandboxDeleteRequest) returns (google.protobuf.Empty);
  rpc Pause(SandboxPauseRequest) returns (google.protobuf.Empty);

  rpc ListCachedBuilds(google.protobuf.Empty) returns (SandboxListCachedBuildsResponse);
}
