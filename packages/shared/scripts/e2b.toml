# This is a config for E2B sandbox template.
# You can use template ID (rki5dems9wqfm4r03t7g) or template name (base) to create a sandbox:

# Python SDK
# from e2b import Sandbox, AsyncSandbox
# sandbox = Sandbox("base") # Sync sandbox
# sandbox = await AsyncSandbox.create("base") # Async sandbox

# JS SDK
# import { Sandbox } from 'e2b'
# const sandbox = await Sandbox.create('base')

memory_mb = 512
dockerfile = "e2b.Dockerfile"
template_name = "base"
template_id = "rki5dems9wqfm4r03t7g"
