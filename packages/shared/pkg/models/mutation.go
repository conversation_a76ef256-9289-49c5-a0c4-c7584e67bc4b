// Code generated by ent, DO NOT EDIT.

package models

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/accesstoken"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/env"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/envalias"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/envbuild"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/predicate"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/snapshot"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/team"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/teamapikey"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/tier"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/user"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/usersteams"
	"github.com/google/uuid"
)

const (
	// Operation types.
	OpCreate    = ent.OpCreate
	OpDelete    = ent.OpDelete
	OpDeleteOne = ent.OpDeleteOne
	OpUpdate    = ent.OpUpdate
	OpUpdateOne = ent.OpUpdateOne

	// Node types.
	TypeAccessToken = "AccessToken"
	TypeEnv         = "Env"
	TypeEnvAlias    = "EnvAlias"
	TypeEnvBuild    = "EnvBuild"
	TypeSnapshot    = "Snapshot"
	TypeTeam        = "Team"
	TypeTeamAPIKey  = "TeamAPIKey"
	TypeTier        = "Tier"
	TypeUser        = "User"
	TypeUsersTeams  = "UsersTeams"
)

// AccessTokenMutation represents an operation that mutates the AccessToken nodes in the graph.
type AccessTokenMutation struct {
	config
	op                       Op
	typ                      string
	id                       *uuid.UUID
	access_token             *string
	access_token_hash        *string
	access_token_prefix      *string
	access_token_length      *int
	addaccess_token_length   *int
	access_token_mask_prefix *string
	access_token_mask_suffix *string
	name                     *string
	created_at               *time.Time
	clearedFields            map[string]struct{}
	user                     *uuid.UUID
	cleareduser              bool
	done                     bool
	oldValue                 func(context.Context) (*AccessToken, error)
	predicates               []predicate.AccessToken
}

var _ ent.Mutation = (*AccessTokenMutation)(nil)

// accesstokenOption allows management of the mutation configuration using functional options.
type accesstokenOption func(*AccessTokenMutation)

// newAccessTokenMutation creates new mutation for the AccessToken entity.
func newAccessTokenMutation(c config, op Op, opts ...accesstokenOption) *AccessTokenMutation {
	m := &AccessTokenMutation{
		config:        c,
		op:            op,
		typ:           TypeAccessToken,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withAccessTokenID sets the ID field of the mutation.
func withAccessTokenID(id uuid.UUID) accesstokenOption {
	return func(m *AccessTokenMutation) {
		var (
			err   error
			once  sync.Once
			value *AccessToken
		)
		m.oldValue = func(ctx context.Context) (*AccessToken, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().AccessToken.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withAccessToken sets the old AccessToken of the mutation.
func withAccessToken(node *AccessToken) accesstokenOption {
	return func(m *AccessTokenMutation) {
		m.oldValue = func(context.Context) (*AccessToken, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m AccessTokenMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m AccessTokenMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("models: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of AccessToken entities.
func (m *AccessTokenMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *AccessTokenMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *AccessTokenMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().AccessToken.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetAccessToken sets the "access_token" field.
func (m *AccessTokenMutation) SetAccessToken(s string) {
	m.access_token = &s
}

// AccessToken returns the value of the "access_token" field in the mutation.
func (m *AccessTokenMutation) AccessToken() (r string, exists bool) {
	v := m.access_token
	if v == nil {
		return
	}
	return *v, true
}

// OldAccessToken returns the old "access_token" field's value of the AccessToken entity.
// If the AccessToken object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AccessTokenMutation) OldAccessToken(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAccessToken is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAccessToken requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAccessToken: %w", err)
	}
	return oldValue.AccessToken, nil
}

// ResetAccessToken resets all changes to the "access_token" field.
func (m *AccessTokenMutation) ResetAccessToken() {
	m.access_token = nil
}

// SetAccessTokenHash sets the "access_token_hash" field.
func (m *AccessTokenMutation) SetAccessTokenHash(s string) {
	m.access_token_hash = &s
}

// AccessTokenHash returns the value of the "access_token_hash" field in the mutation.
func (m *AccessTokenMutation) AccessTokenHash() (r string, exists bool) {
	v := m.access_token_hash
	if v == nil {
		return
	}
	return *v, true
}

// OldAccessTokenHash returns the old "access_token_hash" field's value of the AccessToken entity.
// If the AccessToken object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AccessTokenMutation) OldAccessTokenHash(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAccessTokenHash is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAccessTokenHash requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAccessTokenHash: %w", err)
	}
	return oldValue.AccessTokenHash, nil
}

// ResetAccessTokenHash resets all changes to the "access_token_hash" field.
func (m *AccessTokenMutation) ResetAccessTokenHash() {
	m.access_token_hash = nil
}

// SetAccessTokenPrefix sets the "access_token_prefix" field.
func (m *AccessTokenMutation) SetAccessTokenPrefix(s string) {
	m.access_token_prefix = &s
}

// AccessTokenPrefix returns the value of the "access_token_prefix" field in the mutation.
func (m *AccessTokenMutation) AccessTokenPrefix() (r string, exists bool) {
	v := m.access_token_prefix
	if v == nil {
		return
	}
	return *v, true
}

// OldAccessTokenPrefix returns the old "access_token_prefix" field's value of the AccessToken entity.
// If the AccessToken object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AccessTokenMutation) OldAccessTokenPrefix(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAccessTokenPrefix is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAccessTokenPrefix requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAccessTokenPrefix: %w", err)
	}
	return oldValue.AccessTokenPrefix, nil
}

// ResetAccessTokenPrefix resets all changes to the "access_token_prefix" field.
func (m *AccessTokenMutation) ResetAccessTokenPrefix() {
	m.access_token_prefix = nil
}

// SetAccessTokenLength sets the "access_token_length" field.
func (m *AccessTokenMutation) SetAccessTokenLength(i int) {
	m.access_token_length = &i
	m.addaccess_token_length = nil
}

// AccessTokenLength returns the value of the "access_token_length" field in the mutation.
func (m *AccessTokenMutation) AccessTokenLength() (r int, exists bool) {
	v := m.access_token_length
	if v == nil {
		return
	}
	return *v, true
}

// OldAccessTokenLength returns the old "access_token_length" field's value of the AccessToken entity.
// If the AccessToken object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AccessTokenMutation) OldAccessTokenLength(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAccessTokenLength is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAccessTokenLength requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAccessTokenLength: %w", err)
	}
	return oldValue.AccessTokenLength, nil
}

// AddAccessTokenLength adds i to the "access_token_length" field.
func (m *AccessTokenMutation) AddAccessTokenLength(i int) {
	if m.addaccess_token_length != nil {
		*m.addaccess_token_length += i
	} else {
		m.addaccess_token_length = &i
	}
}

// AddedAccessTokenLength returns the value that was added to the "access_token_length" field in this mutation.
func (m *AccessTokenMutation) AddedAccessTokenLength() (r int, exists bool) {
	v := m.addaccess_token_length
	if v == nil {
		return
	}
	return *v, true
}

// ResetAccessTokenLength resets all changes to the "access_token_length" field.
func (m *AccessTokenMutation) ResetAccessTokenLength() {
	m.access_token_length = nil
	m.addaccess_token_length = nil
}

// SetAccessTokenMaskPrefix sets the "access_token_mask_prefix" field.
func (m *AccessTokenMutation) SetAccessTokenMaskPrefix(s string) {
	m.access_token_mask_prefix = &s
}

// AccessTokenMaskPrefix returns the value of the "access_token_mask_prefix" field in the mutation.
func (m *AccessTokenMutation) AccessTokenMaskPrefix() (r string, exists bool) {
	v := m.access_token_mask_prefix
	if v == nil {
		return
	}
	return *v, true
}

// OldAccessTokenMaskPrefix returns the old "access_token_mask_prefix" field's value of the AccessToken entity.
// If the AccessToken object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AccessTokenMutation) OldAccessTokenMaskPrefix(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAccessTokenMaskPrefix is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAccessTokenMaskPrefix requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAccessTokenMaskPrefix: %w", err)
	}
	return oldValue.AccessTokenMaskPrefix, nil
}

// ResetAccessTokenMaskPrefix resets all changes to the "access_token_mask_prefix" field.
func (m *AccessTokenMutation) ResetAccessTokenMaskPrefix() {
	m.access_token_mask_prefix = nil
}

// SetAccessTokenMaskSuffix sets the "access_token_mask_suffix" field.
func (m *AccessTokenMutation) SetAccessTokenMaskSuffix(s string) {
	m.access_token_mask_suffix = &s
}

// AccessTokenMaskSuffix returns the value of the "access_token_mask_suffix" field in the mutation.
func (m *AccessTokenMutation) AccessTokenMaskSuffix() (r string, exists bool) {
	v := m.access_token_mask_suffix
	if v == nil {
		return
	}
	return *v, true
}

// OldAccessTokenMaskSuffix returns the old "access_token_mask_suffix" field's value of the AccessToken entity.
// If the AccessToken object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AccessTokenMutation) OldAccessTokenMaskSuffix(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAccessTokenMaskSuffix is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAccessTokenMaskSuffix requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAccessTokenMaskSuffix: %w", err)
	}
	return oldValue.AccessTokenMaskSuffix, nil
}

// ResetAccessTokenMaskSuffix resets all changes to the "access_token_mask_suffix" field.
func (m *AccessTokenMutation) ResetAccessTokenMaskSuffix() {
	m.access_token_mask_suffix = nil
}

// SetName sets the "name" field.
func (m *AccessTokenMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *AccessTokenMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the AccessToken entity.
// If the AccessToken object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AccessTokenMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *AccessTokenMutation) ResetName() {
	m.name = nil
}

// SetUserID sets the "user_id" field.
func (m *AccessTokenMutation) SetUserID(u uuid.UUID) {
	m.user = &u
}

// UserID returns the value of the "user_id" field in the mutation.
func (m *AccessTokenMutation) UserID() (r uuid.UUID, exists bool) {
	v := m.user
	if v == nil {
		return
	}
	return *v, true
}

// OldUserID returns the old "user_id" field's value of the AccessToken entity.
// If the AccessToken object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AccessTokenMutation) OldUserID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUserID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUserID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUserID: %w", err)
	}
	return oldValue.UserID, nil
}

// ResetUserID resets all changes to the "user_id" field.
func (m *AccessTokenMutation) ResetUserID() {
	m.user = nil
}

// SetCreatedAt sets the "created_at" field.
func (m *AccessTokenMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *AccessTokenMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the AccessToken entity.
// If the AccessToken object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AccessTokenMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ClearCreatedAt clears the value of the "created_at" field.
func (m *AccessTokenMutation) ClearCreatedAt() {
	m.created_at = nil
	m.clearedFields[accesstoken.FieldCreatedAt] = struct{}{}
}

// CreatedAtCleared returns if the "created_at" field was cleared in this mutation.
func (m *AccessTokenMutation) CreatedAtCleared() bool {
	_, ok := m.clearedFields[accesstoken.FieldCreatedAt]
	return ok
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *AccessTokenMutation) ResetCreatedAt() {
	m.created_at = nil
	delete(m.clearedFields, accesstoken.FieldCreatedAt)
}

// ClearUser clears the "user" edge to the User entity.
func (m *AccessTokenMutation) ClearUser() {
	m.cleareduser = true
	m.clearedFields[accesstoken.FieldUserID] = struct{}{}
}

// UserCleared reports if the "user" edge to the User entity was cleared.
func (m *AccessTokenMutation) UserCleared() bool {
	return m.cleareduser
}

// UserIDs returns the "user" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// UserID instead. It exists only for internal usage by the builders.
func (m *AccessTokenMutation) UserIDs() (ids []uuid.UUID) {
	if id := m.user; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetUser resets all changes to the "user" edge.
func (m *AccessTokenMutation) ResetUser() {
	m.user = nil
	m.cleareduser = false
}

// Where appends a list predicates to the AccessTokenMutation builder.
func (m *AccessTokenMutation) Where(ps ...predicate.AccessToken) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the AccessTokenMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *AccessTokenMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.AccessToken, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *AccessTokenMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *AccessTokenMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (AccessToken).
func (m *AccessTokenMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *AccessTokenMutation) Fields() []string {
	fields := make([]string, 0, 9)
	if m.access_token != nil {
		fields = append(fields, accesstoken.FieldAccessToken)
	}
	if m.access_token_hash != nil {
		fields = append(fields, accesstoken.FieldAccessTokenHash)
	}
	if m.access_token_prefix != nil {
		fields = append(fields, accesstoken.FieldAccessTokenPrefix)
	}
	if m.access_token_length != nil {
		fields = append(fields, accesstoken.FieldAccessTokenLength)
	}
	if m.access_token_mask_prefix != nil {
		fields = append(fields, accesstoken.FieldAccessTokenMaskPrefix)
	}
	if m.access_token_mask_suffix != nil {
		fields = append(fields, accesstoken.FieldAccessTokenMaskSuffix)
	}
	if m.name != nil {
		fields = append(fields, accesstoken.FieldName)
	}
	if m.user != nil {
		fields = append(fields, accesstoken.FieldUserID)
	}
	if m.created_at != nil {
		fields = append(fields, accesstoken.FieldCreatedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *AccessTokenMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case accesstoken.FieldAccessToken:
		return m.AccessToken()
	case accesstoken.FieldAccessTokenHash:
		return m.AccessTokenHash()
	case accesstoken.FieldAccessTokenPrefix:
		return m.AccessTokenPrefix()
	case accesstoken.FieldAccessTokenLength:
		return m.AccessTokenLength()
	case accesstoken.FieldAccessTokenMaskPrefix:
		return m.AccessTokenMaskPrefix()
	case accesstoken.FieldAccessTokenMaskSuffix:
		return m.AccessTokenMaskSuffix()
	case accesstoken.FieldName:
		return m.Name()
	case accesstoken.FieldUserID:
		return m.UserID()
	case accesstoken.FieldCreatedAt:
		return m.CreatedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *AccessTokenMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case accesstoken.FieldAccessToken:
		return m.OldAccessToken(ctx)
	case accesstoken.FieldAccessTokenHash:
		return m.OldAccessTokenHash(ctx)
	case accesstoken.FieldAccessTokenPrefix:
		return m.OldAccessTokenPrefix(ctx)
	case accesstoken.FieldAccessTokenLength:
		return m.OldAccessTokenLength(ctx)
	case accesstoken.FieldAccessTokenMaskPrefix:
		return m.OldAccessTokenMaskPrefix(ctx)
	case accesstoken.FieldAccessTokenMaskSuffix:
		return m.OldAccessTokenMaskSuffix(ctx)
	case accesstoken.FieldName:
		return m.OldName(ctx)
	case accesstoken.FieldUserID:
		return m.OldUserID(ctx)
	case accesstoken.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	}
	return nil, fmt.Errorf("unknown AccessToken field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *AccessTokenMutation) SetField(name string, value ent.Value) error {
	switch name {
	case accesstoken.FieldAccessToken:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAccessToken(v)
		return nil
	case accesstoken.FieldAccessTokenHash:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAccessTokenHash(v)
		return nil
	case accesstoken.FieldAccessTokenPrefix:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAccessTokenPrefix(v)
		return nil
	case accesstoken.FieldAccessTokenLength:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAccessTokenLength(v)
		return nil
	case accesstoken.FieldAccessTokenMaskPrefix:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAccessTokenMaskPrefix(v)
		return nil
	case accesstoken.FieldAccessTokenMaskSuffix:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAccessTokenMaskSuffix(v)
		return nil
	case accesstoken.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case accesstoken.FieldUserID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUserID(v)
		return nil
	case accesstoken.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	}
	return fmt.Errorf("unknown AccessToken field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *AccessTokenMutation) AddedFields() []string {
	var fields []string
	if m.addaccess_token_length != nil {
		fields = append(fields, accesstoken.FieldAccessTokenLength)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *AccessTokenMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case accesstoken.FieldAccessTokenLength:
		return m.AddedAccessTokenLength()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *AccessTokenMutation) AddField(name string, value ent.Value) error {
	switch name {
	case accesstoken.FieldAccessTokenLength:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddAccessTokenLength(v)
		return nil
	}
	return fmt.Errorf("unknown AccessToken numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *AccessTokenMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(accesstoken.FieldCreatedAt) {
		fields = append(fields, accesstoken.FieldCreatedAt)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *AccessTokenMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *AccessTokenMutation) ClearField(name string) error {
	switch name {
	case accesstoken.FieldCreatedAt:
		m.ClearCreatedAt()
		return nil
	}
	return fmt.Errorf("unknown AccessToken nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *AccessTokenMutation) ResetField(name string) error {
	switch name {
	case accesstoken.FieldAccessToken:
		m.ResetAccessToken()
		return nil
	case accesstoken.FieldAccessTokenHash:
		m.ResetAccessTokenHash()
		return nil
	case accesstoken.FieldAccessTokenPrefix:
		m.ResetAccessTokenPrefix()
		return nil
	case accesstoken.FieldAccessTokenLength:
		m.ResetAccessTokenLength()
		return nil
	case accesstoken.FieldAccessTokenMaskPrefix:
		m.ResetAccessTokenMaskPrefix()
		return nil
	case accesstoken.FieldAccessTokenMaskSuffix:
		m.ResetAccessTokenMaskSuffix()
		return nil
	case accesstoken.FieldName:
		m.ResetName()
		return nil
	case accesstoken.FieldUserID:
		m.ResetUserID()
		return nil
	case accesstoken.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	}
	return fmt.Errorf("unknown AccessToken field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *AccessTokenMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.user != nil {
		edges = append(edges, accesstoken.EdgeUser)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *AccessTokenMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case accesstoken.EdgeUser:
		if id := m.user; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *AccessTokenMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *AccessTokenMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *AccessTokenMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.cleareduser {
		edges = append(edges, accesstoken.EdgeUser)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *AccessTokenMutation) EdgeCleared(name string) bool {
	switch name {
	case accesstoken.EdgeUser:
		return m.cleareduser
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *AccessTokenMutation) ClearEdge(name string) error {
	switch name {
	case accesstoken.EdgeUser:
		m.ClearUser()
		return nil
	}
	return fmt.Errorf("unknown AccessToken unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *AccessTokenMutation) ResetEdge(name string) error {
	switch name {
	case accesstoken.EdgeUser:
		m.ResetUser()
		return nil
	}
	return fmt.Errorf("unknown AccessToken edge %s", name)
}

// EnvMutation represents an operation that mutates the Env nodes in the graph.
type EnvMutation struct {
	config
	op                 Op
	typ                string
	id                 *string
	created_at         *time.Time
	updated_at         *time.Time
	public             *bool
	build_count        *int32
	addbuild_count     *int32
	spawn_count        *int64
	addspawn_count     *int64
	last_spawned_at    *time.Time
	clearedFields      map[string]struct{}
	team               *uuid.UUID
	clearedteam        bool
	creator            *uuid.UUID
	clearedcreator     bool
	env_aliases        map[string]struct{}
	removedenv_aliases map[string]struct{}
	clearedenv_aliases bool
	builds             map[uuid.UUID]struct{}
	removedbuilds      map[uuid.UUID]struct{}
	clearedbuilds      bool
	snapshots          map[uuid.UUID]struct{}
	removedsnapshots   map[uuid.UUID]struct{}
	clearedsnapshots   bool
	done               bool
	oldValue           func(context.Context) (*Env, error)
	predicates         []predicate.Env
}

var _ ent.Mutation = (*EnvMutation)(nil)

// envOption allows management of the mutation configuration using functional options.
type envOption func(*EnvMutation)

// newEnvMutation creates new mutation for the Env entity.
func newEnvMutation(c config, op Op, opts ...envOption) *EnvMutation {
	m := &EnvMutation{
		config:        c,
		op:            op,
		typ:           TypeEnv,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withEnvID sets the ID field of the mutation.
func withEnvID(id string) envOption {
	return func(m *EnvMutation) {
		var (
			err   error
			once  sync.Once
			value *Env
		)
		m.oldValue = func(ctx context.Context) (*Env, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Env.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withEnv sets the old Env of the mutation.
func withEnv(node *Env) envOption {
	return func(m *EnvMutation) {
		m.oldValue = func(context.Context) (*Env, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m EnvMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m EnvMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("models: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Env entities.
func (m *EnvMutation) SetID(id string) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *EnvMutation) ID() (id string, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *EnvMutation) IDs(ctx context.Context) ([]string, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []string{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Env.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedAt sets the "created_at" field.
func (m *EnvMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *EnvMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the Env entity.
// If the Env object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EnvMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *EnvMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *EnvMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *EnvMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the Env entity.
// If the Env object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EnvMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *EnvMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetTeamID sets the "team_id" field.
func (m *EnvMutation) SetTeamID(u uuid.UUID) {
	m.team = &u
}

// TeamID returns the value of the "team_id" field in the mutation.
func (m *EnvMutation) TeamID() (r uuid.UUID, exists bool) {
	v := m.team
	if v == nil {
		return
	}
	return *v, true
}

// OldTeamID returns the old "team_id" field's value of the Env entity.
// If the Env object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EnvMutation) OldTeamID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTeamID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTeamID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTeamID: %w", err)
	}
	return oldValue.TeamID, nil
}

// ResetTeamID resets all changes to the "team_id" field.
func (m *EnvMutation) ResetTeamID() {
	m.team = nil
}

// SetCreatedBy sets the "created_by" field.
func (m *EnvMutation) SetCreatedBy(u uuid.UUID) {
	m.creator = &u
}

// CreatedBy returns the value of the "created_by" field in the mutation.
func (m *EnvMutation) CreatedBy() (r uuid.UUID, exists bool) {
	v := m.creator
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedBy returns the old "created_by" field's value of the Env entity.
// If the Env object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EnvMutation) OldCreatedBy(ctx context.Context) (v *uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedBy is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedBy requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedBy: %w", err)
	}
	return oldValue.CreatedBy, nil
}

// ClearCreatedBy clears the value of the "created_by" field.
func (m *EnvMutation) ClearCreatedBy() {
	m.creator = nil
	m.clearedFields[env.FieldCreatedBy] = struct{}{}
}

// CreatedByCleared returns if the "created_by" field was cleared in this mutation.
func (m *EnvMutation) CreatedByCleared() bool {
	_, ok := m.clearedFields[env.FieldCreatedBy]
	return ok
}

// ResetCreatedBy resets all changes to the "created_by" field.
func (m *EnvMutation) ResetCreatedBy() {
	m.creator = nil
	delete(m.clearedFields, env.FieldCreatedBy)
}

// SetPublic sets the "public" field.
func (m *EnvMutation) SetPublic(b bool) {
	m.public = &b
}

// Public returns the value of the "public" field in the mutation.
func (m *EnvMutation) Public() (r bool, exists bool) {
	v := m.public
	if v == nil {
		return
	}
	return *v, true
}

// OldPublic returns the old "public" field's value of the Env entity.
// If the Env object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EnvMutation) OldPublic(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPublic is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPublic requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPublic: %w", err)
	}
	return oldValue.Public, nil
}

// ResetPublic resets all changes to the "public" field.
func (m *EnvMutation) ResetPublic() {
	m.public = nil
}

// SetBuildCount sets the "build_count" field.
func (m *EnvMutation) SetBuildCount(i int32) {
	m.build_count = &i
	m.addbuild_count = nil
}

// BuildCount returns the value of the "build_count" field in the mutation.
func (m *EnvMutation) BuildCount() (r int32, exists bool) {
	v := m.build_count
	if v == nil {
		return
	}
	return *v, true
}

// OldBuildCount returns the old "build_count" field's value of the Env entity.
// If the Env object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EnvMutation) OldBuildCount(ctx context.Context) (v int32, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldBuildCount is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldBuildCount requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldBuildCount: %w", err)
	}
	return oldValue.BuildCount, nil
}

// AddBuildCount adds i to the "build_count" field.
func (m *EnvMutation) AddBuildCount(i int32) {
	if m.addbuild_count != nil {
		*m.addbuild_count += i
	} else {
		m.addbuild_count = &i
	}
}

// AddedBuildCount returns the value that was added to the "build_count" field in this mutation.
func (m *EnvMutation) AddedBuildCount() (r int32, exists bool) {
	v := m.addbuild_count
	if v == nil {
		return
	}
	return *v, true
}

// ResetBuildCount resets all changes to the "build_count" field.
func (m *EnvMutation) ResetBuildCount() {
	m.build_count = nil
	m.addbuild_count = nil
}

// SetSpawnCount sets the "spawn_count" field.
func (m *EnvMutation) SetSpawnCount(i int64) {
	m.spawn_count = &i
	m.addspawn_count = nil
}

// SpawnCount returns the value of the "spawn_count" field in the mutation.
func (m *EnvMutation) SpawnCount() (r int64, exists bool) {
	v := m.spawn_count
	if v == nil {
		return
	}
	return *v, true
}

// OldSpawnCount returns the old "spawn_count" field's value of the Env entity.
// If the Env object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EnvMutation) OldSpawnCount(ctx context.Context) (v int64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldSpawnCount is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldSpawnCount requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldSpawnCount: %w", err)
	}
	return oldValue.SpawnCount, nil
}

// AddSpawnCount adds i to the "spawn_count" field.
func (m *EnvMutation) AddSpawnCount(i int64) {
	if m.addspawn_count != nil {
		*m.addspawn_count += i
	} else {
		m.addspawn_count = &i
	}
}

// AddedSpawnCount returns the value that was added to the "spawn_count" field in this mutation.
func (m *EnvMutation) AddedSpawnCount() (r int64, exists bool) {
	v := m.addspawn_count
	if v == nil {
		return
	}
	return *v, true
}

// ResetSpawnCount resets all changes to the "spawn_count" field.
func (m *EnvMutation) ResetSpawnCount() {
	m.spawn_count = nil
	m.addspawn_count = nil
}

// SetLastSpawnedAt sets the "last_spawned_at" field.
func (m *EnvMutation) SetLastSpawnedAt(t time.Time) {
	m.last_spawned_at = &t
}

// LastSpawnedAt returns the value of the "last_spawned_at" field in the mutation.
func (m *EnvMutation) LastSpawnedAt() (r time.Time, exists bool) {
	v := m.last_spawned_at
	if v == nil {
		return
	}
	return *v, true
}

// OldLastSpawnedAt returns the old "last_spawned_at" field's value of the Env entity.
// If the Env object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EnvMutation) OldLastSpawnedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldLastSpawnedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldLastSpawnedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldLastSpawnedAt: %w", err)
	}
	return oldValue.LastSpawnedAt, nil
}

// ClearLastSpawnedAt clears the value of the "last_spawned_at" field.
func (m *EnvMutation) ClearLastSpawnedAt() {
	m.last_spawned_at = nil
	m.clearedFields[env.FieldLastSpawnedAt] = struct{}{}
}

// LastSpawnedAtCleared returns if the "last_spawned_at" field was cleared in this mutation.
func (m *EnvMutation) LastSpawnedAtCleared() bool {
	_, ok := m.clearedFields[env.FieldLastSpawnedAt]
	return ok
}

// ResetLastSpawnedAt resets all changes to the "last_spawned_at" field.
func (m *EnvMutation) ResetLastSpawnedAt() {
	m.last_spawned_at = nil
	delete(m.clearedFields, env.FieldLastSpawnedAt)
}

// ClearTeam clears the "team" edge to the Team entity.
func (m *EnvMutation) ClearTeam() {
	m.clearedteam = true
	m.clearedFields[env.FieldTeamID] = struct{}{}
}

// TeamCleared reports if the "team" edge to the Team entity was cleared.
func (m *EnvMutation) TeamCleared() bool {
	return m.clearedteam
}

// TeamIDs returns the "team" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// TeamID instead. It exists only for internal usage by the builders.
func (m *EnvMutation) TeamIDs() (ids []uuid.UUID) {
	if id := m.team; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetTeam resets all changes to the "team" edge.
func (m *EnvMutation) ResetTeam() {
	m.team = nil
	m.clearedteam = false
}

// SetCreatorID sets the "creator" edge to the User entity by id.
func (m *EnvMutation) SetCreatorID(id uuid.UUID) {
	m.creator = &id
}

// ClearCreator clears the "creator" edge to the User entity.
func (m *EnvMutation) ClearCreator() {
	m.clearedcreator = true
	m.clearedFields[env.FieldCreatedBy] = struct{}{}
}

// CreatorCleared reports if the "creator" edge to the User entity was cleared.
func (m *EnvMutation) CreatorCleared() bool {
	return m.CreatedByCleared() || m.clearedcreator
}

// CreatorID returns the "creator" edge ID in the mutation.
func (m *EnvMutation) CreatorID() (id uuid.UUID, exists bool) {
	if m.creator != nil {
		return *m.creator, true
	}
	return
}

// CreatorIDs returns the "creator" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// CreatorID instead. It exists only for internal usage by the builders.
func (m *EnvMutation) CreatorIDs() (ids []uuid.UUID) {
	if id := m.creator; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetCreator resets all changes to the "creator" edge.
func (m *EnvMutation) ResetCreator() {
	m.creator = nil
	m.clearedcreator = false
}

// AddEnvAliasIDs adds the "env_aliases" edge to the EnvAlias entity by ids.
func (m *EnvMutation) AddEnvAliasIDs(ids ...string) {
	if m.env_aliases == nil {
		m.env_aliases = make(map[string]struct{})
	}
	for i := range ids {
		m.env_aliases[ids[i]] = struct{}{}
	}
}

// ClearEnvAliases clears the "env_aliases" edge to the EnvAlias entity.
func (m *EnvMutation) ClearEnvAliases() {
	m.clearedenv_aliases = true
}

// EnvAliasesCleared reports if the "env_aliases" edge to the EnvAlias entity was cleared.
func (m *EnvMutation) EnvAliasesCleared() bool {
	return m.clearedenv_aliases
}

// RemoveEnvAliasIDs removes the "env_aliases" edge to the EnvAlias entity by IDs.
func (m *EnvMutation) RemoveEnvAliasIDs(ids ...string) {
	if m.removedenv_aliases == nil {
		m.removedenv_aliases = make(map[string]struct{})
	}
	for i := range ids {
		delete(m.env_aliases, ids[i])
		m.removedenv_aliases[ids[i]] = struct{}{}
	}
}

// RemovedEnvAliases returns the removed IDs of the "env_aliases" edge to the EnvAlias entity.
func (m *EnvMutation) RemovedEnvAliasesIDs() (ids []string) {
	for id := range m.removedenv_aliases {
		ids = append(ids, id)
	}
	return
}

// EnvAliasesIDs returns the "env_aliases" edge IDs in the mutation.
func (m *EnvMutation) EnvAliasesIDs() (ids []string) {
	for id := range m.env_aliases {
		ids = append(ids, id)
	}
	return
}

// ResetEnvAliases resets all changes to the "env_aliases" edge.
func (m *EnvMutation) ResetEnvAliases() {
	m.env_aliases = nil
	m.clearedenv_aliases = false
	m.removedenv_aliases = nil
}

// AddBuildIDs adds the "builds" edge to the EnvBuild entity by ids.
func (m *EnvMutation) AddBuildIDs(ids ...uuid.UUID) {
	if m.builds == nil {
		m.builds = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		m.builds[ids[i]] = struct{}{}
	}
}

// ClearBuilds clears the "builds" edge to the EnvBuild entity.
func (m *EnvMutation) ClearBuilds() {
	m.clearedbuilds = true
}

// BuildsCleared reports if the "builds" edge to the EnvBuild entity was cleared.
func (m *EnvMutation) BuildsCleared() bool {
	return m.clearedbuilds
}

// RemoveBuildIDs removes the "builds" edge to the EnvBuild entity by IDs.
func (m *EnvMutation) RemoveBuildIDs(ids ...uuid.UUID) {
	if m.removedbuilds == nil {
		m.removedbuilds = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		delete(m.builds, ids[i])
		m.removedbuilds[ids[i]] = struct{}{}
	}
}

// RemovedBuilds returns the removed IDs of the "builds" edge to the EnvBuild entity.
func (m *EnvMutation) RemovedBuildsIDs() (ids []uuid.UUID) {
	for id := range m.removedbuilds {
		ids = append(ids, id)
	}
	return
}

// BuildsIDs returns the "builds" edge IDs in the mutation.
func (m *EnvMutation) BuildsIDs() (ids []uuid.UUID) {
	for id := range m.builds {
		ids = append(ids, id)
	}
	return
}

// ResetBuilds resets all changes to the "builds" edge.
func (m *EnvMutation) ResetBuilds() {
	m.builds = nil
	m.clearedbuilds = false
	m.removedbuilds = nil
}

// AddSnapshotIDs adds the "snapshots" edge to the Snapshot entity by ids.
func (m *EnvMutation) AddSnapshotIDs(ids ...uuid.UUID) {
	if m.snapshots == nil {
		m.snapshots = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		m.snapshots[ids[i]] = struct{}{}
	}
}

// ClearSnapshots clears the "snapshots" edge to the Snapshot entity.
func (m *EnvMutation) ClearSnapshots() {
	m.clearedsnapshots = true
}

// SnapshotsCleared reports if the "snapshots" edge to the Snapshot entity was cleared.
func (m *EnvMutation) SnapshotsCleared() bool {
	return m.clearedsnapshots
}

// RemoveSnapshotIDs removes the "snapshots" edge to the Snapshot entity by IDs.
func (m *EnvMutation) RemoveSnapshotIDs(ids ...uuid.UUID) {
	if m.removedsnapshots == nil {
		m.removedsnapshots = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		delete(m.snapshots, ids[i])
		m.removedsnapshots[ids[i]] = struct{}{}
	}
}

// RemovedSnapshots returns the removed IDs of the "snapshots" edge to the Snapshot entity.
func (m *EnvMutation) RemovedSnapshotsIDs() (ids []uuid.UUID) {
	for id := range m.removedsnapshots {
		ids = append(ids, id)
	}
	return
}

// SnapshotsIDs returns the "snapshots" edge IDs in the mutation.
func (m *EnvMutation) SnapshotsIDs() (ids []uuid.UUID) {
	for id := range m.snapshots {
		ids = append(ids, id)
	}
	return
}

// ResetSnapshots resets all changes to the "snapshots" edge.
func (m *EnvMutation) ResetSnapshots() {
	m.snapshots = nil
	m.clearedsnapshots = false
	m.removedsnapshots = nil
}

// Where appends a list predicates to the EnvMutation builder.
func (m *EnvMutation) Where(ps ...predicate.Env) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the EnvMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *EnvMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Env, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *EnvMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *EnvMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Env).
func (m *EnvMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *EnvMutation) Fields() []string {
	fields := make([]string, 0, 8)
	if m.created_at != nil {
		fields = append(fields, env.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, env.FieldUpdatedAt)
	}
	if m.team != nil {
		fields = append(fields, env.FieldTeamID)
	}
	if m.creator != nil {
		fields = append(fields, env.FieldCreatedBy)
	}
	if m.public != nil {
		fields = append(fields, env.FieldPublic)
	}
	if m.build_count != nil {
		fields = append(fields, env.FieldBuildCount)
	}
	if m.spawn_count != nil {
		fields = append(fields, env.FieldSpawnCount)
	}
	if m.last_spawned_at != nil {
		fields = append(fields, env.FieldLastSpawnedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *EnvMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case env.FieldCreatedAt:
		return m.CreatedAt()
	case env.FieldUpdatedAt:
		return m.UpdatedAt()
	case env.FieldTeamID:
		return m.TeamID()
	case env.FieldCreatedBy:
		return m.CreatedBy()
	case env.FieldPublic:
		return m.Public()
	case env.FieldBuildCount:
		return m.BuildCount()
	case env.FieldSpawnCount:
		return m.SpawnCount()
	case env.FieldLastSpawnedAt:
		return m.LastSpawnedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *EnvMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case env.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case env.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case env.FieldTeamID:
		return m.OldTeamID(ctx)
	case env.FieldCreatedBy:
		return m.OldCreatedBy(ctx)
	case env.FieldPublic:
		return m.OldPublic(ctx)
	case env.FieldBuildCount:
		return m.OldBuildCount(ctx)
	case env.FieldSpawnCount:
		return m.OldSpawnCount(ctx)
	case env.FieldLastSpawnedAt:
		return m.OldLastSpawnedAt(ctx)
	}
	return nil, fmt.Errorf("unknown Env field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *EnvMutation) SetField(name string, value ent.Value) error {
	switch name {
	case env.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case env.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case env.FieldTeamID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTeamID(v)
		return nil
	case env.FieldCreatedBy:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedBy(v)
		return nil
	case env.FieldPublic:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPublic(v)
		return nil
	case env.FieldBuildCount:
		v, ok := value.(int32)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetBuildCount(v)
		return nil
	case env.FieldSpawnCount:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetSpawnCount(v)
		return nil
	case env.FieldLastSpawnedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetLastSpawnedAt(v)
		return nil
	}
	return fmt.Errorf("unknown Env field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *EnvMutation) AddedFields() []string {
	var fields []string
	if m.addbuild_count != nil {
		fields = append(fields, env.FieldBuildCount)
	}
	if m.addspawn_count != nil {
		fields = append(fields, env.FieldSpawnCount)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *EnvMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case env.FieldBuildCount:
		return m.AddedBuildCount()
	case env.FieldSpawnCount:
		return m.AddedSpawnCount()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *EnvMutation) AddField(name string, value ent.Value) error {
	switch name {
	case env.FieldBuildCount:
		v, ok := value.(int32)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddBuildCount(v)
		return nil
	case env.FieldSpawnCount:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddSpawnCount(v)
		return nil
	}
	return fmt.Errorf("unknown Env numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *EnvMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(env.FieldCreatedBy) {
		fields = append(fields, env.FieldCreatedBy)
	}
	if m.FieldCleared(env.FieldLastSpawnedAt) {
		fields = append(fields, env.FieldLastSpawnedAt)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *EnvMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *EnvMutation) ClearField(name string) error {
	switch name {
	case env.FieldCreatedBy:
		m.ClearCreatedBy()
		return nil
	case env.FieldLastSpawnedAt:
		m.ClearLastSpawnedAt()
		return nil
	}
	return fmt.Errorf("unknown Env nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *EnvMutation) ResetField(name string) error {
	switch name {
	case env.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case env.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case env.FieldTeamID:
		m.ResetTeamID()
		return nil
	case env.FieldCreatedBy:
		m.ResetCreatedBy()
		return nil
	case env.FieldPublic:
		m.ResetPublic()
		return nil
	case env.FieldBuildCount:
		m.ResetBuildCount()
		return nil
	case env.FieldSpawnCount:
		m.ResetSpawnCount()
		return nil
	case env.FieldLastSpawnedAt:
		m.ResetLastSpawnedAt()
		return nil
	}
	return fmt.Errorf("unknown Env field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *EnvMutation) AddedEdges() []string {
	edges := make([]string, 0, 5)
	if m.team != nil {
		edges = append(edges, env.EdgeTeam)
	}
	if m.creator != nil {
		edges = append(edges, env.EdgeCreator)
	}
	if m.env_aliases != nil {
		edges = append(edges, env.EdgeEnvAliases)
	}
	if m.builds != nil {
		edges = append(edges, env.EdgeBuilds)
	}
	if m.snapshots != nil {
		edges = append(edges, env.EdgeSnapshots)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *EnvMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case env.EdgeTeam:
		if id := m.team; id != nil {
			return []ent.Value{*id}
		}
	case env.EdgeCreator:
		if id := m.creator; id != nil {
			return []ent.Value{*id}
		}
	case env.EdgeEnvAliases:
		ids := make([]ent.Value, 0, len(m.env_aliases))
		for id := range m.env_aliases {
			ids = append(ids, id)
		}
		return ids
	case env.EdgeBuilds:
		ids := make([]ent.Value, 0, len(m.builds))
		for id := range m.builds {
			ids = append(ids, id)
		}
		return ids
	case env.EdgeSnapshots:
		ids := make([]ent.Value, 0, len(m.snapshots))
		for id := range m.snapshots {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *EnvMutation) RemovedEdges() []string {
	edges := make([]string, 0, 5)
	if m.removedenv_aliases != nil {
		edges = append(edges, env.EdgeEnvAliases)
	}
	if m.removedbuilds != nil {
		edges = append(edges, env.EdgeBuilds)
	}
	if m.removedsnapshots != nil {
		edges = append(edges, env.EdgeSnapshots)
	}
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *EnvMutation) RemovedIDs(name string) []ent.Value {
	switch name {
	case env.EdgeEnvAliases:
		ids := make([]ent.Value, 0, len(m.removedenv_aliases))
		for id := range m.removedenv_aliases {
			ids = append(ids, id)
		}
		return ids
	case env.EdgeBuilds:
		ids := make([]ent.Value, 0, len(m.removedbuilds))
		for id := range m.removedbuilds {
			ids = append(ids, id)
		}
		return ids
	case env.EdgeSnapshots:
		ids := make([]ent.Value, 0, len(m.removedsnapshots))
		for id := range m.removedsnapshots {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *EnvMutation) ClearedEdges() []string {
	edges := make([]string, 0, 5)
	if m.clearedteam {
		edges = append(edges, env.EdgeTeam)
	}
	if m.clearedcreator {
		edges = append(edges, env.EdgeCreator)
	}
	if m.clearedenv_aliases {
		edges = append(edges, env.EdgeEnvAliases)
	}
	if m.clearedbuilds {
		edges = append(edges, env.EdgeBuilds)
	}
	if m.clearedsnapshots {
		edges = append(edges, env.EdgeSnapshots)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *EnvMutation) EdgeCleared(name string) bool {
	switch name {
	case env.EdgeTeam:
		return m.clearedteam
	case env.EdgeCreator:
		return m.clearedcreator
	case env.EdgeEnvAliases:
		return m.clearedenv_aliases
	case env.EdgeBuilds:
		return m.clearedbuilds
	case env.EdgeSnapshots:
		return m.clearedsnapshots
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *EnvMutation) ClearEdge(name string) error {
	switch name {
	case env.EdgeTeam:
		m.ClearTeam()
		return nil
	case env.EdgeCreator:
		m.ClearCreator()
		return nil
	}
	return fmt.Errorf("unknown Env unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *EnvMutation) ResetEdge(name string) error {
	switch name {
	case env.EdgeTeam:
		m.ResetTeam()
		return nil
	case env.EdgeCreator:
		m.ResetCreator()
		return nil
	case env.EdgeEnvAliases:
		m.ResetEnvAliases()
		return nil
	case env.EdgeBuilds:
		m.ResetBuilds()
		return nil
	case env.EdgeSnapshots:
		m.ResetSnapshots()
		return nil
	}
	return fmt.Errorf("unknown Env edge %s", name)
}

// EnvAliasMutation represents an operation that mutates the EnvAlias nodes in the graph.
type EnvAliasMutation struct {
	config
	op            Op
	typ           string
	id            *string
	is_renamable  *bool
	clearedFields map[string]struct{}
	env           *string
	clearedenv    bool
	done          bool
	oldValue      func(context.Context) (*EnvAlias, error)
	predicates    []predicate.EnvAlias
}

var _ ent.Mutation = (*EnvAliasMutation)(nil)

// envaliasOption allows management of the mutation configuration using functional options.
type envaliasOption func(*EnvAliasMutation)

// newEnvAliasMutation creates new mutation for the EnvAlias entity.
func newEnvAliasMutation(c config, op Op, opts ...envaliasOption) *EnvAliasMutation {
	m := &EnvAliasMutation{
		config:        c,
		op:            op,
		typ:           TypeEnvAlias,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withEnvAliasID sets the ID field of the mutation.
func withEnvAliasID(id string) envaliasOption {
	return func(m *EnvAliasMutation) {
		var (
			err   error
			once  sync.Once
			value *EnvAlias
		)
		m.oldValue = func(ctx context.Context) (*EnvAlias, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().EnvAlias.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withEnvAlias sets the old EnvAlias of the mutation.
func withEnvAlias(node *EnvAlias) envaliasOption {
	return func(m *EnvAliasMutation) {
		m.oldValue = func(context.Context) (*EnvAlias, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m EnvAliasMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m EnvAliasMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("models: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of EnvAlias entities.
func (m *EnvAliasMutation) SetID(id string) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *EnvAliasMutation) ID() (id string, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *EnvAliasMutation) IDs(ctx context.Context) ([]string, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []string{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().EnvAlias.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetEnvID sets the "env_id" field.
func (m *EnvAliasMutation) SetEnvID(s string) {
	m.env = &s
}

// EnvID returns the value of the "env_id" field in the mutation.
func (m *EnvAliasMutation) EnvID() (r string, exists bool) {
	v := m.env
	if v == nil {
		return
	}
	return *v, true
}

// OldEnvID returns the old "env_id" field's value of the EnvAlias entity.
// If the EnvAlias object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EnvAliasMutation) OldEnvID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEnvID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEnvID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEnvID: %w", err)
	}
	return oldValue.EnvID, nil
}

// ResetEnvID resets all changes to the "env_id" field.
func (m *EnvAliasMutation) ResetEnvID() {
	m.env = nil
}

// SetIsRenamable sets the "is_renamable" field.
func (m *EnvAliasMutation) SetIsRenamable(b bool) {
	m.is_renamable = &b
}

// IsRenamable returns the value of the "is_renamable" field in the mutation.
func (m *EnvAliasMutation) IsRenamable() (r bool, exists bool) {
	v := m.is_renamable
	if v == nil {
		return
	}
	return *v, true
}

// OldIsRenamable returns the old "is_renamable" field's value of the EnvAlias entity.
// If the EnvAlias object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EnvAliasMutation) OldIsRenamable(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIsRenamable is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIsRenamable requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIsRenamable: %w", err)
	}
	return oldValue.IsRenamable, nil
}

// ResetIsRenamable resets all changes to the "is_renamable" field.
func (m *EnvAliasMutation) ResetIsRenamable() {
	m.is_renamable = nil
}

// ClearEnv clears the "env" edge to the Env entity.
func (m *EnvAliasMutation) ClearEnv() {
	m.clearedenv = true
	m.clearedFields[envalias.FieldEnvID] = struct{}{}
}

// EnvCleared reports if the "env" edge to the Env entity was cleared.
func (m *EnvAliasMutation) EnvCleared() bool {
	return m.clearedenv
}

// EnvIDs returns the "env" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// EnvID instead. It exists only for internal usage by the builders.
func (m *EnvAliasMutation) EnvIDs() (ids []string) {
	if id := m.env; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetEnv resets all changes to the "env" edge.
func (m *EnvAliasMutation) ResetEnv() {
	m.env = nil
	m.clearedenv = false
}

// Where appends a list predicates to the EnvAliasMutation builder.
func (m *EnvAliasMutation) Where(ps ...predicate.EnvAlias) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the EnvAliasMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *EnvAliasMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.EnvAlias, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *EnvAliasMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *EnvAliasMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (EnvAlias).
func (m *EnvAliasMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *EnvAliasMutation) Fields() []string {
	fields := make([]string, 0, 2)
	if m.env != nil {
		fields = append(fields, envalias.FieldEnvID)
	}
	if m.is_renamable != nil {
		fields = append(fields, envalias.FieldIsRenamable)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *EnvAliasMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case envalias.FieldEnvID:
		return m.EnvID()
	case envalias.FieldIsRenamable:
		return m.IsRenamable()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *EnvAliasMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case envalias.FieldEnvID:
		return m.OldEnvID(ctx)
	case envalias.FieldIsRenamable:
		return m.OldIsRenamable(ctx)
	}
	return nil, fmt.Errorf("unknown EnvAlias field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *EnvAliasMutation) SetField(name string, value ent.Value) error {
	switch name {
	case envalias.FieldEnvID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEnvID(v)
		return nil
	case envalias.FieldIsRenamable:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIsRenamable(v)
		return nil
	}
	return fmt.Errorf("unknown EnvAlias field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *EnvAliasMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *EnvAliasMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *EnvAliasMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown EnvAlias numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *EnvAliasMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *EnvAliasMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *EnvAliasMutation) ClearField(name string) error {
	return fmt.Errorf("unknown EnvAlias nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *EnvAliasMutation) ResetField(name string) error {
	switch name {
	case envalias.FieldEnvID:
		m.ResetEnvID()
		return nil
	case envalias.FieldIsRenamable:
		m.ResetIsRenamable()
		return nil
	}
	return fmt.Errorf("unknown EnvAlias field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *EnvAliasMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.env != nil {
		edges = append(edges, envalias.EdgeEnv)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *EnvAliasMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case envalias.EdgeEnv:
		if id := m.env; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *EnvAliasMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *EnvAliasMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *EnvAliasMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.clearedenv {
		edges = append(edges, envalias.EdgeEnv)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *EnvAliasMutation) EdgeCleared(name string) bool {
	switch name {
	case envalias.EdgeEnv:
		return m.clearedenv
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *EnvAliasMutation) ClearEdge(name string) error {
	switch name {
	case envalias.EdgeEnv:
		m.ClearEnv()
		return nil
	}
	return fmt.Errorf("unknown EnvAlias unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *EnvAliasMutation) ResetEdge(name string) error {
	switch name {
	case envalias.EdgeEnv:
		m.ResetEnv()
		return nil
	}
	return fmt.Errorf("unknown EnvAlias edge %s", name)
}

// EnvBuildMutation represents an operation that mutates the EnvBuild nodes in the graph.
type EnvBuildMutation struct {
	config
	op                    Op
	typ                   string
	id                    *uuid.UUID
	created_at            *time.Time
	updated_at            *time.Time
	finished_at           *time.Time
	status                *envbuild.Status
	dockerfile            *string
	start_cmd             *string
	ready_cmd             *string
	vcpu                  *int64
	addvcpu               *int64
	ram_mb                *int64
	addram_mb             *int64
	free_disk_size_mb     *int64
	addfree_disk_size_mb  *int64
	total_disk_size_mb    *int64
	addtotal_disk_size_mb *int64
	kernel_version        *string
	firecracker_version   *string
	envd_version          *string
	clearedFields         map[string]struct{}
	env                   *string
	clearedenv            bool
	done                  bool
	oldValue              func(context.Context) (*EnvBuild, error)
	predicates            []predicate.EnvBuild
}

var _ ent.Mutation = (*EnvBuildMutation)(nil)

// envbuildOption allows management of the mutation configuration using functional options.
type envbuildOption func(*EnvBuildMutation)

// newEnvBuildMutation creates new mutation for the EnvBuild entity.
func newEnvBuildMutation(c config, op Op, opts ...envbuildOption) *EnvBuildMutation {
	m := &EnvBuildMutation{
		config:        c,
		op:            op,
		typ:           TypeEnvBuild,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withEnvBuildID sets the ID field of the mutation.
func withEnvBuildID(id uuid.UUID) envbuildOption {
	return func(m *EnvBuildMutation) {
		var (
			err   error
			once  sync.Once
			value *EnvBuild
		)
		m.oldValue = func(ctx context.Context) (*EnvBuild, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().EnvBuild.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withEnvBuild sets the old EnvBuild of the mutation.
func withEnvBuild(node *EnvBuild) envbuildOption {
	return func(m *EnvBuildMutation) {
		m.oldValue = func(context.Context) (*EnvBuild, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m EnvBuildMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m EnvBuildMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("models: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of EnvBuild entities.
func (m *EnvBuildMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *EnvBuildMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *EnvBuildMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().EnvBuild.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedAt sets the "created_at" field.
func (m *EnvBuildMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *EnvBuildMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the EnvBuild entity.
// If the EnvBuild object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EnvBuildMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *EnvBuildMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *EnvBuildMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *EnvBuildMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the EnvBuild entity.
// If the EnvBuild object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EnvBuildMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *EnvBuildMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetFinishedAt sets the "finished_at" field.
func (m *EnvBuildMutation) SetFinishedAt(t time.Time) {
	m.finished_at = &t
}

// FinishedAt returns the value of the "finished_at" field in the mutation.
func (m *EnvBuildMutation) FinishedAt() (r time.Time, exists bool) {
	v := m.finished_at
	if v == nil {
		return
	}
	return *v, true
}

// OldFinishedAt returns the old "finished_at" field's value of the EnvBuild entity.
// If the EnvBuild object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EnvBuildMutation) OldFinishedAt(ctx context.Context) (v *time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFinishedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFinishedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFinishedAt: %w", err)
	}
	return oldValue.FinishedAt, nil
}

// ClearFinishedAt clears the value of the "finished_at" field.
func (m *EnvBuildMutation) ClearFinishedAt() {
	m.finished_at = nil
	m.clearedFields[envbuild.FieldFinishedAt] = struct{}{}
}

// FinishedAtCleared returns if the "finished_at" field was cleared in this mutation.
func (m *EnvBuildMutation) FinishedAtCleared() bool {
	_, ok := m.clearedFields[envbuild.FieldFinishedAt]
	return ok
}

// ResetFinishedAt resets all changes to the "finished_at" field.
func (m *EnvBuildMutation) ResetFinishedAt() {
	m.finished_at = nil
	delete(m.clearedFields, envbuild.FieldFinishedAt)
}

// SetEnvID sets the "env_id" field.
func (m *EnvBuildMutation) SetEnvID(s string) {
	m.env = &s
}

// EnvID returns the value of the "env_id" field in the mutation.
func (m *EnvBuildMutation) EnvID() (r string, exists bool) {
	v := m.env
	if v == nil {
		return
	}
	return *v, true
}

// OldEnvID returns the old "env_id" field's value of the EnvBuild entity.
// If the EnvBuild object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EnvBuildMutation) OldEnvID(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEnvID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEnvID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEnvID: %w", err)
	}
	return oldValue.EnvID, nil
}

// ClearEnvID clears the value of the "env_id" field.
func (m *EnvBuildMutation) ClearEnvID() {
	m.env = nil
	m.clearedFields[envbuild.FieldEnvID] = struct{}{}
}

// EnvIDCleared returns if the "env_id" field was cleared in this mutation.
func (m *EnvBuildMutation) EnvIDCleared() bool {
	_, ok := m.clearedFields[envbuild.FieldEnvID]
	return ok
}

// ResetEnvID resets all changes to the "env_id" field.
func (m *EnvBuildMutation) ResetEnvID() {
	m.env = nil
	delete(m.clearedFields, envbuild.FieldEnvID)
}

// SetStatus sets the "status" field.
func (m *EnvBuildMutation) SetStatus(e envbuild.Status) {
	m.status = &e
}

// Status returns the value of the "status" field in the mutation.
func (m *EnvBuildMutation) Status() (r envbuild.Status, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the EnvBuild entity.
// If the EnvBuild object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EnvBuildMutation) OldStatus(ctx context.Context) (v envbuild.Status, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// ResetStatus resets all changes to the "status" field.
func (m *EnvBuildMutation) ResetStatus() {
	m.status = nil
}

// SetDockerfile sets the "dockerfile" field.
func (m *EnvBuildMutation) SetDockerfile(s string) {
	m.dockerfile = &s
}

// Dockerfile returns the value of the "dockerfile" field in the mutation.
func (m *EnvBuildMutation) Dockerfile() (r string, exists bool) {
	v := m.dockerfile
	if v == nil {
		return
	}
	return *v, true
}

// OldDockerfile returns the old "dockerfile" field's value of the EnvBuild entity.
// If the EnvBuild object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EnvBuildMutation) OldDockerfile(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDockerfile is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDockerfile requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDockerfile: %w", err)
	}
	return oldValue.Dockerfile, nil
}

// ClearDockerfile clears the value of the "dockerfile" field.
func (m *EnvBuildMutation) ClearDockerfile() {
	m.dockerfile = nil
	m.clearedFields[envbuild.FieldDockerfile] = struct{}{}
}

// DockerfileCleared returns if the "dockerfile" field was cleared in this mutation.
func (m *EnvBuildMutation) DockerfileCleared() bool {
	_, ok := m.clearedFields[envbuild.FieldDockerfile]
	return ok
}

// ResetDockerfile resets all changes to the "dockerfile" field.
func (m *EnvBuildMutation) ResetDockerfile() {
	m.dockerfile = nil
	delete(m.clearedFields, envbuild.FieldDockerfile)
}

// SetStartCmd sets the "start_cmd" field.
func (m *EnvBuildMutation) SetStartCmd(s string) {
	m.start_cmd = &s
}

// StartCmd returns the value of the "start_cmd" field in the mutation.
func (m *EnvBuildMutation) StartCmd() (r string, exists bool) {
	v := m.start_cmd
	if v == nil {
		return
	}
	return *v, true
}

// OldStartCmd returns the old "start_cmd" field's value of the EnvBuild entity.
// If the EnvBuild object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EnvBuildMutation) OldStartCmd(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStartCmd is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStartCmd requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStartCmd: %w", err)
	}
	return oldValue.StartCmd, nil
}

// ClearStartCmd clears the value of the "start_cmd" field.
func (m *EnvBuildMutation) ClearStartCmd() {
	m.start_cmd = nil
	m.clearedFields[envbuild.FieldStartCmd] = struct{}{}
}

// StartCmdCleared returns if the "start_cmd" field was cleared in this mutation.
func (m *EnvBuildMutation) StartCmdCleared() bool {
	_, ok := m.clearedFields[envbuild.FieldStartCmd]
	return ok
}

// ResetStartCmd resets all changes to the "start_cmd" field.
func (m *EnvBuildMutation) ResetStartCmd() {
	m.start_cmd = nil
	delete(m.clearedFields, envbuild.FieldStartCmd)
}

// SetReadyCmd sets the "ready_cmd" field.
func (m *EnvBuildMutation) SetReadyCmd(s string) {
	m.ready_cmd = &s
}

// ReadyCmd returns the value of the "ready_cmd" field in the mutation.
func (m *EnvBuildMutation) ReadyCmd() (r string, exists bool) {
	v := m.ready_cmd
	if v == nil {
		return
	}
	return *v, true
}

// OldReadyCmd returns the old "ready_cmd" field's value of the EnvBuild entity.
// If the EnvBuild object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EnvBuildMutation) OldReadyCmd(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldReadyCmd is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldReadyCmd requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldReadyCmd: %w", err)
	}
	return oldValue.ReadyCmd, nil
}

// ClearReadyCmd clears the value of the "ready_cmd" field.
func (m *EnvBuildMutation) ClearReadyCmd() {
	m.ready_cmd = nil
	m.clearedFields[envbuild.FieldReadyCmd] = struct{}{}
}

// ReadyCmdCleared returns if the "ready_cmd" field was cleared in this mutation.
func (m *EnvBuildMutation) ReadyCmdCleared() bool {
	_, ok := m.clearedFields[envbuild.FieldReadyCmd]
	return ok
}

// ResetReadyCmd resets all changes to the "ready_cmd" field.
func (m *EnvBuildMutation) ResetReadyCmd() {
	m.ready_cmd = nil
	delete(m.clearedFields, envbuild.FieldReadyCmd)
}

// SetVcpu sets the "vcpu" field.
func (m *EnvBuildMutation) SetVcpu(i int64) {
	m.vcpu = &i
	m.addvcpu = nil
}

// Vcpu returns the value of the "vcpu" field in the mutation.
func (m *EnvBuildMutation) Vcpu() (r int64, exists bool) {
	v := m.vcpu
	if v == nil {
		return
	}
	return *v, true
}

// OldVcpu returns the old "vcpu" field's value of the EnvBuild entity.
// If the EnvBuild object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EnvBuildMutation) OldVcpu(ctx context.Context) (v int64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldVcpu is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldVcpu requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldVcpu: %w", err)
	}
	return oldValue.Vcpu, nil
}

// AddVcpu adds i to the "vcpu" field.
func (m *EnvBuildMutation) AddVcpu(i int64) {
	if m.addvcpu != nil {
		*m.addvcpu += i
	} else {
		m.addvcpu = &i
	}
}

// AddedVcpu returns the value that was added to the "vcpu" field in this mutation.
func (m *EnvBuildMutation) AddedVcpu() (r int64, exists bool) {
	v := m.addvcpu
	if v == nil {
		return
	}
	return *v, true
}

// ResetVcpu resets all changes to the "vcpu" field.
func (m *EnvBuildMutation) ResetVcpu() {
	m.vcpu = nil
	m.addvcpu = nil
}

// SetRAMMB sets the "ram_mb" field.
func (m *EnvBuildMutation) SetRAMMB(i int64) {
	m.ram_mb = &i
	m.addram_mb = nil
}

// RAMMB returns the value of the "ram_mb" field in the mutation.
func (m *EnvBuildMutation) RAMMB() (r int64, exists bool) {
	v := m.ram_mb
	if v == nil {
		return
	}
	return *v, true
}

// OldRAMMB returns the old "ram_mb" field's value of the EnvBuild entity.
// If the EnvBuild object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EnvBuildMutation) OldRAMMB(ctx context.Context) (v int64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRAMMB is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRAMMB requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRAMMB: %w", err)
	}
	return oldValue.RAMMB, nil
}

// AddRAMMB adds i to the "ram_mb" field.
func (m *EnvBuildMutation) AddRAMMB(i int64) {
	if m.addram_mb != nil {
		*m.addram_mb += i
	} else {
		m.addram_mb = &i
	}
}

// AddedRAMMB returns the value that was added to the "ram_mb" field in this mutation.
func (m *EnvBuildMutation) AddedRAMMB() (r int64, exists bool) {
	v := m.addram_mb
	if v == nil {
		return
	}
	return *v, true
}

// ResetRAMMB resets all changes to the "ram_mb" field.
func (m *EnvBuildMutation) ResetRAMMB() {
	m.ram_mb = nil
	m.addram_mb = nil
}

// SetFreeDiskSizeMB sets the "free_disk_size_mb" field.
func (m *EnvBuildMutation) SetFreeDiskSizeMB(i int64) {
	m.free_disk_size_mb = &i
	m.addfree_disk_size_mb = nil
}

// FreeDiskSizeMB returns the value of the "free_disk_size_mb" field in the mutation.
func (m *EnvBuildMutation) FreeDiskSizeMB() (r int64, exists bool) {
	v := m.free_disk_size_mb
	if v == nil {
		return
	}
	return *v, true
}

// OldFreeDiskSizeMB returns the old "free_disk_size_mb" field's value of the EnvBuild entity.
// If the EnvBuild object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EnvBuildMutation) OldFreeDiskSizeMB(ctx context.Context) (v int64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFreeDiskSizeMB is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFreeDiskSizeMB requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFreeDiskSizeMB: %w", err)
	}
	return oldValue.FreeDiskSizeMB, nil
}

// AddFreeDiskSizeMB adds i to the "free_disk_size_mb" field.
func (m *EnvBuildMutation) AddFreeDiskSizeMB(i int64) {
	if m.addfree_disk_size_mb != nil {
		*m.addfree_disk_size_mb += i
	} else {
		m.addfree_disk_size_mb = &i
	}
}

// AddedFreeDiskSizeMB returns the value that was added to the "free_disk_size_mb" field in this mutation.
func (m *EnvBuildMutation) AddedFreeDiskSizeMB() (r int64, exists bool) {
	v := m.addfree_disk_size_mb
	if v == nil {
		return
	}
	return *v, true
}

// ResetFreeDiskSizeMB resets all changes to the "free_disk_size_mb" field.
func (m *EnvBuildMutation) ResetFreeDiskSizeMB() {
	m.free_disk_size_mb = nil
	m.addfree_disk_size_mb = nil
}

// SetTotalDiskSizeMB sets the "total_disk_size_mb" field.
func (m *EnvBuildMutation) SetTotalDiskSizeMB(i int64) {
	m.total_disk_size_mb = &i
	m.addtotal_disk_size_mb = nil
}

// TotalDiskSizeMB returns the value of the "total_disk_size_mb" field in the mutation.
func (m *EnvBuildMutation) TotalDiskSizeMB() (r int64, exists bool) {
	v := m.total_disk_size_mb
	if v == nil {
		return
	}
	return *v, true
}

// OldTotalDiskSizeMB returns the old "total_disk_size_mb" field's value of the EnvBuild entity.
// If the EnvBuild object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EnvBuildMutation) OldTotalDiskSizeMB(ctx context.Context) (v *int64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTotalDiskSizeMB is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTotalDiskSizeMB requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTotalDiskSizeMB: %w", err)
	}
	return oldValue.TotalDiskSizeMB, nil
}

// AddTotalDiskSizeMB adds i to the "total_disk_size_mb" field.
func (m *EnvBuildMutation) AddTotalDiskSizeMB(i int64) {
	if m.addtotal_disk_size_mb != nil {
		*m.addtotal_disk_size_mb += i
	} else {
		m.addtotal_disk_size_mb = &i
	}
}

// AddedTotalDiskSizeMB returns the value that was added to the "total_disk_size_mb" field in this mutation.
func (m *EnvBuildMutation) AddedTotalDiskSizeMB() (r int64, exists bool) {
	v := m.addtotal_disk_size_mb
	if v == nil {
		return
	}
	return *v, true
}

// ClearTotalDiskSizeMB clears the value of the "total_disk_size_mb" field.
func (m *EnvBuildMutation) ClearTotalDiskSizeMB() {
	m.total_disk_size_mb = nil
	m.addtotal_disk_size_mb = nil
	m.clearedFields[envbuild.FieldTotalDiskSizeMB] = struct{}{}
}

// TotalDiskSizeMBCleared returns if the "total_disk_size_mb" field was cleared in this mutation.
func (m *EnvBuildMutation) TotalDiskSizeMBCleared() bool {
	_, ok := m.clearedFields[envbuild.FieldTotalDiskSizeMB]
	return ok
}

// ResetTotalDiskSizeMB resets all changes to the "total_disk_size_mb" field.
func (m *EnvBuildMutation) ResetTotalDiskSizeMB() {
	m.total_disk_size_mb = nil
	m.addtotal_disk_size_mb = nil
	delete(m.clearedFields, envbuild.FieldTotalDiskSizeMB)
}

// SetKernelVersion sets the "kernel_version" field.
func (m *EnvBuildMutation) SetKernelVersion(s string) {
	m.kernel_version = &s
}

// KernelVersion returns the value of the "kernel_version" field in the mutation.
func (m *EnvBuildMutation) KernelVersion() (r string, exists bool) {
	v := m.kernel_version
	if v == nil {
		return
	}
	return *v, true
}

// OldKernelVersion returns the old "kernel_version" field's value of the EnvBuild entity.
// If the EnvBuild object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EnvBuildMutation) OldKernelVersion(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldKernelVersion is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldKernelVersion requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldKernelVersion: %w", err)
	}
	return oldValue.KernelVersion, nil
}

// ResetKernelVersion resets all changes to the "kernel_version" field.
func (m *EnvBuildMutation) ResetKernelVersion() {
	m.kernel_version = nil
}

// SetFirecrackerVersion sets the "firecracker_version" field.
func (m *EnvBuildMutation) SetFirecrackerVersion(s string) {
	m.firecracker_version = &s
}

// FirecrackerVersion returns the value of the "firecracker_version" field in the mutation.
func (m *EnvBuildMutation) FirecrackerVersion() (r string, exists bool) {
	v := m.firecracker_version
	if v == nil {
		return
	}
	return *v, true
}

// OldFirecrackerVersion returns the old "firecracker_version" field's value of the EnvBuild entity.
// If the EnvBuild object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EnvBuildMutation) OldFirecrackerVersion(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFirecrackerVersion is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFirecrackerVersion requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFirecrackerVersion: %w", err)
	}
	return oldValue.FirecrackerVersion, nil
}

// ResetFirecrackerVersion resets all changes to the "firecracker_version" field.
func (m *EnvBuildMutation) ResetFirecrackerVersion() {
	m.firecracker_version = nil
}

// SetEnvdVersion sets the "envd_version" field.
func (m *EnvBuildMutation) SetEnvdVersion(s string) {
	m.envd_version = &s
}

// EnvdVersion returns the value of the "envd_version" field in the mutation.
func (m *EnvBuildMutation) EnvdVersion() (r string, exists bool) {
	v := m.envd_version
	if v == nil {
		return
	}
	return *v, true
}

// OldEnvdVersion returns the old "envd_version" field's value of the EnvBuild entity.
// If the EnvBuild object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EnvBuildMutation) OldEnvdVersion(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEnvdVersion is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEnvdVersion requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEnvdVersion: %w", err)
	}
	return oldValue.EnvdVersion, nil
}

// ClearEnvdVersion clears the value of the "envd_version" field.
func (m *EnvBuildMutation) ClearEnvdVersion() {
	m.envd_version = nil
	m.clearedFields[envbuild.FieldEnvdVersion] = struct{}{}
}

// EnvdVersionCleared returns if the "envd_version" field was cleared in this mutation.
func (m *EnvBuildMutation) EnvdVersionCleared() bool {
	_, ok := m.clearedFields[envbuild.FieldEnvdVersion]
	return ok
}

// ResetEnvdVersion resets all changes to the "envd_version" field.
func (m *EnvBuildMutation) ResetEnvdVersion() {
	m.envd_version = nil
	delete(m.clearedFields, envbuild.FieldEnvdVersion)
}

// ClearEnv clears the "env" edge to the Env entity.
func (m *EnvBuildMutation) ClearEnv() {
	m.clearedenv = true
	m.clearedFields[envbuild.FieldEnvID] = struct{}{}
}

// EnvCleared reports if the "env" edge to the Env entity was cleared.
func (m *EnvBuildMutation) EnvCleared() bool {
	return m.EnvIDCleared() || m.clearedenv
}

// EnvIDs returns the "env" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// EnvID instead. It exists only for internal usage by the builders.
func (m *EnvBuildMutation) EnvIDs() (ids []string) {
	if id := m.env; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetEnv resets all changes to the "env" edge.
func (m *EnvBuildMutation) ResetEnv() {
	m.env = nil
	m.clearedenv = false
}

// Where appends a list predicates to the EnvBuildMutation builder.
func (m *EnvBuildMutation) Where(ps ...predicate.EnvBuild) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the EnvBuildMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *EnvBuildMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.EnvBuild, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *EnvBuildMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *EnvBuildMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (EnvBuild).
func (m *EnvBuildMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *EnvBuildMutation) Fields() []string {
	fields := make([]string, 0, 15)
	if m.created_at != nil {
		fields = append(fields, envbuild.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, envbuild.FieldUpdatedAt)
	}
	if m.finished_at != nil {
		fields = append(fields, envbuild.FieldFinishedAt)
	}
	if m.env != nil {
		fields = append(fields, envbuild.FieldEnvID)
	}
	if m.status != nil {
		fields = append(fields, envbuild.FieldStatus)
	}
	if m.dockerfile != nil {
		fields = append(fields, envbuild.FieldDockerfile)
	}
	if m.start_cmd != nil {
		fields = append(fields, envbuild.FieldStartCmd)
	}
	if m.ready_cmd != nil {
		fields = append(fields, envbuild.FieldReadyCmd)
	}
	if m.vcpu != nil {
		fields = append(fields, envbuild.FieldVcpu)
	}
	if m.ram_mb != nil {
		fields = append(fields, envbuild.FieldRAMMB)
	}
	if m.free_disk_size_mb != nil {
		fields = append(fields, envbuild.FieldFreeDiskSizeMB)
	}
	if m.total_disk_size_mb != nil {
		fields = append(fields, envbuild.FieldTotalDiskSizeMB)
	}
	if m.kernel_version != nil {
		fields = append(fields, envbuild.FieldKernelVersion)
	}
	if m.firecracker_version != nil {
		fields = append(fields, envbuild.FieldFirecrackerVersion)
	}
	if m.envd_version != nil {
		fields = append(fields, envbuild.FieldEnvdVersion)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *EnvBuildMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case envbuild.FieldCreatedAt:
		return m.CreatedAt()
	case envbuild.FieldUpdatedAt:
		return m.UpdatedAt()
	case envbuild.FieldFinishedAt:
		return m.FinishedAt()
	case envbuild.FieldEnvID:
		return m.EnvID()
	case envbuild.FieldStatus:
		return m.Status()
	case envbuild.FieldDockerfile:
		return m.Dockerfile()
	case envbuild.FieldStartCmd:
		return m.StartCmd()
	case envbuild.FieldReadyCmd:
		return m.ReadyCmd()
	case envbuild.FieldVcpu:
		return m.Vcpu()
	case envbuild.FieldRAMMB:
		return m.RAMMB()
	case envbuild.FieldFreeDiskSizeMB:
		return m.FreeDiskSizeMB()
	case envbuild.FieldTotalDiskSizeMB:
		return m.TotalDiskSizeMB()
	case envbuild.FieldKernelVersion:
		return m.KernelVersion()
	case envbuild.FieldFirecrackerVersion:
		return m.FirecrackerVersion()
	case envbuild.FieldEnvdVersion:
		return m.EnvdVersion()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *EnvBuildMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case envbuild.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case envbuild.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case envbuild.FieldFinishedAt:
		return m.OldFinishedAt(ctx)
	case envbuild.FieldEnvID:
		return m.OldEnvID(ctx)
	case envbuild.FieldStatus:
		return m.OldStatus(ctx)
	case envbuild.FieldDockerfile:
		return m.OldDockerfile(ctx)
	case envbuild.FieldStartCmd:
		return m.OldStartCmd(ctx)
	case envbuild.FieldReadyCmd:
		return m.OldReadyCmd(ctx)
	case envbuild.FieldVcpu:
		return m.OldVcpu(ctx)
	case envbuild.FieldRAMMB:
		return m.OldRAMMB(ctx)
	case envbuild.FieldFreeDiskSizeMB:
		return m.OldFreeDiskSizeMB(ctx)
	case envbuild.FieldTotalDiskSizeMB:
		return m.OldTotalDiskSizeMB(ctx)
	case envbuild.FieldKernelVersion:
		return m.OldKernelVersion(ctx)
	case envbuild.FieldFirecrackerVersion:
		return m.OldFirecrackerVersion(ctx)
	case envbuild.FieldEnvdVersion:
		return m.OldEnvdVersion(ctx)
	}
	return nil, fmt.Errorf("unknown EnvBuild field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *EnvBuildMutation) SetField(name string, value ent.Value) error {
	switch name {
	case envbuild.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case envbuild.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case envbuild.FieldFinishedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFinishedAt(v)
		return nil
	case envbuild.FieldEnvID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEnvID(v)
		return nil
	case envbuild.FieldStatus:
		v, ok := value.(envbuild.Status)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	case envbuild.FieldDockerfile:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDockerfile(v)
		return nil
	case envbuild.FieldStartCmd:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStartCmd(v)
		return nil
	case envbuild.FieldReadyCmd:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetReadyCmd(v)
		return nil
	case envbuild.FieldVcpu:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetVcpu(v)
		return nil
	case envbuild.FieldRAMMB:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRAMMB(v)
		return nil
	case envbuild.FieldFreeDiskSizeMB:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFreeDiskSizeMB(v)
		return nil
	case envbuild.FieldTotalDiskSizeMB:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTotalDiskSizeMB(v)
		return nil
	case envbuild.FieldKernelVersion:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetKernelVersion(v)
		return nil
	case envbuild.FieldFirecrackerVersion:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFirecrackerVersion(v)
		return nil
	case envbuild.FieldEnvdVersion:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEnvdVersion(v)
		return nil
	}
	return fmt.Errorf("unknown EnvBuild field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *EnvBuildMutation) AddedFields() []string {
	var fields []string
	if m.addvcpu != nil {
		fields = append(fields, envbuild.FieldVcpu)
	}
	if m.addram_mb != nil {
		fields = append(fields, envbuild.FieldRAMMB)
	}
	if m.addfree_disk_size_mb != nil {
		fields = append(fields, envbuild.FieldFreeDiskSizeMB)
	}
	if m.addtotal_disk_size_mb != nil {
		fields = append(fields, envbuild.FieldTotalDiskSizeMB)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *EnvBuildMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case envbuild.FieldVcpu:
		return m.AddedVcpu()
	case envbuild.FieldRAMMB:
		return m.AddedRAMMB()
	case envbuild.FieldFreeDiskSizeMB:
		return m.AddedFreeDiskSizeMB()
	case envbuild.FieldTotalDiskSizeMB:
		return m.AddedTotalDiskSizeMB()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *EnvBuildMutation) AddField(name string, value ent.Value) error {
	switch name {
	case envbuild.FieldVcpu:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddVcpu(v)
		return nil
	case envbuild.FieldRAMMB:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddRAMMB(v)
		return nil
	case envbuild.FieldFreeDiskSizeMB:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddFreeDiskSizeMB(v)
		return nil
	case envbuild.FieldTotalDiskSizeMB:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddTotalDiskSizeMB(v)
		return nil
	}
	return fmt.Errorf("unknown EnvBuild numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *EnvBuildMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(envbuild.FieldFinishedAt) {
		fields = append(fields, envbuild.FieldFinishedAt)
	}
	if m.FieldCleared(envbuild.FieldEnvID) {
		fields = append(fields, envbuild.FieldEnvID)
	}
	if m.FieldCleared(envbuild.FieldDockerfile) {
		fields = append(fields, envbuild.FieldDockerfile)
	}
	if m.FieldCleared(envbuild.FieldStartCmd) {
		fields = append(fields, envbuild.FieldStartCmd)
	}
	if m.FieldCleared(envbuild.FieldReadyCmd) {
		fields = append(fields, envbuild.FieldReadyCmd)
	}
	if m.FieldCleared(envbuild.FieldTotalDiskSizeMB) {
		fields = append(fields, envbuild.FieldTotalDiskSizeMB)
	}
	if m.FieldCleared(envbuild.FieldEnvdVersion) {
		fields = append(fields, envbuild.FieldEnvdVersion)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *EnvBuildMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *EnvBuildMutation) ClearField(name string) error {
	switch name {
	case envbuild.FieldFinishedAt:
		m.ClearFinishedAt()
		return nil
	case envbuild.FieldEnvID:
		m.ClearEnvID()
		return nil
	case envbuild.FieldDockerfile:
		m.ClearDockerfile()
		return nil
	case envbuild.FieldStartCmd:
		m.ClearStartCmd()
		return nil
	case envbuild.FieldReadyCmd:
		m.ClearReadyCmd()
		return nil
	case envbuild.FieldTotalDiskSizeMB:
		m.ClearTotalDiskSizeMB()
		return nil
	case envbuild.FieldEnvdVersion:
		m.ClearEnvdVersion()
		return nil
	}
	return fmt.Errorf("unknown EnvBuild nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *EnvBuildMutation) ResetField(name string) error {
	switch name {
	case envbuild.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case envbuild.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case envbuild.FieldFinishedAt:
		m.ResetFinishedAt()
		return nil
	case envbuild.FieldEnvID:
		m.ResetEnvID()
		return nil
	case envbuild.FieldStatus:
		m.ResetStatus()
		return nil
	case envbuild.FieldDockerfile:
		m.ResetDockerfile()
		return nil
	case envbuild.FieldStartCmd:
		m.ResetStartCmd()
		return nil
	case envbuild.FieldReadyCmd:
		m.ResetReadyCmd()
		return nil
	case envbuild.FieldVcpu:
		m.ResetVcpu()
		return nil
	case envbuild.FieldRAMMB:
		m.ResetRAMMB()
		return nil
	case envbuild.FieldFreeDiskSizeMB:
		m.ResetFreeDiskSizeMB()
		return nil
	case envbuild.FieldTotalDiskSizeMB:
		m.ResetTotalDiskSizeMB()
		return nil
	case envbuild.FieldKernelVersion:
		m.ResetKernelVersion()
		return nil
	case envbuild.FieldFirecrackerVersion:
		m.ResetFirecrackerVersion()
		return nil
	case envbuild.FieldEnvdVersion:
		m.ResetEnvdVersion()
		return nil
	}
	return fmt.Errorf("unknown EnvBuild field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *EnvBuildMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.env != nil {
		edges = append(edges, envbuild.EdgeEnv)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *EnvBuildMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case envbuild.EdgeEnv:
		if id := m.env; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *EnvBuildMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *EnvBuildMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *EnvBuildMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.clearedenv {
		edges = append(edges, envbuild.EdgeEnv)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *EnvBuildMutation) EdgeCleared(name string) bool {
	switch name {
	case envbuild.EdgeEnv:
		return m.clearedenv
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *EnvBuildMutation) ClearEdge(name string) error {
	switch name {
	case envbuild.EdgeEnv:
		m.ClearEnv()
		return nil
	}
	return fmt.Errorf("unknown EnvBuild unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *EnvBuildMutation) ResetEdge(name string) error {
	switch name {
	case envbuild.EdgeEnv:
		m.ResetEnv()
		return nil
	}
	return fmt.Errorf("unknown EnvBuild edge %s", name)
}

// SnapshotMutation represents an operation that mutates the Snapshot nodes in the graph.
type SnapshotMutation struct {
	config
	op                 Op
	typ                string
	id                 *uuid.UUID
	created_at         *time.Time
	base_env_id        *string
	sandbox_id         *string
	metadata           *map[string]string
	sandbox_started_at *time.Time
	env_secure         *bool
	clearedFields      map[string]struct{}
	env                *string
	clearedenv         bool
	done               bool
	oldValue           func(context.Context) (*Snapshot, error)
	predicates         []predicate.Snapshot
}

var _ ent.Mutation = (*SnapshotMutation)(nil)

// snapshotOption allows management of the mutation configuration using functional options.
type snapshotOption func(*SnapshotMutation)

// newSnapshotMutation creates new mutation for the Snapshot entity.
func newSnapshotMutation(c config, op Op, opts ...snapshotOption) *SnapshotMutation {
	m := &SnapshotMutation{
		config:        c,
		op:            op,
		typ:           TypeSnapshot,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withSnapshotID sets the ID field of the mutation.
func withSnapshotID(id uuid.UUID) snapshotOption {
	return func(m *SnapshotMutation) {
		var (
			err   error
			once  sync.Once
			value *Snapshot
		)
		m.oldValue = func(ctx context.Context) (*Snapshot, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Snapshot.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withSnapshot sets the old Snapshot of the mutation.
func withSnapshot(node *Snapshot) snapshotOption {
	return func(m *SnapshotMutation) {
		m.oldValue = func(context.Context) (*Snapshot, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m SnapshotMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m SnapshotMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("models: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Snapshot entities.
func (m *SnapshotMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *SnapshotMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *SnapshotMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Snapshot.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedAt sets the "created_at" field.
func (m *SnapshotMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *SnapshotMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the Snapshot entity.
// If the Snapshot object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SnapshotMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *SnapshotMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetBaseEnvID sets the "base_env_id" field.
func (m *SnapshotMutation) SetBaseEnvID(s string) {
	m.base_env_id = &s
}

// BaseEnvID returns the value of the "base_env_id" field in the mutation.
func (m *SnapshotMutation) BaseEnvID() (r string, exists bool) {
	v := m.base_env_id
	if v == nil {
		return
	}
	return *v, true
}

// OldBaseEnvID returns the old "base_env_id" field's value of the Snapshot entity.
// If the Snapshot object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SnapshotMutation) OldBaseEnvID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldBaseEnvID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldBaseEnvID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldBaseEnvID: %w", err)
	}
	return oldValue.BaseEnvID, nil
}

// ResetBaseEnvID resets all changes to the "base_env_id" field.
func (m *SnapshotMutation) ResetBaseEnvID() {
	m.base_env_id = nil
}

// SetEnvID sets the "env_id" field.
func (m *SnapshotMutation) SetEnvID(s string) {
	m.env = &s
}

// EnvID returns the value of the "env_id" field in the mutation.
func (m *SnapshotMutation) EnvID() (r string, exists bool) {
	v := m.env
	if v == nil {
		return
	}
	return *v, true
}

// OldEnvID returns the old "env_id" field's value of the Snapshot entity.
// If the Snapshot object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SnapshotMutation) OldEnvID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEnvID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEnvID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEnvID: %w", err)
	}
	return oldValue.EnvID, nil
}

// ResetEnvID resets all changes to the "env_id" field.
func (m *SnapshotMutation) ResetEnvID() {
	m.env = nil
}

// SetSandboxID sets the "sandbox_id" field.
func (m *SnapshotMutation) SetSandboxID(s string) {
	m.sandbox_id = &s
}

// SandboxID returns the value of the "sandbox_id" field in the mutation.
func (m *SnapshotMutation) SandboxID() (r string, exists bool) {
	v := m.sandbox_id
	if v == nil {
		return
	}
	return *v, true
}

// OldSandboxID returns the old "sandbox_id" field's value of the Snapshot entity.
// If the Snapshot object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SnapshotMutation) OldSandboxID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldSandboxID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldSandboxID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldSandboxID: %w", err)
	}
	return oldValue.SandboxID, nil
}

// ResetSandboxID resets all changes to the "sandbox_id" field.
func (m *SnapshotMutation) ResetSandboxID() {
	m.sandbox_id = nil
}

// SetMetadata sets the "metadata" field.
func (m *SnapshotMutation) SetMetadata(value map[string]string) {
	m.metadata = &value
}

// Metadata returns the value of the "metadata" field in the mutation.
func (m *SnapshotMutation) Metadata() (r map[string]string, exists bool) {
	v := m.metadata
	if v == nil {
		return
	}
	return *v, true
}

// OldMetadata returns the old "metadata" field's value of the Snapshot entity.
// If the Snapshot object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SnapshotMutation) OldMetadata(ctx context.Context) (v map[string]string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMetadata is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMetadata requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMetadata: %w", err)
	}
	return oldValue.Metadata, nil
}

// ResetMetadata resets all changes to the "metadata" field.
func (m *SnapshotMutation) ResetMetadata() {
	m.metadata = nil
}

// SetSandboxStartedAt sets the "sandbox_started_at" field.
func (m *SnapshotMutation) SetSandboxStartedAt(t time.Time) {
	m.sandbox_started_at = &t
}

// SandboxStartedAt returns the value of the "sandbox_started_at" field in the mutation.
func (m *SnapshotMutation) SandboxStartedAt() (r time.Time, exists bool) {
	v := m.sandbox_started_at
	if v == nil {
		return
	}
	return *v, true
}

// OldSandboxStartedAt returns the old "sandbox_started_at" field's value of the Snapshot entity.
// If the Snapshot object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SnapshotMutation) OldSandboxStartedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldSandboxStartedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldSandboxStartedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldSandboxStartedAt: %w", err)
	}
	return oldValue.SandboxStartedAt, nil
}

// ResetSandboxStartedAt resets all changes to the "sandbox_started_at" field.
func (m *SnapshotMutation) ResetSandboxStartedAt() {
	m.sandbox_started_at = nil
}

// SetEnvSecure sets the "env_secure" field.
func (m *SnapshotMutation) SetEnvSecure(b bool) {
	m.env_secure = &b
}

// EnvSecure returns the value of the "env_secure" field in the mutation.
func (m *SnapshotMutation) EnvSecure() (r bool, exists bool) {
	v := m.env_secure
	if v == nil {
		return
	}
	return *v, true
}

// OldEnvSecure returns the old "env_secure" field's value of the Snapshot entity.
// If the Snapshot object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SnapshotMutation) OldEnvSecure(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEnvSecure is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEnvSecure requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEnvSecure: %w", err)
	}
	return oldValue.EnvSecure, nil
}

// ResetEnvSecure resets all changes to the "env_secure" field.
func (m *SnapshotMutation) ResetEnvSecure() {
	m.env_secure = nil
}

// ClearEnv clears the "env" edge to the Env entity.
func (m *SnapshotMutation) ClearEnv() {
	m.clearedenv = true
	m.clearedFields[snapshot.FieldEnvID] = struct{}{}
}

// EnvCleared reports if the "env" edge to the Env entity was cleared.
func (m *SnapshotMutation) EnvCleared() bool {
	return m.clearedenv
}

// EnvIDs returns the "env" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// EnvID instead. It exists only for internal usage by the builders.
func (m *SnapshotMutation) EnvIDs() (ids []string) {
	if id := m.env; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetEnv resets all changes to the "env" edge.
func (m *SnapshotMutation) ResetEnv() {
	m.env = nil
	m.clearedenv = false
}

// Where appends a list predicates to the SnapshotMutation builder.
func (m *SnapshotMutation) Where(ps ...predicate.Snapshot) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the SnapshotMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *SnapshotMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Snapshot, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *SnapshotMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *SnapshotMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Snapshot).
func (m *SnapshotMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *SnapshotMutation) Fields() []string {
	fields := make([]string, 0, 7)
	if m.created_at != nil {
		fields = append(fields, snapshot.FieldCreatedAt)
	}
	if m.base_env_id != nil {
		fields = append(fields, snapshot.FieldBaseEnvID)
	}
	if m.env != nil {
		fields = append(fields, snapshot.FieldEnvID)
	}
	if m.sandbox_id != nil {
		fields = append(fields, snapshot.FieldSandboxID)
	}
	if m.metadata != nil {
		fields = append(fields, snapshot.FieldMetadata)
	}
	if m.sandbox_started_at != nil {
		fields = append(fields, snapshot.FieldSandboxStartedAt)
	}
	if m.env_secure != nil {
		fields = append(fields, snapshot.FieldEnvSecure)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *SnapshotMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case snapshot.FieldCreatedAt:
		return m.CreatedAt()
	case snapshot.FieldBaseEnvID:
		return m.BaseEnvID()
	case snapshot.FieldEnvID:
		return m.EnvID()
	case snapshot.FieldSandboxID:
		return m.SandboxID()
	case snapshot.FieldMetadata:
		return m.Metadata()
	case snapshot.FieldSandboxStartedAt:
		return m.SandboxStartedAt()
	case snapshot.FieldEnvSecure:
		return m.EnvSecure()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *SnapshotMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case snapshot.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case snapshot.FieldBaseEnvID:
		return m.OldBaseEnvID(ctx)
	case snapshot.FieldEnvID:
		return m.OldEnvID(ctx)
	case snapshot.FieldSandboxID:
		return m.OldSandboxID(ctx)
	case snapshot.FieldMetadata:
		return m.OldMetadata(ctx)
	case snapshot.FieldSandboxStartedAt:
		return m.OldSandboxStartedAt(ctx)
	case snapshot.FieldEnvSecure:
		return m.OldEnvSecure(ctx)
	}
	return nil, fmt.Errorf("unknown Snapshot field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *SnapshotMutation) SetField(name string, value ent.Value) error {
	switch name {
	case snapshot.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case snapshot.FieldBaseEnvID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetBaseEnvID(v)
		return nil
	case snapshot.FieldEnvID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEnvID(v)
		return nil
	case snapshot.FieldSandboxID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetSandboxID(v)
		return nil
	case snapshot.FieldMetadata:
		v, ok := value.(map[string]string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMetadata(v)
		return nil
	case snapshot.FieldSandboxStartedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetSandboxStartedAt(v)
		return nil
	case snapshot.FieldEnvSecure:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEnvSecure(v)
		return nil
	}
	return fmt.Errorf("unknown Snapshot field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *SnapshotMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *SnapshotMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *SnapshotMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Snapshot numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *SnapshotMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *SnapshotMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *SnapshotMutation) ClearField(name string) error {
	return fmt.Errorf("unknown Snapshot nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *SnapshotMutation) ResetField(name string) error {
	switch name {
	case snapshot.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case snapshot.FieldBaseEnvID:
		m.ResetBaseEnvID()
		return nil
	case snapshot.FieldEnvID:
		m.ResetEnvID()
		return nil
	case snapshot.FieldSandboxID:
		m.ResetSandboxID()
		return nil
	case snapshot.FieldMetadata:
		m.ResetMetadata()
		return nil
	case snapshot.FieldSandboxStartedAt:
		m.ResetSandboxStartedAt()
		return nil
	case snapshot.FieldEnvSecure:
		m.ResetEnvSecure()
		return nil
	}
	return fmt.Errorf("unknown Snapshot field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *SnapshotMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.env != nil {
		edges = append(edges, snapshot.EdgeEnv)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *SnapshotMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case snapshot.EdgeEnv:
		if id := m.env; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *SnapshotMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *SnapshotMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *SnapshotMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.clearedenv {
		edges = append(edges, snapshot.EdgeEnv)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *SnapshotMutation) EdgeCleared(name string) bool {
	switch name {
	case snapshot.EdgeEnv:
		return m.clearedenv
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *SnapshotMutation) ClearEdge(name string) error {
	switch name {
	case snapshot.EdgeEnv:
		m.ClearEnv()
		return nil
	}
	return fmt.Errorf("unknown Snapshot unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *SnapshotMutation) ResetEdge(name string) error {
	switch name {
	case snapshot.EdgeEnv:
		m.ResetEnv()
		return nil
	}
	return fmt.Errorf("unknown Snapshot edge %s", name)
}

// TeamMutation represents an operation that mutates the Team nodes in the graph.
type TeamMutation struct {
	config
	op                   Op
	typ                  string
	id                   *uuid.UUID
	created_at           *time.Time
	is_banned            *bool
	is_blocked           *bool
	blocked_reason       *string
	name                 *string
	email                *string
	clearedFields        map[string]struct{}
	users                map[uuid.UUID]struct{}
	removedusers         map[uuid.UUID]struct{}
	clearedusers         bool
	team_api_keys        map[uuid.UUID]struct{}
	removedteam_api_keys map[uuid.UUID]struct{}
	clearedteam_api_keys bool
	team_tier            *string
	clearedteam_tier     bool
	envs                 map[string]struct{}
	removedenvs          map[string]struct{}
	clearedenvs          bool
	users_teams          map[int]struct{}
	removedusers_teams   map[int]struct{}
	clearedusers_teams   bool
	done                 bool
	oldValue             func(context.Context) (*Team, error)
	predicates           []predicate.Team
}

var _ ent.Mutation = (*TeamMutation)(nil)

// teamOption allows management of the mutation configuration using functional options.
type teamOption func(*TeamMutation)

// newTeamMutation creates new mutation for the Team entity.
func newTeamMutation(c config, op Op, opts ...teamOption) *TeamMutation {
	m := &TeamMutation{
		config:        c,
		op:            op,
		typ:           TypeTeam,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withTeamID sets the ID field of the mutation.
func withTeamID(id uuid.UUID) teamOption {
	return func(m *TeamMutation) {
		var (
			err   error
			once  sync.Once
			value *Team
		)
		m.oldValue = func(ctx context.Context) (*Team, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Team.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withTeam sets the old Team of the mutation.
func withTeam(node *Team) teamOption {
	return func(m *TeamMutation) {
		m.oldValue = func(context.Context) (*Team, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m TeamMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m TeamMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("models: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Team entities.
func (m *TeamMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *TeamMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *TeamMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Team.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedAt sets the "created_at" field.
func (m *TeamMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *TeamMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the Team entity.
// If the Team object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TeamMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *TeamMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetIsBanned sets the "is_banned" field.
func (m *TeamMutation) SetIsBanned(b bool) {
	m.is_banned = &b
}

// IsBanned returns the value of the "is_banned" field in the mutation.
func (m *TeamMutation) IsBanned() (r bool, exists bool) {
	v := m.is_banned
	if v == nil {
		return
	}
	return *v, true
}

// OldIsBanned returns the old "is_banned" field's value of the Team entity.
// If the Team object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TeamMutation) OldIsBanned(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIsBanned is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIsBanned requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIsBanned: %w", err)
	}
	return oldValue.IsBanned, nil
}

// ClearIsBanned clears the value of the "is_banned" field.
func (m *TeamMutation) ClearIsBanned() {
	m.is_banned = nil
	m.clearedFields[team.FieldIsBanned] = struct{}{}
}

// IsBannedCleared returns if the "is_banned" field was cleared in this mutation.
func (m *TeamMutation) IsBannedCleared() bool {
	_, ok := m.clearedFields[team.FieldIsBanned]
	return ok
}

// ResetIsBanned resets all changes to the "is_banned" field.
func (m *TeamMutation) ResetIsBanned() {
	m.is_banned = nil
	delete(m.clearedFields, team.FieldIsBanned)
}

// SetIsBlocked sets the "is_blocked" field.
func (m *TeamMutation) SetIsBlocked(b bool) {
	m.is_blocked = &b
}

// IsBlocked returns the value of the "is_blocked" field in the mutation.
func (m *TeamMutation) IsBlocked() (r bool, exists bool) {
	v := m.is_blocked
	if v == nil {
		return
	}
	return *v, true
}

// OldIsBlocked returns the old "is_blocked" field's value of the Team entity.
// If the Team object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TeamMutation) OldIsBlocked(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIsBlocked is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIsBlocked requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIsBlocked: %w", err)
	}
	return oldValue.IsBlocked, nil
}

// ClearIsBlocked clears the value of the "is_blocked" field.
func (m *TeamMutation) ClearIsBlocked() {
	m.is_blocked = nil
	m.clearedFields[team.FieldIsBlocked] = struct{}{}
}

// IsBlockedCleared returns if the "is_blocked" field was cleared in this mutation.
func (m *TeamMutation) IsBlockedCleared() bool {
	_, ok := m.clearedFields[team.FieldIsBlocked]
	return ok
}

// ResetIsBlocked resets all changes to the "is_blocked" field.
func (m *TeamMutation) ResetIsBlocked() {
	m.is_blocked = nil
	delete(m.clearedFields, team.FieldIsBlocked)
}

// SetBlockedReason sets the "blocked_reason" field.
func (m *TeamMutation) SetBlockedReason(s string) {
	m.blocked_reason = &s
}

// BlockedReason returns the value of the "blocked_reason" field in the mutation.
func (m *TeamMutation) BlockedReason() (r string, exists bool) {
	v := m.blocked_reason
	if v == nil {
		return
	}
	return *v, true
}

// OldBlockedReason returns the old "blocked_reason" field's value of the Team entity.
// If the Team object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TeamMutation) OldBlockedReason(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldBlockedReason is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldBlockedReason requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldBlockedReason: %w", err)
	}
	return oldValue.BlockedReason, nil
}

// ClearBlockedReason clears the value of the "blocked_reason" field.
func (m *TeamMutation) ClearBlockedReason() {
	m.blocked_reason = nil
	m.clearedFields[team.FieldBlockedReason] = struct{}{}
}

// BlockedReasonCleared returns if the "blocked_reason" field was cleared in this mutation.
func (m *TeamMutation) BlockedReasonCleared() bool {
	_, ok := m.clearedFields[team.FieldBlockedReason]
	return ok
}

// ResetBlockedReason resets all changes to the "blocked_reason" field.
func (m *TeamMutation) ResetBlockedReason() {
	m.blocked_reason = nil
	delete(m.clearedFields, team.FieldBlockedReason)
}

// SetName sets the "name" field.
func (m *TeamMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *TeamMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the Team entity.
// If the Team object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TeamMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *TeamMutation) ResetName() {
	m.name = nil
}

// SetTier sets the "tier" field.
func (m *TeamMutation) SetTier(s string) {
	m.team_tier = &s
}

// Tier returns the value of the "tier" field in the mutation.
func (m *TeamMutation) Tier() (r string, exists bool) {
	v := m.team_tier
	if v == nil {
		return
	}
	return *v, true
}

// OldTier returns the old "tier" field's value of the Team entity.
// If the Team object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TeamMutation) OldTier(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTier is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTier requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTier: %w", err)
	}
	return oldValue.Tier, nil
}

// ResetTier resets all changes to the "tier" field.
func (m *TeamMutation) ResetTier() {
	m.team_tier = nil
}

// SetEmail sets the "email" field.
func (m *TeamMutation) SetEmail(s string) {
	m.email = &s
}

// Email returns the value of the "email" field in the mutation.
func (m *TeamMutation) Email() (r string, exists bool) {
	v := m.email
	if v == nil {
		return
	}
	return *v, true
}

// OldEmail returns the old "email" field's value of the Team entity.
// If the Team object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TeamMutation) OldEmail(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEmail is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEmail requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEmail: %w", err)
	}
	return oldValue.Email, nil
}

// ResetEmail resets all changes to the "email" field.
func (m *TeamMutation) ResetEmail() {
	m.email = nil
}

// AddUserIDs adds the "users" edge to the User entity by ids.
func (m *TeamMutation) AddUserIDs(ids ...uuid.UUID) {
	if m.users == nil {
		m.users = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		m.users[ids[i]] = struct{}{}
	}
}

// ClearUsers clears the "users" edge to the User entity.
func (m *TeamMutation) ClearUsers() {
	m.clearedusers = true
}

// UsersCleared reports if the "users" edge to the User entity was cleared.
func (m *TeamMutation) UsersCleared() bool {
	return m.clearedusers
}

// RemoveUserIDs removes the "users" edge to the User entity by IDs.
func (m *TeamMutation) RemoveUserIDs(ids ...uuid.UUID) {
	if m.removedusers == nil {
		m.removedusers = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		delete(m.users, ids[i])
		m.removedusers[ids[i]] = struct{}{}
	}
}

// RemovedUsers returns the removed IDs of the "users" edge to the User entity.
func (m *TeamMutation) RemovedUsersIDs() (ids []uuid.UUID) {
	for id := range m.removedusers {
		ids = append(ids, id)
	}
	return
}

// UsersIDs returns the "users" edge IDs in the mutation.
func (m *TeamMutation) UsersIDs() (ids []uuid.UUID) {
	for id := range m.users {
		ids = append(ids, id)
	}
	return
}

// ResetUsers resets all changes to the "users" edge.
func (m *TeamMutation) ResetUsers() {
	m.users = nil
	m.clearedusers = false
	m.removedusers = nil
}

// AddTeamAPIKeyIDs adds the "team_api_keys" edge to the TeamAPIKey entity by ids.
func (m *TeamMutation) AddTeamAPIKeyIDs(ids ...uuid.UUID) {
	if m.team_api_keys == nil {
		m.team_api_keys = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		m.team_api_keys[ids[i]] = struct{}{}
	}
}

// ClearTeamAPIKeys clears the "team_api_keys" edge to the TeamAPIKey entity.
func (m *TeamMutation) ClearTeamAPIKeys() {
	m.clearedteam_api_keys = true
}

// TeamAPIKeysCleared reports if the "team_api_keys" edge to the TeamAPIKey entity was cleared.
func (m *TeamMutation) TeamAPIKeysCleared() bool {
	return m.clearedteam_api_keys
}

// RemoveTeamAPIKeyIDs removes the "team_api_keys" edge to the TeamAPIKey entity by IDs.
func (m *TeamMutation) RemoveTeamAPIKeyIDs(ids ...uuid.UUID) {
	if m.removedteam_api_keys == nil {
		m.removedteam_api_keys = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		delete(m.team_api_keys, ids[i])
		m.removedteam_api_keys[ids[i]] = struct{}{}
	}
}

// RemovedTeamAPIKeys returns the removed IDs of the "team_api_keys" edge to the TeamAPIKey entity.
func (m *TeamMutation) RemovedTeamAPIKeysIDs() (ids []uuid.UUID) {
	for id := range m.removedteam_api_keys {
		ids = append(ids, id)
	}
	return
}

// TeamAPIKeysIDs returns the "team_api_keys" edge IDs in the mutation.
func (m *TeamMutation) TeamAPIKeysIDs() (ids []uuid.UUID) {
	for id := range m.team_api_keys {
		ids = append(ids, id)
	}
	return
}

// ResetTeamAPIKeys resets all changes to the "team_api_keys" edge.
func (m *TeamMutation) ResetTeamAPIKeys() {
	m.team_api_keys = nil
	m.clearedteam_api_keys = false
	m.removedteam_api_keys = nil
}

// SetTeamTierID sets the "team_tier" edge to the Tier entity by id.
func (m *TeamMutation) SetTeamTierID(id string) {
	m.team_tier = &id
}

// ClearTeamTier clears the "team_tier" edge to the Tier entity.
func (m *TeamMutation) ClearTeamTier() {
	m.clearedteam_tier = true
	m.clearedFields[team.FieldTier] = struct{}{}
}

// TeamTierCleared reports if the "team_tier" edge to the Tier entity was cleared.
func (m *TeamMutation) TeamTierCleared() bool {
	return m.clearedteam_tier
}

// TeamTierID returns the "team_tier" edge ID in the mutation.
func (m *TeamMutation) TeamTierID() (id string, exists bool) {
	if m.team_tier != nil {
		return *m.team_tier, true
	}
	return
}

// TeamTierIDs returns the "team_tier" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// TeamTierID instead. It exists only for internal usage by the builders.
func (m *TeamMutation) TeamTierIDs() (ids []string) {
	if id := m.team_tier; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetTeamTier resets all changes to the "team_tier" edge.
func (m *TeamMutation) ResetTeamTier() {
	m.team_tier = nil
	m.clearedteam_tier = false
}

// AddEnvIDs adds the "envs" edge to the Env entity by ids.
func (m *TeamMutation) AddEnvIDs(ids ...string) {
	if m.envs == nil {
		m.envs = make(map[string]struct{})
	}
	for i := range ids {
		m.envs[ids[i]] = struct{}{}
	}
}

// ClearEnvs clears the "envs" edge to the Env entity.
func (m *TeamMutation) ClearEnvs() {
	m.clearedenvs = true
}

// EnvsCleared reports if the "envs" edge to the Env entity was cleared.
func (m *TeamMutation) EnvsCleared() bool {
	return m.clearedenvs
}

// RemoveEnvIDs removes the "envs" edge to the Env entity by IDs.
func (m *TeamMutation) RemoveEnvIDs(ids ...string) {
	if m.removedenvs == nil {
		m.removedenvs = make(map[string]struct{})
	}
	for i := range ids {
		delete(m.envs, ids[i])
		m.removedenvs[ids[i]] = struct{}{}
	}
}

// RemovedEnvs returns the removed IDs of the "envs" edge to the Env entity.
func (m *TeamMutation) RemovedEnvsIDs() (ids []string) {
	for id := range m.removedenvs {
		ids = append(ids, id)
	}
	return
}

// EnvsIDs returns the "envs" edge IDs in the mutation.
func (m *TeamMutation) EnvsIDs() (ids []string) {
	for id := range m.envs {
		ids = append(ids, id)
	}
	return
}

// ResetEnvs resets all changes to the "envs" edge.
func (m *TeamMutation) ResetEnvs() {
	m.envs = nil
	m.clearedenvs = false
	m.removedenvs = nil
}

// AddUsersTeamIDs adds the "users_teams" edge to the UsersTeams entity by ids.
func (m *TeamMutation) AddUsersTeamIDs(ids ...int) {
	if m.users_teams == nil {
		m.users_teams = make(map[int]struct{})
	}
	for i := range ids {
		m.users_teams[ids[i]] = struct{}{}
	}
}

// ClearUsersTeams clears the "users_teams" edge to the UsersTeams entity.
func (m *TeamMutation) ClearUsersTeams() {
	m.clearedusers_teams = true
}

// UsersTeamsCleared reports if the "users_teams" edge to the UsersTeams entity was cleared.
func (m *TeamMutation) UsersTeamsCleared() bool {
	return m.clearedusers_teams
}

// RemoveUsersTeamIDs removes the "users_teams" edge to the UsersTeams entity by IDs.
func (m *TeamMutation) RemoveUsersTeamIDs(ids ...int) {
	if m.removedusers_teams == nil {
		m.removedusers_teams = make(map[int]struct{})
	}
	for i := range ids {
		delete(m.users_teams, ids[i])
		m.removedusers_teams[ids[i]] = struct{}{}
	}
}

// RemovedUsersTeams returns the removed IDs of the "users_teams" edge to the UsersTeams entity.
func (m *TeamMutation) RemovedUsersTeamsIDs() (ids []int) {
	for id := range m.removedusers_teams {
		ids = append(ids, id)
	}
	return
}

// UsersTeamsIDs returns the "users_teams" edge IDs in the mutation.
func (m *TeamMutation) UsersTeamsIDs() (ids []int) {
	for id := range m.users_teams {
		ids = append(ids, id)
	}
	return
}

// ResetUsersTeams resets all changes to the "users_teams" edge.
func (m *TeamMutation) ResetUsersTeams() {
	m.users_teams = nil
	m.clearedusers_teams = false
	m.removedusers_teams = nil
}

// Where appends a list predicates to the TeamMutation builder.
func (m *TeamMutation) Where(ps ...predicate.Team) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the TeamMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *TeamMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Team, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *TeamMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *TeamMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Team).
func (m *TeamMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *TeamMutation) Fields() []string {
	fields := make([]string, 0, 7)
	if m.created_at != nil {
		fields = append(fields, team.FieldCreatedAt)
	}
	if m.is_banned != nil {
		fields = append(fields, team.FieldIsBanned)
	}
	if m.is_blocked != nil {
		fields = append(fields, team.FieldIsBlocked)
	}
	if m.blocked_reason != nil {
		fields = append(fields, team.FieldBlockedReason)
	}
	if m.name != nil {
		fields = append(fields, team.FieldName)
	}
	if m.team_tier != nil {
		fields = append(fields, team.FieldTier)
	}
	if m.email != nil {
		fields = append(fields, team.FieldEmail)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *TeamMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case team.FieldCreatedAt:
		return m.CreatedAt()
	case team.FieldIsBanned:
		return m.IsBanned()
	case team.FieldIsBlocked:
		return m.IsBlocked()
	case team.FieldBlockedReason:
		return m.BlockedReason()
	case team.FieldName:
		return m.Name()
	case team.FieldTier:
		return m.Tier()
	case team.FieldEmail:
		return m.Email()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *TeamMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case team.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case team.FieldIsBanned:
		return m.OldIsBanned(ctx)
	case team.FieldIsBlocked:
		return m.OldIsBlocked(ctx)
	case team.FieldBlockedReason:
		return m.OldBlockedReason(ctx)
	case team.FieldName:
		return m.OldName(ctx)
	case team.FieldTier:
		return m.OldTier(ctx)
	case team.FieldEmail:
		return m.OldEmail(ctx)
	}
	return nil, fmt.Errorf("unknown Team field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *TeamMutation) SetField(name string, value ent.Value) error {
	switch name {
	case team.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case team.FieldIsBanned:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIsBanned(v)
		return nil
	case team.FieldIsBlocked:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIsBlocked(v)
		return nil
	case team.FieldBlockedReason:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetBlockedReason(v)
		return nil
	case team.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case team.FieldTier:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTier(v)
		return nil
	case team.FieldEmail:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEmail(v)
		return nil
	}
	return fmt.Errorf("unknown Team field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *TeamMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *TeamMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *TeamMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Team numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *TeamMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(team.FieldIsBanned) {
		fields = append(fields, team.FieldIsBanned)
	}
	if m.FieldCleared(team.FieldIsBlocked) {
		fields = append(fields, team.FieldIsBlocked)
	}
	if m.FieldCleared(team.FieldBlockedReason) {
		fields = append(fields, team.FieldBlockedReason)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *TeamMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *TeamMutation) ClearField(name string) error {
	switch name {
	case team.FieldIsBanned:
		m.ClearIsBanned()
		return nil
	case team.FieldIsBlocked:
		m.ClearIsBlocked()
		return nil
	case team.FieldBlockedReason:
		m.ClearBlockedReason()
		return nil
	}
	return fmt.Errorf("unknown Team nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *TeamMutation) ResetField(name string) error {
	switch name {
	case team.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case team.FieldIsBanned:
		m.ResetIsBanned()
		return nil
	case team.FieldIsBlocked:
		m.ResetIsBlocked()
		return nil
	case team.FieldBlockedReason:
		m.ResetBlockedReason()
		return nil
	case team.FieldName:
		m.ResetName()
		return nil
	case team.FieldTier:
		m.ResetTier()
		return nil
	case team.FieldEmail:
		m.ResetEmail()
		return nil
	}
	return fmt.Errorf("unknown Team field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *TeamMutation) AddedEdges() []string {
	edges := make([]string, 0, 5)
	if m.users != nil {
		edges = append(edges, team.EdgeUsers)
	}
	if m.team_api_keys != nil {
		edges = append(edges, team.EdgeTeamAPIKeys)
	}
	if m.team_tier != nil {
		edges = append(edges, team.EdgeTeamTier)
	}
	if m.envs != nil {
		edges = append(edges, team.EdgeEnvs)
	}
	if m.users_teams != nil {
		edges = append(edges, team.EdgeUsersTeams)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *TeamMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case team.EdgeUsers:
		ids := make([]ent.Value, 0, len(m.users))
		for id := range m.users {
			ids = append(ids, id)
		}
		return ids
	case team.EdgeTeamAPIKeys:
		ids := make([]ent.Value, 0, len(m.team_api_keys))
		for id := range m.team_api_keys {
			ids = append(ids, id)
		}
		return ids
	case team.EdgeTeamTier:
		if id := m.team_tier; id != nil {
			return []ent.Value{*id}
		}
	case team.EdgeEnvs:
		ids := make([]ent.Value, 0, len(m.envs))
		for id := range m.envs {
			ids = append(ids, id)
		}
		return ids
	case team.EdgeUsersTeams:
		ids := make([]ent.Value, 0, len(m.users_teams))
		for id := range m.users_teams {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *TeamMutation) RemovedEdges() []string {
	edges := make([]string, 0, 5)
	if m.removedusers != nil {
		edges = append(edges, team.EdgeUsers)
	}
	if m.removedteam_api_keys != nil {
		edges = append(edges, team.EdgeTeamAPIKeys)
	}
	if m.removedenvs != nil {
		edges = append(edges, team.EdgeEnvs)
	}
	if m.removedusers_teams != nil {
		edges = append(edges, team.EdgeUsersTeams)
	}
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *TeamMutation) RemovedIDs(name string) []ent.Value {
	switch name {
	case team.EdgeUsers:
		ids := make([]ent.Value, 0, len(m.removedusers))
		for id := range m.removedusers {
			ids = append(ids, id)
		}
		return ids
	case team.EdgeTeamAPIKeys:
		ids := make([]ent.Value, 0, len(m.removedteam_api_keys))
		for id := range m.removedteam_api_keys {
			ids = append(ids, id)
		}
		return ids
	case team.EdgeEnvs:
		ids := make([]ent.Value, 0, len(m.removedenvs))
		for id := range m.removedenvs {
			ids = append(ids, id)
		}
		return ids
	case team.EdgeUsersTeams:
		ids := make([]ent.Value, 0, len(m.removedusers_teams))
		for id := range m.removedusers_teams {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *TeamMutation) ClearedEdges() []string {
	edges := make([]string, 0, 5)
	if m.clearedusers {
		edges = append(edges, team.EdgeUsers)
	}
	if m.clearedteam_api_keys {
		edges = append(edges, team.EdgeTeamAPIKeys)
	}
	if m.clearedteam_tier {
		edges = append(edges, team.EdgeTeamTier)
	}
	if m.clearedenvs {
		edges = append(edges, team.EdgeEnvs)
	}
	if m.clearedusers_teams {
		edges = append(edges, team.EdgeUsersTeams)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *TeamMutation) EdgeCleared(name string) bool {
	switch name {
	case team.EdgeUsers:
		return m.clearedusers
	case team.EdgeTeamAPIKeys:
		return m.clearedteam_api_keys
	case team.EdgeTeamTier:
		return m.clearedteam_tier
	case team.EdgeEnvs:
		return m.clearedenvs
	case team.EdgeUsersTeams:
		return m.clearedusers_teams
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *TeamMutation) ClearEdge(name string) error {
	switch name {
	case team.EdgeTeamTier:
		m.ClearTeamTier()
		return nil
	}
	return fmt.Errorf("unknown Team unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *TeamMutation) ResetEdge(name string) error {
	switch name {
	case team.EdgeUsers:
		m.ResetUsers()
		return nil
	case team.EdgeTeamAPIKeys:
		m.ResetTeamAPIKeys()
		return nil
	case team.EdgeTeamTier:
		m.ResetTeamTier()
		return nil
	case team.EdgeEnvs:
		m.ResetEnvs()
		return nil
	case team.EdgeUsersTeams:
		m.ResetUsersTeams()
		return nil
	}
	return fmt.Errorf("unknown Team edge %s", name)
}

// TeamAPIKeyMutation represents an operation that mutates the TeamAPIKey nodes in the graph.
type TeamAPIKeyMutation struct {
	config
	op                  Op
	typ                 string
	id                  *uuid.UUID
	api_key             *string
	api_key_hash        *string
	api_key_prefix      *string
	api_key_length      *int
	addapi_key_length   *int
	api_key_mask_prefix *string
	api_key_mask_suffix *string
	created_at          *time.Time
	updated_at          *time.Time
	name                *string
	last_used           *time.Time
	clearedFields       map[string]struct{}
	team                *uuid.UUID
	clearedteam         bool
	creator             *uuid.UUID
	clearedcreator      bool
	done                bool
	oldValue            func(context.Context) (*TeamAPIKey, error)
	predicates          []predicate.TeamAPIKey
}

var _ ent.Mutation = (*TeamAPIKeyMutation)(nil)

// teamapikeyOption allows management of the mutation configuration using functional options.
type teamapikeyOption func(*TeamAPIKeyMutation)

// newTeamAPIKeyMutation creates new mutation for the TeamAPIKey entity.
func newTeamAPIKeyMutation(c config, op Op, opts ...teamapikeyOption) *TeamAPIKeyMutation {
	m := &TeamAPIKeyMutation{
		config:        c,
		op:            op,
		typ:           TypeTeamAPIKey,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withTeamAPIKeyID sets the ID field of the mutation.
func withTeamAPIKeyID(id uuid.UUID) teamapikeyOption {
	return func(m *TeamAPIKeyMutation) {
		var (
			err   error
			once  sync.Once
			value *TeamAPIKey
		)
		m.oldValue = func(ctx context.Context) (*TeamAPIKey, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().TeamAPIKey.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withTeamAPIKey sets the old TeamAPIKey of the mutation.
func withTeamAPIKey(node *TeamAPIKey) teamapikeyOption {
	return func(m *TeamAPIKeyMutation) {
		m.oldValue = func(context.Context) (*TeamAPIKey, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m TeamAPIKeyMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m TeamAPIKeyMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("models: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of TeamAPIKey entities.
func (m *TeamAPIKeyMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *TeamAPIKeyMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *TeamAPIKeyMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().TeamAPIKey.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetAPIKey sets the "api_key" field.
func (m *TeamAPIKeyMutation) SetAPIKey(s string) {
	m.api_key = &s
}

// APIKey returns the value of the "api_key" field in the mutation.
func (m *TeamAPIKeyMutation) APIKey() (r string, exists bool) {
	v := m.api_key
	if v == nil {
		return
	}
	return *v, true
}

// OldAPIKey returns the old "api_key" field's value of the TeamAPIKey entity.
// If the TeamAPIKey object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TeamAPIKeyMutation) OldAPIKey(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAPIKey is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAPIKey requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAPIKey: %w", err)
	}
	return oldValue.APIKey, nil
}

// ResetAPIKey resets all changes to the "api_key" field.
func (m *TeamAPIKeyMutation) ResetAPIKey() {
	m.api_key = nil
}

// SetAPIKeyHash sets the "api_key_hash" field.
func (m *TeamAPIKeyMutation) SetAPIKeyHash(s string) {
	m.api_key_hash = &s
}

// APIKeyHash returns the value of the "api_key_hash" field in the mutation.
func (m *TeamAPIKeyMutation) APIKeyHash() (r string, exists bool) {
	v := m.api_key_hash
	if v == nil {
		return
	}
	return *v, true
}

// OldAPIKeyHash returns the old "api_key_hash" field's value of the TeamAPIKey entity.
// If the TeamAPIKey object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TeamAPIKeyMutation) OldAPIKeyHash(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAPIKeyHash is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAPIKeyHash requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAPIKeyHash: %w", err)
	}
	return oldValue.APIKeyHash, nil
}

// ResetAPIKeyHash resets all changes to the "api_key_hash" field.
func (m *TeamAPIKeyMutation) ResetAPIKeyHash() {
	m.api_key_hash = nil
}

// SetAPIKeyPrefix sets the "api_key_prefix" field.
func (m *TeamAPIKeyMutation) SetAPIKeyPrefix(s string) {
	m.api_key_prefix = &s
}

// APIKeyPrefix returns the value of the "api_key_prefix" field in the mutation.
func (m *TeamAPIKeyMutation) APIKeyPrefix() (r string, exists bool) {
	v := m.api_key_prefix
	if v == nil {
		return
	}
	return *v, true
}

// OldAPIKeyPrefix returns the old "api_key_prefix" field's value of the TeamAPIKey entity.
// If the TeamAPIKey object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TeamAPIKeyMutation) OldAPIKeyPrefix(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAPIKeyPrefix is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAPIKeyPrefix requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAPIKeyPrefix: %w", err)
	}
	return oldValue.APIKeyPrefix, nil
}

// ResetAPIKeyPrefix resets all changes to the "api_key_prefix" field.
func (m *TeamAPIKeyMutation) ResetAPIKeyPrefix() {
	m.api_key_prefix = nil
}

// SetAPIKeyLength sets the "api_key_length" field.
func (m *TeamAPIKeyMutation) SetAPIKeyLength(i int) {
	m.api_key_length = &i
	m.addapi_key_length = nil
}

// APIKeyLength returns the value of the "api_key_length" field in the mutation.
func (m *TeamAPIKeyMutation) APIKeyLength() (r int, exists bool) {
	v := m.api_key_length
	if v == nil {
		return
	}
	return *v, true
}

// OldAPIKeyLength returns the old "api_key_length" field's value of the TeamAPIKey entity.
// If the TeamAPIKey object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TeamAPIKeyMutation) OldAPIKeyLength(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAPIKeyLength is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAPIKeyLength requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAPIKeyLength: %w", err)
	}
	return oldValue.APIKeyLength, nil
}

// AddAPIKeyLength adds i to the "api_key_length" field.
func (m *TeamAPIKeyMutation) AddAPIKeyLength(i int) {
	if m.addapi_key_length != nil {
		*m.addapi_key_length += i
	} else {
		m.addapi_key_length = &i
	}
}

// AddedAPIKeyLength returns the value that was added to the "api_key_length" field in this mutation.
func (m *TeamAPIKeyMutation) AddedAPIKeyLength() (r int, exists bool) {
	v := m.addapi_key_length
	if v == nil {
		return
	}
	return *v, true
}

// ResetAPIKeyLength resets all changes to the "api_key_length" field.
func (m *TeamAPIKeyMutation) ResetAPIKeyLength() {
	m.api_key_length = nil
	m.addapi_key_length = nil
}

// SetAPIKeyMaskPrefix sets the "api_key_mask_prefix" field.
func (m *TeamAPIKeyMutation) SetAPIKeyMaskPrefix(s string) {
	m.api_key_mask_prefix = &s
}

// APIKeyMaskPrefix returns the value of the "api_key_mask_prefix" field in the mutation.
func (m *TeamAPIKeyMutation) APIKeyMaskPrefix() (r string, exists bool) {
	v := m.api_key_mask_prefix
	if v == nil {
		return
	}
	return *v, true
}

// OldAPIKeyMaskPrefix returns the old "api_key_mask_prefix" field's value of the TeamAPIKey entity.
// If the TeamAPIKey object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TeamAPIKeyMutation) OldAPIKeyMaskPrefix(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAPIKeyMaskPrefix is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAPIKeyMaskPrefix requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAPIKeyMaskPrefix: %w", err)
	}
	return oldValue.APIKeyMaskPrefix, nil
}

// ResetAPIKeyMaskPrefix resets all changes to the "api_key_mask_prefix" field.
func (m *TeamAPIKeyMutation) ResetAPIKeyMaskPrefix() {
	m.api_key_mask_prefix = nil
}

// SetAPIKeyMaskSuffix sets the "api_key_mask_suffix" field.
func (m *TeamAPIKeyMutation) SetAPIKeyMaskSuffix(s string) {
	m.api_key_mask_suffix = &s
}

// APIKeyMaskSuffix returns the value of the "api_key_mask_suffix" field in the mutation.
func (m *TeamAPIKeyMutation) APIKeyMaskSuffix() (r string, exists bool) {
	v := m.api_key_mask_suffix
	if v == nil {
		return
	}
	return *v, true
}

// OldAPIKeyMaskSuffix returns the old "api_key_mask_suffix" field's value of the TeamAPIKey entity.
// If the TeamAPIKey object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TeamAPIKeyMutation) OldAPIKeyMaskSuffix(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAPIKeyMaskSuffix is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAPIKeyMaskSuffix requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAPIKeyMaskSuffix: %w", err)
	}
	return oldValue.APIKeyMaskSuffix, nil
}

// ResetAPIKeyMaskSuffix resets all changes to the "api_key_mask_suffix" field.
func (m *TeamAPIKeyMutation) ResetAPIKeyMaskSuffix() {
	m.api_key_mask_suffix = nil
}

// SetCreatedAt sets the "created_at" field.
func (m *TeamAPIKeyMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *TeamAPIKeyMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the TeamAPIKey entity.
// If the TeamAPIKey object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TeamAPIKeyMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *TeamAPIKeyMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *TeamAPIKeyMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *TeamAPIKeyMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the TeamAPIKey entity.
// If the TeamAPIKey object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TeamAPIKeyMutation) OldUpdatedAt(ctx context.Context) (v *time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ClearUpdatedAt clears the value of the "updated_at" field.
func (m *TeamAPIKeyMutation) ClearUpdatedAt() {
	m.updated_at = nil
	m.clearedFields[teamapikey.FieldUpdatedAt] = struct{}{}
}

// UpdatedAtCleared returns if the "updated_at" field was cleared in this mutation.
func (m *TeamAPIKeyMutation) UpdatedAtCleared() bool {
	_, ok := m.clearedFields[teamapikey.FieldUpdatedAt]
	return ok
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *TeamAPIKeyMutation) ResetUpdatedAt() {
	m.updated_at = nil
	delete(m.clearedFields, teamapikey.FieldUpdatedAt)
}

// SetTeamID sets the "team_id" field.
func (m *TeamAPIKeyMutation) SetTeamID(u uuid.UUID) {
	m.team = &u
}

// TeamID returns the value of the "team_id" field in the mutation.
func (m *TeamAPIKeyMutation) TeamID() (r uuid.UUID, exists bool) {
	v := m.team
	if v == nil {
		return
	}
	return *v, true
}

// OldTeamID returns the old "team_id" field's value of the TeamAPIKey entity.
// If the TeamAPIKey object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TeamAPIKeyMutation) OldTeamID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTeamID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTeamID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTeamID: %w", err)
	}
	return oldValue.TeamID, nil
}

// ResetTeamID resets all changes to the "team_id" field.
func (m *TeamAPIKeyMutation) ResetTeamID() {
	m.team = nil
}

// SetName sets the "name" field.
func (m *TeamAPIKeyMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *TeamAPIKeyMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the TeamAPIKey entity.
// If the TeamAPIKey object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TeamAPIKeyMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *TeamAPIKeyMutation) ResetName() {
	m.name = nil
}

// SetCreatedBy sets the "created_by" field.
func (m *TeamAPIKeyMutation) SetCreatedBy(u uuid.UUID) {
	m.creator = &u
}

// CreatedBy returns the value of the "created_by" field in the mutation.
func (m *TeamAPIKeyMutation) CreatedBy() (r uuid.UUID, exists bool) {
	v := m.creator
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedBy returns the old "created_by" field's value of the TeamAPIKey entity.
// If the TeamAPIKey object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TeamAPIKeyMutation) OldCreatedBy(ctx context.Context) (v *uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedBy is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedBy requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedBy: %w", err)
	}
	return oldValue.CreatedBy, nil
}

// ClearCreatedBy clears the value of the "created_by" field.
func (m *TeamAPIKeyMutation) ClearCreatedBy() {
	m.creator = nil
	m.clearedFields[teamapikey.FieldCreatedBy] = struct{}{}
}

// CreatedByCleared returns if the "created_by" field was cleared in this mutation.
func (m *TeamAPIKeyMutation) CreatedByCleared() bool {
	_, ok := m.clearedFields[teamapikey.FieldCreatedBy]
	return ok
}

// ResetCreatedBy resets all changes to the "created_by" field.
func (m *TeamAPIKeyMutation) ResetCreatedBy() {
	m.creator = nil
	delete(m.clearedFields, teamapikey.FieldCreatedBy)
}

// SetLastUsed sets the "last_used" field.
func (m *TeamAPIKeyMutation) SetLastUsed(t time.Time) {
	m.last_used = &t
}

// LastUsed returns the value of the "last_used" field in the mutation.
func (m *TeamAPIKeyMutation) LastUsed() (r time.Time, exists bool) {
	v := m.last_used
	if v == nil {
		return
	}
	return *v, true
}

// OldLastUsed returns the old "last_used" field's value of the TeamAPIKey entity.
// If the TeamAPIKey object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TeamAPIKeyMutation) OldLastUsed(ctx context.Context) (v *time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldLastUsed is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldLastUsed requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldLastUsed: %w", err)
	}
	return oldValue.LastUsed, nil
}

// ClearLastUsed clears the value of the "last_used" field.
func (m *TeamAPIKeyMutation) ClearLastUsed() {
	m.last_used = nil
	m.clearedFields[teamapikey.FieldLastUsed] = struct{}{}
}

// LastUsedCleared returns if the "last_used" field was cleared in this mutation.
func (m *TeamAPIKeyMutation) LastUsedCleared() bool {
	_, ok := m.clearedFields[teamapikey.FieldLastUsed]
	return ok
}

// ResetLastUsed resets all changes to the "last_used" field.
func (m *TeamAPIKeyMutation) ResetLastUsed() {
	m.last_used = nil
	delete(m.clearedFields, teamapikey.FieldLastUsed)
}

// ClearTeam clears the "team" edge to the Team entity.
func (m *TeamAPIKeyMutation) ClearTeam() {
	m.clearedteam = true
	m.clearedFields[teamapikey.FieldTeamID] = struct{}{}
}

// TeamCleared reports if the "team" edge to the Team entity was cleared.
func (m *TeamAPIKeyMutation) TeamCleared() bool {
	return m.clearedteam
}

// TeamIDs returns the "team" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// TeamID instead. It exists only for internal usage by the builders.
func (m *TeamAPIKeyMutation) TeamIDs() (ids []uuid.UUID) {
	if id := m.team; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetTeam resets all changes to the "team" edge.
func (m *TeamAPIKeyMutation) ResetTeam() {
	m.team = nil
	m.clearedteam = false
}

// SetCreatorID sets the "creator" edge to the User entity by id.
func (m *TeamAPIKeyMutation) SetCreatorID(id uuid.UUID) {
	m.creator = &id
}

// ClearCreator clears the "creator" edge to the User entity.
func (m *TeamAPIKeyMutation) ClearCreator() {
	m.clearedcreator = true
	m.clearedFields[teamapikey.FieldCreatedBy] = struct{}{}
}

// CreatorCleared reports if the "creator" edge to the User entity was cleared.
func (m *TeamAPIKeyMutation) CreatorCleared() bool {
	return m.CreatedByCleared() || m.clearedcreator
}

// CreatorID returns the "creator" edge ID in the mutation.
func (m *TeamAPIKeyMutation) CreatorID() (id uuid.UUID, exists bool) {
	if m.creator != nil {
		return *m.creator, true
	}
	return
}

// CreatorIDs returns the "creator" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// CreatorID instead. It exists only for internal usage by the builders.
func (m *TeamAPIKeyMutation) CreatorIDs() (ids []uuid.UUID) {
	if id := m.creator; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetCreator resets all changes to the "creator" edge.
func (m *TeamAPIKeyMutation) ResetCreator() {
	m.creator = nil
	m.clearedcreator = false
}

// Where appends a list predicates to the TeamAPIKeyMutation builder.
func (m *TeamAPIKeyMutation) Where(ps ...predicate.TeamAPIKey) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the TeamAPIKeyMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *TeamAPIKeyMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.TeamAPIKey, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *TeamAPIKeyMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *TeamAPIKeyMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (TeamAPIKey).
func (m *TeamAPIKeyMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *TeamAPIKeyMutation) Fields() []string {
	fields := make([]string, 0, 12)
	if m.api_key != nil {
		fields = append(fields, teamapikey.FieldAPIKey)
	}
	if m.api_key_hash != nil {
		fields = append(fields, teamapikey.FieldAPIKeyHash)
	}
	if m.api_key_prefix != nil {
		fields = append(fields, teamapikey.FieldAPIKeyPrefix)
	}
	if m.api_key_length != nil {
		fields = append(fields, teamapikey.FieldAPIKeyLength)
	}
	if m.api_key_mask_prefix != nil {
		fields = append(fields, teamapikey.FieldAPIKeyMaskPrefix)
	}
	if m.api_key_mask_suffix != nil {
		fields = append(fields, teamapikey.FieldAPIKeyMaskSuffix)
	}
	if m.created_at != nil {
		fields = append(fields, teamapikey.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, teamapikey.FieldUpdatedAt)
	}
	if m.team != nil {
		fields = append(fields, teamapikey.FieldTeamID)
	}
	if m.name != nil {
		fields = append(fields, teamapikey.FieldName)
	}
	if m.creator != nil {
		fields = append(fields, teamapikey.FieldCreatedBy)
	}
	if m.last_used != nil {
		fields = append(fields, teamapikey.FieldLastUsed)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *TeamAPIKeyMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case teamapikey.FieldAPIKey:
		return m.APIKey()
	case teamapikey.FieldAPIKeyHash:
		return m.APIKeyHash()
	case teamapikey.FieldAPIKeyPrefix:
		return m.APIKeyPrefix()
	case teamapikey.FieldAPIKeyLength:
		return m.APIKeyLength()
	case teamapikey.FieldAPIKeyMaskPrefix:
		return m.APIKeyMaskPrefix()
	case teamapikey.FieldAPIKeyMaskSuffix:
		return m.APIKeyMaskSuffix()
	case teamapikey.FieldCreatedAt:
		return m.CreatedAt()
	case teamapikey.FieldUpdatedAt:
		return m.UpdatedAt()
	case teamapikey.FieldTeamID:
		return m.TeamID()
	case teamapikey.FieldName:
		return m.Name()
	case teamapikey.FieldCreatedBy:
		return m.CreatedBy()
	case teamapikey.FieldLastUsed:
		return m.LastUsed()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *TeamAPIKeyMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case teamapikey.FieldAPIKey:
		return m.OldAPIKey(ctx)
	case teamapikey.FieldAPIKeyHash:
		return m.OldAPIKeyHash(ctx)
	case teamapikey.FieldAPIKeyPrefix:
		return m.OldAPIKeyPrefix(ctx)
	case teamapikey.FieldAPIKeyLength:
		return m.OldAPIKeyLength(ctx)
	case teamapikey.FieldAPIKeyMaskPrefix:
		return m.OldAPIKeyMaskPrefix(ctx)
	case teamapikey.FieldAPIKeyMaskSuffix:
		return m.OldAPIKeyMaskSuffix(ctx)
	case teamapikey.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case teamapikey.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case teamapikey.FieldTeamID:
		return m.OldTeamID(ctx)
	case teamapikey.FieldName:
		return m.OldName(ctx)
	case teamapikey.FieldCreatedBy:
		return m.OldCreatedBy(ctx)
	case teamapikey.FieldLastUsed:
		return m.OldLastUsed(ctx)
	}
	return nil, fmt.Errorf("unknown TeamAPIKey field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *TeamAPIKeyMutation) SetField(name string, value ent.Value) error {
	switch name {
	case teamapikey.FieldAPIKey:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAPIKey(v)
		return nil
	case teamapikey.FieldAPIKeyHash:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAPIKeyHash(v)
		return nil
	case teamapikey.FieldAPIKeyPrefix:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAPIKeyPrefix(v)
		return nil
	case teamapikey.FieldAPIKeyLength:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAPIKeyLength(v)
		return nil
	case teamapikey.FieldAPIKeyMaskPrefix:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAPIKeyMaskPrefix(v)
		return nil
	case teamapikey.FieldAPIKeyMaskSuffix:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAPIKeyMaskSuffix(v)
		return nil
	case teamapikey.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case teamapikey.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case teamapikey.FieldTeamID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTeamID(v)
		return nil
	case teamapikey.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case teamapikey.FieldCreatedBy:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedBy(v)
		return nil
	case teamapikey.FieldLastUsed:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetLastUsed(v)
		return nil
	}
	return fmt.Errorf("unknown TeamAPIKey field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *TeamAPIKeyMutation) AddedFields() []string {
	var fields []string
	if m.addapi_key_length != nil {
		fields = append(fields, teamapikey.FieldAPIKeyLength)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *TeamAPIKeyMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case teamapikey.FieldAPIKeyLength:
		return m.AddedAPIKeyLength()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *TeamAPIKeyMutation) AddField(name string, value ent.Value) error {
	switch name {
	case teamapikey.FieldAPIKeyLength:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddAPIKeyLength(v)
		return nil
	}
	return fmt.Errorf("unknown TeamAPIKey numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *TeamAPIKeyMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(teamapikey.FieldUpdatedAt) {
		fields = append(fields, teamapikey.FieldUpdatedAt)
	}
	if m.FieldCleared(teamapikey.FieldCreatedBy) {
		fields = append(fields, teamapikey.FieldCreatedBy)
	}
	if m.FieldCleared(teamapikey.FieldLastUsed) {
		fields = append(fields, teamapikey.FieldLastUsed)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *TeamAPIKeyMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *TeamAPIKeyMutation) ClearField(name string) error {
	switch name {
	case teamapikey.FieldUpdatedAt:
		m.ClearUpdatedAt()
		return nil
	case teamapikey.FieldCreatedBy:
		m.ClearCreatedBy()
		return nil
	case teamapikey.FieldLastUsed:
		m.ClearLastUsed()
		return nil
	}
	return fmt.Errorf("unknown TeamAPIKey nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *TeamAPIKeyMutation) ResetField(name string) error {
	switch name {
	case teamapikey.FieldAPIKey:
		m.ResetAPIKey()
		return nil
	case teamapikey.FieldAPIKeyHash:
		m.ResetAPIKeyHash()
		return nil
	case teamapikey.FieldAPIKeyPrefix:
		m.ResetAPIKeyPrefix()
		return nil
	case teamapikey.FieldAPIKeyLength:
		m.ResetAPIKeyLength()
		return nil
	case teamapikey.FieldAPIKeyMaskPrefix:
		m.ResetAPIKeyMaskPrefix()
		return nil
	case teamapikey.FieldAPIKeyMaskSuffix:
		m.ResetAPIKeyMaskSuffix()
		return nil
	case teamapikey.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case teamapikey.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case teamapikey.FieldTeamID:
		m.ResetTeamID()
		return nil
	case teamapikey.FieldName:
		m.ResetName()
		return nil
	case teamapikey.FieldCreatedBy:
		m.ResetCreatedBy()
		return nil
	case teamapikey.FieldLastUsed:
		m.ResetLastUsed()
		return nil
	}
	return fmt.Errorf("unknown TeamAPIKey field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *TeamAPIKeyMutation) AddedEdges() []string {
	edges := make([]string, 0, 2)
	if m.team != nil {
		edges = append(edges, teamapikey.EdgeTeam)
	}
	if m.creator != nil {
		edges = append(edges, teamapikey.EdgeCreator)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *TeamAPIKeyMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case teamapikey.EdgeTeam:
		if id := m.team; id != nil {
			return []ent.Value{*id}
		}
	case teamapikey.EdgeCreator:
		if id := m.creator; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *TeamAPIKeyMutation) RemovedEdges() []string {
	edges := make([]string, 0, 2)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *TeamAPIKeyMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *TeamAPIKeyMutation) ClearedEdges() []string {
	edges := make([]string, 0, 2)
	if m.clearedteam {
		edges = append(edges, teamapikey.EdgeTeam)
	}
	if m.clearedcreator {
		edges = append(edges, teamapikey.EdgeCreator)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *TeamAPIKeyMutation) EdgeCleared(name string) bool {
	switch name {
	case teamapikey.EdgeTeam:
		return m.clearedteam
	case teamapikey.EdgeCreator:
		return m.clearedcreator
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *TeamAPIKeyMutation) ClearEdge(name string) error {
	switch name {
	case teamapikey.EdgeTeam:
		m.ClearTeam()
		return nil
	case teamapikey.EdgeCreator:
		m.ClearCreator()
		return nil
	}
	return fmt.Errorf("unknown TeamAPIKey unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *TeamAPIKeyMutation) ResetEdge(name string) error {
	switch name {
	case teamapikey.EdgeTeam:
		m.ResetTeam()
		return nil
	case teamapikey.EdgeCreator:
		m.ResetCreator()
		return nil
	}
	return fmt.Errorf("unknown TeamAPIKey edge %s", name)
}

// TierMutation represents an operation that mutates the Tier nodes in the graph.
type TierMutation struct {
	config
	op                      Op
	typ                     string
	id                      *string
	name                    *string
	disk_mb                 *int64
	adddisk_mb              *int64
	concurrent_instances    *int64
	addconcurrent_instances *int64
	max_length_hours        *int64
	addmax_length_hours     *int64
	clearedFields           map[string]struct{}
	teams                   map[uuid.UUID]struct{}
	removedteams            map[uuid.UUID]struct{}
	clearedteams            bool
	done                    bool
	oldValue                func(context.Context) (*Tier, error)
	predicates              []predicate.Tier
}

var _ ent.Mutation = (*TierMutation)(nil)

// tierOption allows management of the mutation configuration using functional options.
type tierOption func(*TierMutation)

// newTierMutation creates new mutation for the Tier entity.
func newTierMutation(c config, op Op, opts ...tierOption) *TierMutation {
	m := &TierMutation{
		config:        c,
		op:            op,
		typ:           TypeTier,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withTierID sets the ID field of the mutation.
func withTierID(id string) tierOption {
	return func(m *TierMutation) {
		var (
			err   error
			once  sync.Once
			value *Tier
		)
		m.oldValue = func(ctx context.Context) (*Tier, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Tier.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withTier sets the old Tier of the mutation.
func withTier(node *Tier) tierOption {
	return func(m *TierMutation) {
		m.oldValue = func(context.Context) (*Tier, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m TierMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m TierMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("models: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Tier entities.
func (m *TierMutation) SetID(id string) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *TierMutation) ID() (id string, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *TierMutation) IDs(ctx context.Context) ([]string, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []string{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Tier.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetName sets the "name" field.
func (m *TierMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *TierMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the Tier entity.
// If the Tier object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TierMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *TierMutation) ResetName() {
	m.name = nil
}

// SetDiskMB sets the "disk_mb" field.
func (m *TierMutation) SetDiskMB(i int64) {
	m.disk_mb = &i
	m.adddisk_mb = nil
}

// DiskMB returns the value of the "disk_mb" field in the mutation.
func (m *TierMutation) DiskMB() (r int64, exists bool) {
	v := m.disk_mb
	if v == nil {
		return
	}
	return *v, true
}

// OldDiskMB returns the old "disk_mb" field's value of the Tier entity.
// If the Tier object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TierMutation) OldDiskMB(ctx context.Context) (v int64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDiskMB is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDiskMB requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDiskMB: %w", err)
	}
	return oldValue.DiskMB, nil
}

// AddDiskMB adds i to the "disk_mb" field.
func (m *TierMutation) AddDiskMB(i int64) {
	if m.adddisk_mb != nil {
		*m.adddisk_mb += i
	} else {
		m.adddisk_mb = &i
	}
}

// AddedDiskMB returns the value that was added to the "disk_mb" field in this mutation.
func (m *TierMutation) AddedDiskMB() (r int64, exists bool) {
	v := m.adddisk_mb
	if v == nil {
		return
	}
	return *v, true
}

// ResetDiskMB resets all changes to the "disk_mb" field.
func (m *TierMutation) ResetDiskMB() {
	m.disk_mb = nil
	m.adddisk_mb = nil
}

// SetConcurrentInstances sets the "concurrent_instances" field.
func (m *TierMutation) SetConcurrentInstances(i int64) {
	m.concurrent_instances = &i
	m.addconcurrent_instances = nil
}

// ConcurrentInstances returns the value of the "concurrent_instances" field in the mutation.
func (m *TierMutation) ConcurrentInstances() (r int64, exists bool) {
	v := m.concurrent_instances
	if v == nil {
		return
	}
	return *v, true
}

// OldConcurrentInstances returns the old "concurrent_instances" field's value of the Tier entity.
// If the Tier object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TierMutation) OldConcurrentInstances(ctx context.Context) (v int64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldConcurrentInstances is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldConcurrentInstances requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldConcurrentInstances: %w", err)
	}
	return oldValue.ConcurrentInstances, nil
}

// AddConcurrentInstances adds i to the "concurrent_instances" field.
func (m *TierMutation) AddConcurrentInstances(i int64) {
	if m.addconcurrent_instances != nil {
		*m.addconcurrent_instances += i
	} else {
		m.addconcurrent_instances = &i
	}
}

// AddedConcurrentInstances returns the value that was added to the "concurrent_instances" field in this mutation.
func (m *TierMutation) AddedConcurrentInstances() (r int64, exists bool) {
	v := m.addconcurrent_instances
	if v == nil {
		return
	}
	return *v, true
}

// ResetConcurrentInstances resets all changes to the "concurrent_instances" field.
func (m *TierMutation) ResetConcurrentInstances() {
	m.concurrent_instances = nil
	m.addconcurrent_instances = nil
}

// SetMaxLengthHours sets the "max_length_hours" field.
func (m *TierMutation) SetMaxLengthHours(i int64) {
	m.max_length_hours = &i
	m.addmax_length_hours = nil
}

// MaxLengthHours returns the value of the "max_length_hours" field in the mutation.
func (m *TierMutation) MaxLengthHours() (r int64, exists bool) {
	v := m.max_length_hours
	if v == nil {
		return
	}
	return *v, true
}

// OldMaxLengthHours returns the old "max_length_hours" field's value of the Tier entity.
// If the Tier object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TierMutation) OldMaxLengthHours(ctx context.Context) (v int64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMaxLengthHours is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMaxLengthHours requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMaxLengthHours: %w", err)
	}
	return oldValue.MaxLengthHours, nil
}

// AddMaxLengthHours adds i to the "max_length_hours" field.
func (m *TierMutation) AddMaxLengthHours(i int64) {
	if m.addmax_length_hours != nil {
		*m.addmax_length_hours += i
	} else {
		m.addmax_length_hours = &i
	}
}

// AddedMaxLengthHours returns the value that was added to the "max_length_hours" field in this mutation.
func (m *TierMutation) AddedMaxLengthHours() (r int64, exists bool) {
	v := m.addmax_length_hours
	if v == nil {
		return
	}
	return *v, true
}

// ResetMaxLengthHours resets all changes to the "max_length_hours" field.
func (m *TierMutation) ResetMaxLengthHours() {
	m.max_length_hours = nil
	m.addmax_length_hours = nil
}

// AddTeamIDs adds the "teams" edge to the Team entity by ids.
func (m *TierMutation) AddTeamIDs(ids ...uuid.UUID) {
	if m.teams == nil {
		m.teams = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		m.teams[ids[i]] = struct{}{}
	}
}

// ClearTeams clears the "teams" edge to the Team entity.
func (m *TierMutation) ClearTeams() {
	m.clearedteams = true
}

// TeamsCleared reports if the "teams" edge to the Team entity was cleared.
func (m *TierMutation) TeamsCleared() bool {
	return m.clearedteams
}

// RemoveTeamIDs removes the "teams" edge to the Team entity by IDs.
func (m *TierMutation) RemoveTeamIDs(ids ...uuid.UUID) {
	if m.removedteams == nil {
		m.removedteams = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		delete(m.teams, ids[i])
		m.removedteams[ids[i]] = struct{}{}
	}
}

// RemovedTeams returns the removed IDs of the "teams" edge to the Team entity.
func (m *TierMutation) RemovedTeamsIDs() (ids []uuid.UUID) {
	for id := range m.removedteams {
		ids = append(ids, id)
	}
	return
}

// TeamsIDs returns the "teams" edge IDs in the mutation.
func (m *TierMutation) TeamsIDs() (ids []uuid.UUID) {
	for id := range m.teams {
		ids = append(ids, id)
	}
	return
}

// ResetTeams resets all changes to the "teams" edge.
func (m *TierMutation) ResetTeams() {
	m.teams = nil
	m.clearedteams = false
	m.removedteams = nil
}

// Where appends a list predicates to the TierMutation builder.
func (m *TierMutation) Where(ps ...predicate.Tier) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the TierMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *TierMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Tier, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *TierMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *TierMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Tier).
func (m *TierMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *TierMutation) Fields() []string {
	fields := make([]string, 0, 4)
	if m.name != nil {
		fields = append(fields, tier.FieldName)
	}
	if m.disk_mb != nil {
		fields = append(fields, tier.FieldDiskMB)
	}
	if m.concurrent_instances != nil {
		fields = append(fields, tier.FieldConcurrentInstances)
	}
	if m.max_length_hours != nil {
		fields = append(fields, tier.FieldMaxLengthHours)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *TierMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case tier.FieldName:
		return m.Name()
	case tier.FieldDiskMB:
		return m.DiskMB()
	case tier.FieldConcurrentInstances:
		return m.ConcurrentInstances()
	case tier.FieldMaxLengthHours:
		return m.MaxLengthHours()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *TierMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case tier.FieldName:
		return m.OldName(ctx)
	case tier.FieldDiskMB:
		return m.OldDiskMB(ctx)
	case tier.FieldConcurrentInstances:
		return m.OldConcurrentInstances(ctx)
	case tier.FieldMaxLengthHours:
		return m.OldMaxLengthHours(ctx)
	}
	return nil, fmt.Errorf("unknown Tier field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *TierMutation) SetField(name string, value ent.Value) error {
	switch name {
	case tier.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case tier.FieldDiskMB:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDiskMB(v)
		return nil
	case tier.FieldConcurrentInstances:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetConcurrentInstances(v)
		return nil
	case tier.FieldMaxLengthHours:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMaxLengthHours(v)
		return nil
	}
	return fmt.Errorf("unknown Tier field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *TierMutation) AddedFields() []string {
	var fields []string
	if m.adddisk_mb != nil {
		fields = append(fields, tier.FieldDiskMB)
	}
	if m.addconcurrent_instances != nil {
		fields = append(fields, tier.FieldConcurrentInstances)
	}
	if m.addmax_length_hours != nil {
		fields = append(fields, tier.FieldMaxLengthHours)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *TierMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case tier.FieldDiskMB:
		return m.AddedDiskMB()
	case tier.FieldConcurrentInstances:
		return m.AddedConcurrentInstances()
	case tier.FieldMaxLengthHours:
		return m.AddedMaxLengthHours()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *TierMutation) AddField(name string, value ent.Value) error {
	switch name {
	case tier.FieldDiskMB:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddDiskMB(v)
		return nil
	case tier.FieldConcurrentInstances:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddConcurrentInstances(v)
		return nil
	case tier.FieldMaxLengthHours:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddMaxLengthHours(v)
		return nil
	}
	return fmt.Errorf("unknown Tier numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *TierMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *TierMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *TierMutation) ClearField(name string) error {
	return fmt.Errorf("unknown Tier nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *TierMutation) ResetField(name string) error {
	switch name {
	case tier.FieldName:
		m.ResetName()
		return nil
	case tier.FieldDiskMB:
		m.ResetDiskMB()
		return nil
	case tier.FieldConcurrentInstances:
		m.ResetConcurrentInstances()
		return nil
	case tier.FieldMaxLengthHours:
		m.ResetMaxLengthHours()
		return nil
	}
	return fmt.Errorf("unknown Tier field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *TierMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.teams != nil {
		edges = append(edges, tier.EdgeTeams)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *TierMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case tier.EdgeTeams:
		ids := make([]ent.Value, 0, len(m.teams))
		for id := range m.teams {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *TierMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	if m.removedteams != nil {
		edges = append(edges, tier.EdgeTeams)
	}
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *TierMutation) RemovedIDs(name string) []ent.Value {
	switch name {
	case tier.EdgeTeams:
		ids := make([]ent.Value, 0, len(m.removedteams))
		for id := range m.removedteams {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *TierMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.clearedteams {
		edges = append(edges, tier.EdgeTeams)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *TierMutation) EdgeCleared(name string) bool {
	switch name {
	case tier.EdgeTeams:
		return m.clearedteams
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *TierMutation) ClearEdge(name string) error {
	switch name {
	}
	return fmt.Errorf("unknown Tier unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *TierMutation) ResetEdge(name string) error {
	switch name {
	case tier.EdgeTeams:
		m.ResetTeams()
		return nil
	}
	return fmt.Errorf("unknown Tier edge %s", name)
}

// UserMutation represents an operation that mutates the User nodes in the graph.
type UserMutation struct {
	config
	op                      Op
	typ                     string
	id                      *uuid.UUID
	email                   *string
	clearedFields           map[string]struct{}
	teams                   map[uuid.UUID]struct{}
	removedteams            map[uuid.UUID]struct{}
	clearedteams            bool
	created_envs            map[string]struct{}
	removedcreated_envs     map[string]struct{}
	clearedcreated_envs     bool
	access_tokens           map[uuid.UUID]struct{}
	removedaccess_tokens    map[uuid.UUID]struct{}
	clearedaccess_tokens    bool
	created_api_keys        map[uuid.UUID]struct{}
	removedcreated_api_keys map[uuid.UUID]struct{}
	clearedcreated_api_keys bool
	users_teams             map[int]struct{}
	removedusers_teams      map[int]struct{}
	clearedusers_teams      bool
	done                    bool
	oldValue                func(context.Context) (*User, error)
	predicates              []predicate.User
}

var _ ent.Mutation = (*UserMutation)(nil)

// userOption allows management of the mutation configuration using functional options.
type userOption func(*UserMutation)

// newUserMutation creates new mutation for the User entity.
func newUserMutation(c config, op Op, opts ...userOption) *UserMutation {
	m := &UserMutation{
		config:        c,
		op:            op,
		typ:           TypeUser,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withUserID sets the ID field of the mutation.
func withUserID(id uuid.UUID) userOption {
	return func(m *UserMutation) {
		var (
			err   error
			once  sync.Once
			value *User
		)
		m.oldValue = func(ctx context.Context) (*User, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().User.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withUser sets the old User of the mutation.
func withUser(node *User) userOption {
	return func(m *UserMutation) {
		m.oldValue = func(context.Context) (*User, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m UserMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m UserMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("models: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of User entities.
func (m *UserMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *UserMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *UserMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().User.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetEmail sets the "email" field.
func (m *UserMutation) SetEmail(s string) {
	m.email = &s
}

// Email returns the value of the "email" field in the mutation.
func (m *UserMutation) Email() (r string, exists bool) {
	v := m.email
	if v == nil {
		return
	}
	return *v, true
}

// OldEmail returns the old "email" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldEmail(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEmail is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEmail requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEmail: %w", err)
	}
	return oldValue.Email, nil
}

// ResetEmail resets all changes to the "email" field.
func (m *UserMutation) ResetEmail() {
	m.email = nil
}

// AddTeamIDs adds the "teams" edge to the Team entity by ids.
func (m *UserMutation) AddTeamIDs(ids ...uuid.UUID) {
	if m.teams == nil {
		m.teams = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		m.teams[ids[i]] = struct{}{}
	}
}

// ClearTeams clears the "teams" edge to the Team entity.
func (m *UserMutation) ClearTeams() {
	m.clearedteams = true
}

// TeamsCleared reports if the "teams" edge to the Team entity was cleared.
func (m *UserMutation) TeamsCleared() bool {
	return m.clearedteams
}

// RemoveTeamIDs removes the "teams" edge to the Team entity by IDs.
func (m *UserMutation) RemoveTeamIDs(ids ...uuid.UUID) {
	if m.removedteams == nil {
		m.removedteams = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		delete(m.teams, ids[i])
		m.removedteams[ids[i]] = struct{}{}
	}
}

// RemovedTeams returns the removed IDs of the "teams" edge to the Team entity.
func (m *UserMutation) RemovedTeamsIDs() (ids []uuid.UUID) {
	for id := range m.removedteams {
		ids = append(ids, id)
	}
	return
}

// TeamsIDs returns the "teams" edge IDs in the mutation.
func (m *UserMutation) TeamsIDs() (ids []uuid.UUID) {
	for id := range m.teams {
		ids = append(ids, id)
	}
	return
}

// ResetTeams resets all changes to the "teams" edge.
func (m *UserMutation) ResetTeams() {
	m.teams = nil
	m.clearedteams = false
	m.removedteams = nil
}

// AddCreatedEnvIDs adds the "created_envs" edge to the Env entity by ids.
func (m *UserMutation) AddCreatedEnvIDs(ids ...string) {
	if m.created_envs == nil {
		m.created_envs = make(map[string]struct{})
	}
	for i := range ids {
		m.created_envs[ids[i]] = struct{}{}
	}
}

// ClearCreatedEnvs clears the "created_envs" edge to the Env entity.
func (m *UserMutation) ClearCreatedEnvs() {
	m.clearedcreated_envs = true
}

// CreatedEnvsCleared reports if the "created_envs" edge to the Env entity was cleared.
func (m *UserMutation) CreatedEnvsCleared() bool {
	return m.clearedcreated_envs
}

// RemoveCreatedEnvIDs removes the "created_envs" edge to the Env entity by IDs.
func (m *UserMutation) RemoveCreatedEnvIDs(ids ...string) {
	if m.removedcreated_envs == nil {
		m.removedcreated_envs = make(map[string]struct{})
	}
	for i := range ids {
		delete(m.created_envs, ids[i])
		m.removedcreated_envs[ids[i]] = struct{}{}
	}
}

// RemovedCreatedEnvs returns the removed IDs of the "created_envs" edge to the Env entity.
func (m *UserMutation) RemovedCreatedEnvsIDs() (ids []string) {
	for id := range m.removedcreated_envs {
		ids = append(ids, id)
	}
	return
}

// CreatedEnvsIDs returns the "created_envs" edge IDs in the mutation.
func (m *UserMutation) CreatedEnvsIDs() (ids []string) {
	for id := range m.created_envs {
		ids = append(ids, id)
	}
	return
}

// ResetCreatedEnvs resets all changes to the "created_envs" edge.
func (m *UserMutation) ResetCreatedEnvs() {
	m.created_envs = nil
	m.clearedcreated_envs = false
	m.removedcreated_envs = nil
}

// AddAccessTokenIDs adds the "access_tokens" edge to the AccessToken entity by ids.
func (m *UserMutation) AddAccessTokenIDs(ids ...uuid.UUID) {
	if m.access_tokens == nil {
		m.access_tokens = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		m.access_tokens[ids[i]] = struct{}{}
	}
}

// ClearAccessTokens clears the "access_tokens" edge to the AccessToken entity.
func (m *UserMutation) ClearAccessTokens() {
	m.clearedaccess_tokens = true
}

// AccessTokensCleared reports if the "access_tokens" edge to the AccessToken entity was cleared.
func (m *UserMutation) AccessTokensCleared() bool {
	return m.clearedaccess_tokens
}

// RemoveAccessTokenIDs removes the "access_tokens" edge to the AccessToken entity by IDs.
func (m *UserMutation) RemoveAccessTokenIDs(ids ...uuid.UUID) {
	if m.removedaccess_tokens == nil {
		m.removedaccess_tokens = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		delete(m.access_tokens, ids[i])
		m.removedaccess_tokens[ids[i]] = struct{}{}
	}
}

// RemovedAccessTokens returns the removed IDs of the "access_tokens" edge to the AccessToken entity.
func (m *UserMutation) RemovedAccessTokensIDs() (ids []uuid.UUID) {
	for id := range m.removedaccess_tokens {
		ids = append(ids, id)
	}
	return
}

// AccessTokensIDs returns the "access_tokens" edge IDs in the mutation.
func (m *UserMutation) AccessTokensIDs() (ids []uuid.UUID) {
	for id := range m.access_tokens {
		ids = append(ids, id)
	}
	return
}

// ResetAccessTokens resets all changes to the "access_tokens" edge.
func (m *UserMutation) ResetAccessTokens() {
	m.access_tokens = nil
	m.clearedaccess_tokens = false
	m.removedaccess_tokens = nil
}

// AddCreatedAPIKeyIDs adds the "created_api_keys" edge to the TeamAPIKey entity by ids.
func (m *UserMutation) AddCreatedAPIKeyIDs(ids ...uuid.UUID) {
	if m.created_api_keys == nil {
		m.created_api_keys = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		m.created_api_keys[ids[i]] = struct{}{}
	}
}

// ClearCreatedAPIKeys clears the "created_api_keys" edge to the TeamAPIKey entity.
func (m *UserMutation) ClearCreatedAPIKeys() {
	m.clearedcreated_api_keys = true
}

// CreatedAPIKeysCleared reports if the "created_api_keys" edge to the TeamAPIKey entity was cleared.
func (m *UserMutation) CreatedAPIKeysCleared() bool {
	return m.clearedcreated_api_keys
}

// RemoveCreatedAPIKeyIDs removes the "created_api_keys" edge to the TeamAPIKey entity by IDs.
func (m *UserMutation) RemoveCreatedAPIKeyIDs(ids ...uuid.UUID) {
	if m.removedcreated_api_keys == nil {
		m.removedcreated_api_keys = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		delete(m.created_api_keys, ids[i])
		m.removedcreated_api_keys[ids[i]] = struct{}{}
	}
}

// RemovedCreatedAPIKeys returns the removed IDs of the "created_api_keys" edge to the TeamAPIKey entity.
func (m *UserMutation) RemovedCreatedAPIKeysIDs() (ids []uuid.UUID) {
	for id := range m.removedcreated_api_keys {
		ids = append(ids, id)
	}
	return
}

// CreatedAPIKeysIDs returns the "created_api_keys" edge IDs in the mutation.
func (m *UserMutation) CreatedAPIKeysIDs() (ids []uuid.UUID) {
	for id := range m.created_api_keys {
		ids = append(ids, id)
	}
	return
}

// ResetCreatedAPIKeys resets all changes to the "created_api_keys" edge.
func (m *UserMutation) ResetCreatedAPIKeys() {
	m.created_api_keys = nil
	m.clearedcreated_api_keys = false
	m.removedcreated_api_keys = nil
}

// AddUsersTeamIDs adds the "users_teams" edge to the UsersTeams entity by ids.
func (m *UserMutation) AddUsersTeamIDs(ids ...int) {
	if m.users_teams == nil {
		m.users_teams = make(map[int]struct{})
	}
	for i := range ids {
		m.users_teams[ids[i]] = struct{}{}
	}
}

// ClearUsersTeams clears the "users_teams" edge to the UsersTeams entity.
func (m *UserMutation) ClearUsersTeams() {
	m.clearedusers_teams = true
}

// UsersTeamsCleared reports if the "users_teams" edge to the UsersTeams entity was cleared.
func (m *UserMutation) UsersTeamsCleared() bool {
	return m.clearedusers_teams
}

// RemoveUsersTeamIDs removes the "users_teams" edge to the UsersTeams entity by IDs.
func (m *UserMutation) RemoveUsersTeamIDs(ids ...int) {
	if m.removedusers_teams == nil {
		m.removedusers_teams = make(map[int]struct{})
	}
	for i := range ids {
		delete(m.users_teams, ids[i])
		m.removedusers_teams[ids[i]] = struct{}{}
	}
}

// RemovedUsersTeams returns the removed IDs of the "users_teams" edge to the UsersTeams entity.
func (m *UserMutation) RemovedUsersTeamsIDs() (ids []int) {
	for id := range m.removedusers_teams {
		ids = append(ids, id)
	}
	return
}

// UsersTeamsIDs returns the "users_teams" edge IDs in the mutation.
func (m *UserMutation) UsersTeamsIDs() (ids []int) {
	for id := range m.users_teams {
		ids = append(ids, id)
	}
	return
}

// ResetUsersTeams resets all changes to the "users_teams" edge.
func (m *UserMutation) ResetUsersTeams() {
	m.users_teams = nil
	m.clearedusers_teams = false
	m.removedusers_teams = nil
}

// Where appends a list predicates to the UserMutation builder.
func (m *UserMutation) Where(ps ...predicate.User) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the UserMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *UserMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.User, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *UserMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *UserMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (User).
func (m *UserMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *UserMutation) Fields() []string {
	fields := make([]string, 0, 1)
	if m.email != nil {
		fields = append(fields, user.FieldEmail)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *UserMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case user.FieldEmail:
		return m.Email()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *UserMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case user.FieldEmail:
		return m.OldEmail(ctx)
	}
	return nil, fmt.Errorf("unknown User field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *UserMutation) SetField(name string, value ent.Value) error {
	switch name {
	case user.FieldEmail:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEmail(v)
		return nil
	}
	return fmt.Errorf("unknown User field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *UserMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *UserMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *UserMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown User numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *UserMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *UserMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *UserMutation) ClearField(name string) error {
	return fmt.Errorf("unknown User nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *UserMutation) ResetField(name string) error {
	switch name {
	case user.FieldEmail:
		m.ResetEmail()
		return nil
	}
	return fmt.Errorf("unknown User field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *UserMutation) AddedEdges() []string {
	edges := make([]string, 0, 5)
	if m.teams != nil {
		edges = append(edges, user.EdgeTeams)
	}
	if m.created_envs != nil {
		edges = append(edges, user.EdgeCreatedEnvs)
	}
	if m.access_tokens != nil {
		edges = append(edges, user.EdgeAccessTokens)
	}
	if m.created_api_keys != nil {
		edges = append(edges, user.EdgeCreatedAPIKeys)
	}
	if m.users_teams != nil {
		edges = append(edges, user.EdgeUsersTeams)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *UserMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case user.EdgeTeams:
		ids := make([]ent.Value, 0, len(m.teams))
		for id := range m.teams {
			ids = append(ids, id)
		}
		return ids
	case user.EdgeCreatedEnvs:
		ids := make([]ent.Value, 0, len(m.created_envs))
		for id := range m.created_envs {
			ids = append(ids, id)
		}
		return ids
	case user.EdgeAccessTokens:
		ids := make([]ent.Value, 0, len(m.access_tokens))
		for id := range m.access_tokens {
			ids = append(ids, id)
		}
		return ids
	case user.EdgeCreatedAPIKeys:
		ids := make([]ent.Value, 0, len(m.created_api_keys))
		for id := range m.created_api_keys {
			ids = append(ids, id)
		}
		return ids
	case user.EdgeUsersTeams:
		ids := make([]ent.Value, 0, len(m.users_teams))
		for id := range m.users_teams {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *UserMutation) RemovedEdges() []string {
	edges := make([]string, 0, 5)
	if m.removedteams != nil {
		edges = append(edges, user.EdgeTeams)
	}
	if m.removedcreated_envs != nil {
		edges = append(edges, user.EdgeCreatedEnvs)
	}
	if m.removedaccess_tokens != nil {
		edges = append(edges, user.EdgeAccessTokens)
	}
	if m.removedcreated_api_keys != nil {
		edges = append(edges, user.EdgeCreatedAPIKeys)
	}
	if m.removedusers_teams != nil {
		edges = append(edges, user.EdgeUsersTeams)
	}
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *UserMutation) RemovedIDs(name string) []ent.Value {
	switch name {
	case user.EdgeTeams:
		ids := make([]ent.Value, 0, len(m.removedteams))
		for id := range m.removedteams {
			ids = append(ids, id)
		}
		return ids
	case user.EdgeCreatedEnvs:
		ids := make([]ent.Value, 0, len(m.removedcreated_envs))
		for id := range m.removedcreated_envs {
			ids = append(ids, id)
		}
		return ids
	case user.EdgeAccessTokens:
		ids := make([]ent.Value, 0, len(m.removedaccess_tokens))
		for id := range m.removedaccess_tokens {
			ids = append(ids, id)
		}
		return ids
	case user.EdgeCreatedAPIKeys:
		ids := make([]ent.Value, 0, len(m.removedcreated_api_keys))
		for id := range m.removedcreated_api_keys {
			ids = append(ids, id)
		}
		return ids
	case user.EdgeUsersTeams:
		ids := make([]ent.Value, 0, len(m.removedusers_teams))
		for id := range m.removedusers_teams {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *UserMutation) ClearedEdges() []string {
	edges := make([]string, 0, 5)
	if m.clearedteams {
		edges = append(edges, user.EdgeTeams)
	}
	if m.clearedcreated_envs {
		edges = append(edges, user.EdgeCreatedEnvs)
	}
	if m.clearedaccess_tokens {
		edges = append(edges, user.EdgeAccessTokens)
	}
	if m.clearedcreated_api_keys {
		edges = append(edges, user.EdgeCreatedAPIKeys)
	}
	if m.clearedusers_teams {
		edges = append(edges, user.EdgeUsersTeams)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *UserMutation) EdgeCleared(name string) bool {
	switch name {
	case user.EdgeTeams:
		return m.clearedteams
	case user.EdgeCreatedEnvs:
		return m.clearedcreated_envs
	case user.EdgeAccessTokens:
		return m.clearedaccess_tokens
	case user.EdgeCreatedAPIKeys:
		return m.clearedcreated_api_keys
	case user.EdgeUsersTeams:
		return m.clearedusers_teams
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *UserMutation) ClearEdge(name string) error {
	switch name {
	}
	return fmt.Errorf("unknown User unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *UserMutation) ResetEdge(name string) error {
	switch name {
	case user.EdgeTeams:
		m.ResetTeams()
		return nil
	case user.EdgeCreatedEnvs:
		m.ResetCreatedEnvs()
		return nil
	case user.EdgeAccessTokens:
		m.ResetAccessTokens()
		return nil
	case user.EdgeCreatedAPIKeys:
		m.ResetCreatedAPIKeys()
		return nil
	case user.EdgeUsersTeams:
		m.ResetUsersTeams()
		return nil
	}
	return fmt.Errorf("unknown User edge %s", name)
}

// UsersTeamsMutation represents an operation that mutates the UsersTeams nodes in the graph.
type UsersTeamsMutation struct {
	config
	op            Op
	typ           string
	id            *int
	is_default    *bool
	clearedFields map[string]struct{}
	users         *uuid.UUID
	clearedusers  bool
	teams         *uuid.UUID
	clearedteams  bool
	done          bool
	oldValue      func(context.Context) (*UsersTeams, error)
	predicates    []predicate.UsersTeams
}

var _ ent.Mutation = (*UsersTeamsMutation)(nil)

// usersteamsOption allows management of the mutation configuration using functional options.
type usersteamsOption func(*UsersTeamsMutation)

// newUsersTeamsMutation creates new mutation for the UsersTeams entity.
func newUsersTeamsMutation(c config, op Op, opts ...usersteamsOption) *UsersTeamsMutation {
	m := &UsersTeamsMutation{
		config:        c,
		op:            op,
		typ:           TypeUsersTeams,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withUsersTeamsID sets the ID field of the mutation.
func withUsersTeamsID(id int) usersteamsOption {
	return func(m *UsersTeamsMutation) {
		var (
			err   error
			once  sync.Once
			value *UsersTeams
		)
		m.oldValue = func(ctx context.Context) (*UsersTeams, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().UsersTeams.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withUsersTeams sets the old UsersTeams of the mutation.
func withUsersTeams(node *UsersTeams) usersteamsOption {
	return func(m *UsersTeamsMutation) {
		m.oldValue = func(context.Context) (*UsersTeams, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m UsersTeamsMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m UsersTeamsMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("models: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *UsersTeamsMutation) ID() (id int, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *UsersTeamsMutation) IDs(ctx context.Context) ([]int, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().UsersTeams.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetUserID sets the "user_id" field.
func (m *UsersTeamsMutation) SetUserID(u uuid.UUID) {
	m.users = &u
}

// UserID returns the value of the "user_id" field in the mutation.
func (m *UsersTeamsMutation) UserID() (r uuid.UUID, exists bool) {
	v := m.users
	if v == nil {
		return
	}
	return *v, true
}

// OldUserID returns the old "user_id" field's value of the UsersTeams entity.
// If the UsersTeams object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UsersTeamsMutation) OldUserID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUserID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUserID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUserID: %w", err)
	}
	return oldValue.UserID, nil
}

// ResetUserID resets all changes to the "user_id" field.
func (m *UsersTeamsMutation) ResetUserID() {
	m.users = nil
}

// SetTeamID sets the "team_id" field.
func (m *UsersTeamsMutation) SetTeamID(u uuid.UUID) {
	m.teams = &u
}

// TeamID returns the value of the "team_id" field in the mutation.
func (m *UsersTeamsMutation) TeamID() (r uuid.UUID, exists bool) {
	v := m.teams
	if v == nil {
		return
	}
	return *v, true
}

// OldTeamID returns the old "team_id" field's value of the UsersTeams entity.
// If the UsersTeams object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UsersTeamsMutation) OldTeamID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTeamID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTeamID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTeamID: %w", err)
	}
	return oldValue.TeamID, nil
}

// ResetTeamID resets all changes to the "team_id" field.
func (m *UsersTeamsMutation) ResetTeamID() {
	m.teams = nil
}

// SetIsDefault sets the "is_default" field.
func (m *UsersTeamsMutation) SetIsDefault(b bool) {
	m.is_default = &b
}

// IsDefault returns the value of the "is_default" field in the mutation.
func (m *UsersTeamsMutation) IsDefault() (r bool, exists bool) {
	v := m.is_default
	if v == nil {
		return
	}
	return *v, true
}

// OldIsDefault returns the old "is_default" field's value of the UsersTeams entity.
// If the UsersTeams object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UsersTeamsMutation) OldIsDefault(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIsDefault is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIsDefault requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIsDefault: %w", err)
	}
	return oldValue.IsDefault, nil
}

// ResetIsDefault resets all changes to the "is_default" field.
func (m *UsersTeamsMutation) ResetIsDefault() {
	m.is_default = nil
}

// SetUsersID sets the "users" edge to the User entity by id.
func (m *UsersTeamsMutation) SetUsersID(id uuid.UUID) {
	m.users = &id
}

// ClearUsers clears the "users" edge to the User entity.
func (m *UsersTeamsMutation) ClearUsers() {
	m.clearedusers = true
	m.clearedFields[usersteams.FieldUserID] = struct{}{}
}

// UsersCleared reports if the "users" edge to the User entity was cleared.
func (m *UsersTeamsMutation) UsersCleared() bool {
	return m.clearedusers
}

// UsersID returns the "users" edge ID in the mutation.
func (m *UsersTeamsMutation) UsersID() (id uuid.UUID, exists bool) {
	if m.users != nil {
		return *m.users, true
	}
	return
}

// UsersIDs returns the "users" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// UsersID instead. It exists only for internal usage by the builders.
func (m *UsersTeamsMutation) UsersIDs() (ids []uuid.UUID) {
	if id := m.users; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetUsers resets all changes to the "users" edge.
func (m *UsersTeamsMutation) ResetUsers() {
	m.users = nil
	m.clearedusers = false
}

// SetTeamsID sets the "teams" edge to the Team entity by id.
func (m *UsersTeamsMutation) SetTeamsID(id uuid.UUID) {
	m.teams = &id
}

// ClearTeams clears the "teams" edge to the Team entity.
func (m *UsersTeamsMutation) ClearTeams() {
	m.clearedteams = true
	m.clearedFields[usersteams.FieldTeamID] = struct{}{}
}

// TeamsCleared reports if the "teams" edge to the Team entity was cleared.
func (m *UsersTeamsMutation) TeamsCleared() bool {
	return m.clearedteams
}

// TeamsID returns the "teams" edge ID in the mutation.
func (m *UsersTeamsMutation) TeamsID() (id uuid.UUID, exists bool) {
	if m.teams != nil {
		return *m.teams, true
	}
	return
}

// TeamsIDs returns the "teams" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// TeamsID instead. It exists only for internal usage by the builders.
func (m *UsersTeamsMutation) TeamsIDs() (ids []uuid.UUID) {
	if id := m.teams; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetTeams resets all changes to the "teams" edge.
func (m *UsersTeamsMutation) ResetTeams() {
	m.teams = nil
	m.clearedteams = false
}

// Where appends a list predicates to the UsersTeamsMutation builder.
func (m *UsersTeamsMutation) Where(ps ...predicate.UsersTeams) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the UsersTeamsMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *UsersTeamsMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.UsersTeams, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *UsersTeamsMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *UsersTeamsMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (UsersTeams).
func (m *UsersTeamsMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *UsersTeamsMutation) Fields() []string {
	fields := make([]string, 0, 3)
	if m.users != nil {
		fields = append(fields, usersteams.FieldUserID)
	}
	if m.teams != nil {
		fields = append(fields, usersteams.FieldTeamID)
	}
	if m.is_default != nil {
		fields = append(fields, usersteams.FieldIsDefault)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *UsersTeamsMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case usersteams.FieldUserID:
		return m.UserID()
	case usersteams.FieldTeamID:
		return m.TeamID()
	case usersteams.FieldIsDefault:
		return m.IsDefault()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *UsersTeamsMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case usersteams.FieldUserID:
		return m.OldUserID(ctx)
	case usersteams.FieldTeamID:
		return m.OldTeamID(ctx)
	case usersteams.FieldIsDefault:
		return m.OldIsDefault(ctx)
	}
	return nil, fmt.Errorf("unknown UsersTeams field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *UsersTeamsMutation) SetField(name string, value ent.Value) error {
	switch name {
	case usersteams.FieldUserID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUserID(v)
		return nil
	case usersteams.FieldTeamID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTeamID(v)
		return nil
	case usersteams.FieldIsDefault:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIsDefault(v)
		return nil
	}
	return fmt.Errorf("unknown UsersTeams field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *UsersTeamsMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *UsersTeamsMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *UsersTeamsMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown UsersTeams numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *UsersTeamsMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *UsersTeamsMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *UsersTeamsMutation) ClearField(name string) error {
	return fmt.Errorf("unknown UsersTeams nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *UsersTeamsMutation) ResetField(name string) error {
	switch name {
	case usersteams.FieldUserID:
		m.ResetUserID()
		return nil
	case usersteams.FieldTeamID:
		m.ResetTeamID()
		return nil
	case usersteams.FieldIsDefault:
		m.ResetIsDefault()
		return nil
	}
	return fmt.Errorf("unknown UsersTeams field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *UsersTeamsMutation) AddedEdges() []string {
	edges := make([]string, 0, 2)
	if m.users != nil {
		edges = append(edges, usersteams.EdgeUsers)
	}
	if m.teams != nil {
		edges = append(edges, usersteams.EdgeTeams)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *UsersTeamsMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case usersteams.EdgeUsers:
		if id := m.users; id != nil {
			return []ent.Value{*id}
		}
	case usersteams.EdgeTeams:
		if id := m.teams; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *UsersTeamsMutation) RemovedEdges() []string {
	edges := make([]string, 0, 2)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *UsersTeamsMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *UsersTeamsMutation) ClearedEdges() []string {
	edges := make([]string, 0, 2)
	if m.clearedusers {
		edges = append(edges, usersteams.EdgeUsers)
	}
	if m.clearedteams {
		edges = append(edges, usersteams.EdgeTeams)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *UsersTeamsMutation) EdgeCleared(name string) bool {
	switch name {
	case usersteams.EdgeUsers:
		return m.clearedusers
	case usersteams.EdgeTeams:
		return m.clearedteams
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *UsersTeamsMutation) ClearEdge(name string) error {
	switch name {
	case usersteams.EdgeUsers:
		m.ClearUsers()
		return nil
	case usersteams.EdgeTeams:
		m.ClearTeams()
		return nil
	}
	return fmt.Errorf("unknown UsersTeams unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *UsersTeamsMutation) ResetEdge(name string) error {
	switch name {
	case usersteams.EdgeUsers:
		m.ResetUsers()
		return nil
	case usersteams.EdgeTeams:
		m.ResetTeams()
		return nil
	}
	return fmt.Errorf("unknown UsersTeams edge %s", name)
}
