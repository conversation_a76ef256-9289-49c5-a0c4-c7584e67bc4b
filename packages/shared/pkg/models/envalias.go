// Code generated by ent, DO NOT EDIT.

package models

import (
	"fmt"
	"strings"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/env"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/envalias"
)

// EnvAlias is the model entity for the EnvAlias schema.
type EnvAlias struct {
	config `json:"-"`
	// ID of the ent.
	ID string `json:"id,omitempty"`
	// EnvID holds the value of the "env_id" field.
	EnvID string `json:"env_id,omitempty"`
	// IsRenamable holds the value of the "is_renamable" field.
	IsRenamable bool `json:"is_renamable,omitempty"`
	// <PERSON>s holds the relations/edges for other nodes in the graph.
	// The values are being populated by the EnvAliasQuery when eager-loading is set.
	Edges        EnvAliasEdges `json:"edges"`
	selectValues sql.SelectValues
}

// EnvAliasEdges holds the relations/edges for other nodes in the graph.
type EnvAliasEdges struct {
	// Env holds the value of the env edge.
	Env *Env `json:"env,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [1]bool
}

// EnvOrErr returns the Env value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e EnvAliasEdges) EnvOrErr() (*Env, error) {
	if e.loadedTypes[0] {
		if e.Env == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: env.Label}
		}
		return e.Env, nil
	}
	return nil, &NotLoadedError{edge: "env"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*EnvAlias) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case envalias.FieldIsRenamable:
			values[i] = new(sql.NullBool)
		case envalias.FieldID, envalias.FieldEnvID:
			values[i] = new(sql.NullString)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the EnvAlias fields.
func (ea *EnvAlias) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case envalias.FieldID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value.Valid {
				ea.ID = value.String
			}
		case envalias.FieldEnvID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field env_id", values[i])
			} else if value.Valid {
				ea.EnvID = value.String
			}
		case envalias.FieldIsRenamable:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_renamable", values[i])
			} else if value.Valid {
				ea.IsRenamable = value.Bool
			}
		default:
			ea.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the EnvAlias.
// This includes values selected through modifiers, order, etc.
func (ea *EnvAlias) Value(name string) (ent.Value, error) {
	return ea.selectValues.Get(name)
}

// QueryEnv queries the "env" edge of the EnvAlias entity.
func (ea *EnvAlias) QueryEnv() *EnvQuery {
	return NewEnvAliasClient(ea.config).QueryEnv(ea)
}

// Update returns a builder for updating this EnvAlias.
// Note that you need to call EnvAlias.Unwrap() before calling this method if this EnvAlias
// was returned from a transaction, and the transaction was committed or rolled back.
func (ea *EnvAlias) Update() *EnvAliasUpdateOne {
	return NewEnvAliasClient(ea.config).UpdateOne(ea)
}

// Unwrap unwraps the EnvAlias entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (ea *EnvAlias) Unwrap() *EnvAlias {
	_tx, ok := ea.config.driver.(*txDriver)
	if !ok {
		panic("models: EnvAlias is not a transactional entity")
	}
	ea.config.driver = _tx.drv
	return ea
}

// String implements the fmt.Stringer.
func (ea *EnvAlias) String() string {
	var builder strings.Builder
	builder.WriteString("EnvAlias(")
	builder.WriteString(fmt.Sprintf("id=%v, ", ea.ID))
	builder.WriteString("env_id=")
	builder.WriteString(ea.EnvID)
	builder.WriteString(", ")
	builder.WriteString("is_renamable=")
	builder.WriteString(fmt.Sprintf("%v", ea.IsRenamable))
	builder.WriteByte(')')
	return builder.String()
}

// EnvAliasSlice is a parsable slice of EnvAlias.
type EnvAliasSlice []*EnvAlias
