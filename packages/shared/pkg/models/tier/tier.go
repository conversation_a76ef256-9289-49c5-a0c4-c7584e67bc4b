// Code generated by ent, DO NOT EDIT.

package tier

import (
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the tier type in the database.
	Label = "tier"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldDiskMB holds the string denoting the disk_mb field in the database.
	FieldDiskMB = "disk_mb"
	// FieldConcurrentInstances holds the string denoting the concurrent_instances field in the database.
	FieldConcurrentInstances = "concurrent_instances"
	// FieldMaxLengthHours holds the string denoting the max_length_hours field in the database.
	FieldMaxLengthHours = "max_length_hours"
	// EdgeTeams holds the string denoting the teams edge name in mutations.
	EdgeTeams = "teams"
	// Table holds the table name of the tier in the database.
	Table = "tiers"
	// TeamsTable is the table that holds the teams relation/edge.
	TeamsTable = "teams"
	// TeamsInverseTable is the table name for the Team entity.
	// It exists in this package in order to avoid circular dependency with the "team" package.
	TeamsInverseTable = "teams"
	// TeamsColumn is the table column denoting the teams relation/edge.
	TeamsColumn = "tier"
)

// Columns holds all SQL columns for tier fields.
var Columns = []string{
	FieldID,
	FieldName,
	FieldDiskMB,
	FieldConcurrentInstances,
	FieldMaxLengthHours,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// OrderOption defines the ordering options for the Tier queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByDiskMB orders the results by the disk_mb field.
func ByDiskMB(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDiskMB, opts...).ToFunc()
}

// ByConcurrentInstances orders the results by the concurrent_instances field.
func ByConcurrentInstances(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldConcurrentInstances, opts...).ToFunc()
}

// ByMaxLengthHours orders the results by the max_length_hours field.
func ByMaxLengthHours(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMaxLengthHours, opts...).ToFunc()
}

// ByTeamsCount orders the results by teams count.
func ByTeamsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newTeamsStep(), opts...)
	}
}

// ByTeams orders the results by teams terms.
func ByTeams(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newTeamsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}
func newTeamsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(TeamsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, TeamsTable, TeamsColumn),
	)
}
