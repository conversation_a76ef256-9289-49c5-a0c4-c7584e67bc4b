// Code generated by ent, DO NOT EDIT.

package models

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/env"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/envalias"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/envbuild"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/internal"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/predicate"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/snapshot"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/team"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/user"
	"github.com/google/uuid"
)

// EnvUpdate is the builder for updating Env entities.
type EnvUpdate struct {
	config
	hooks     []Hook
	mutation  *EnvMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the EnvUpdate builder.
func (eu *EnvUpdate) Where(ps ...predicate.Env) *EnvUpdate {
	eu.mutation.Where(ps...)
	return eu
}

// SetUpdatedAt sets the "updated_at" field.
func (eu *EnvUpdate) SetUpdatedAt(t time.Time) *EnvUpdate {
	eu.mutation.SetUpdatedAt(t)
	return eu
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (eu *EnvUpdate) SetNillableUpdatedAt(t *time.Time) *EnvUpdate {
	if t != nil {
		eu.SetUpdatedAt(*t)
	}
	return eu
}

// SetTeamID sets the "team_id" field.
func (eu *EnvUpdate) SetTeamID(u uuid.UUID) *EnvUpdate {
	eu.mutation.SetTeamID(u)
	return eu
}

// SetNillableTeamID sets the "team_id" field if the given value is not nil.
func (eu *EnvUpdate) SetNillableTeamID(u *uuid.UUID) *EnvUpdate {
	if u != nil {
		eu.SetTeamID(*u)
	}
	return eu
}

// SetCreatedBy sets the "created_by" field.
func (eu *EnvUpdate) SetCreatedBy(u uuid.UUID) *EnvUpdate {
	eu.mutation.SetCreatedBy(u)
	return eu
}

// SetNillableCreatedBy sets the "created_by" field if the given value is not nil.
func (eu *EnvUpdate) SetNillableCreatedBy(u *uuid.UUID) *EnvUpdate {
	if u != nil {
		eu.SetCreatedBy(*u)
	}
	return eu
}

// ClearCreatedBy clears the value of the "created_by" field.
func (eu *EnvUpdate) ClearCreatedBy() *EnvUpdate {
	eu.mutation.ClearCreatedBy()
	return eu
}

// SetPublic sets the "public" field.
func (eu *EnvUpdate) SetPublic(b bool) *EnvUpdate {
	eu.mutation.SetPublic(b)
	return eu
}

// SetNillablePublic sets the "public" field if the given value is not nil.
func (eu *EnvUpdate) SetNillablePublic(b *bool) *EnvUpdate {
	if b != nil {
		eu.SetPublic(*b)
	}
	return eu
}

// SetBuildCount sets the "build_count" field.
func (eu *EnvUpdate) SetBuildCount(i int32) *EnvUpdate {
	eu.mutation.ResetBuildCount()
	eu.mutation.SetBuildCount(i)
	return eu
}

// SetNillableBuildCount sets the "build_count" field if the given value is not nil.
func (eu *EnvUpdate) SetNillableBuildCount(i *int32) *EnvUpdate {
	if i != nil {
		eu.SetBuildCount(*i)
	}
	return eu
}

// AddBuildCount adds i to the "build_count" field.
func (eu *EnvUpdate) AddBuildCount(i int32) *EnvUpdate {
	eu.mutation.AddBuildCount(i)
	return eu
}

// SetSpawnCount sets the "spawn_count" field.
func (eu *EnvUpdate) SetSpawnCount(i int64) *EnvUpdate {
	eu.mutation.ResetSpawnCount()
	eu.mutation.SetSpawnCount(i)
	return eu
}

// SetNillableSpawnCount sets the "spawn_count" field if the given value is not nil.
func (eu *EnvUpdate) SetNillableSpawnCount(i *int64) *EnvUpdate {
	if i != nil {
		eu.SetSpawnCount(*i)
	}
	return eu
}

// AddSpawnCount adds i to the "spawn_count" field.
func (eu *EnvUpdate) AddSpawnCount(i int64) *EnvUpdate {
	eu.mutation.AddSpawnCount(i)
	return eu
}

// SetLastSpawnedAt sets the "last_spawned_at" field.
func (eu *EnvUpdate) SetLastSpawnedAt(t time.Time) *EnvUpdate {
	eu.mutation.SetLastSpawnedAt(t)
	return eu
}

// SetNillableLastSpawnedAt sets the "last_spawned_at" field if the given value is not nil.
func (eu *EnvUpdate) SetNillableLastSpawnedAt(t *time.Time) *EnvUpdate {
	if t != nil {
		eu.SetLastSpawnedAt(*t)
	}
	return eu
}

// ClearLastSpawnedAt clears the value of the "last_spawned_at" field.
func (eu *EnvUpdate) ClearLastSpawnedAt() *EnvUpdate {
	eu.mutation.ClearLastSpawnedAt()
	return eu
}

// SetTeam sets the "team" edge to the Team entity.
func (eu *EnvUpdate) SetTeam(t *Team) *EnvUpdate {
	return eu.SetTeamID(t.ID)
}

// SetCreatorID sets the "creator" edge to the User entity by ID.
func (eu *EnvUpdate) SetCreatorID(id uuid.UUID) *EnvUpdate {
	eu.mutation.SetCreatorID(id)
	return eu
}

// SetNillableCreatorID sets the "creator" edge to the User entity by ID if the given value is not nil.
func (eu *EnvUpdate) SetNillableCreatorID(id *uuid.UUID) *EnvUpdate {
	if id != nil {
		eu = eu.SetCreatorID(*id)
	}
	return eu
}

// SetCreator sets the "creator" edge to the User entity.
func (eu *EnvUpdate) SetCreator(u *User) *EnvUpdate {
	return eu.SetCreatorID(u.ID)
}

// AddEnvAliasIDs adds the "env_aliases" edge to the EnvAlias entity by IDs.
func (eu *EnvUpdate) AddEnvAliasIDs(ids ...string) *EnvUpdate {
	eu.mutation.AddEnvAliasIDs(ids...)
	return eu
}

// AddEnvAliases adds the "env_aliases" edges to the EnvAlias entity.
func (eu *EnvUpdate) AddEnvAliases(e ...*EnvAlias) *EnvUpdate {
	ids := make([]string, len(e))
	for i := range e {
		ids[i] = e[i].ID
	}
	return eu.AddEnvAliasIDs(ids...)
}

// AddBuildIDs adds the "builds" edge to the EnvBuild entity by IDs.
func (eu *EnvUpdate) AddBuildIDs(ids ...uuid.UUID) *EnvUpdate {
	eu.mutation.AddBuildIDs(ids...)
	return eu
}

// AddBuilds adds the "builds" edges to the EnvBuild entity.
func (eu *EnvUpdate) AddBuilds(e ...*EnvBuild) *EnvUpdate {
	ids := make([]uuid.UUID, len(e))
	for i := range e {
		ids[i] = e[i].ID
	}
	return eu.AddBuildIDs(ids...)
}

// AddSnapshotIDs adds the "snapshots" edge to the Snapshot entity by IDs.
func (eu *EnvUpdate) AddSnapshotIDs(ids ...uuid.UUID) *EnvUpdate {
	eu.mutation.AddSnapshotIDs(ids...)
	return eu
}

// AddSnapshots adds the "snapshots" edges to the Snapshot entity.
func (eu *EnvUpdate) AddSnapshots(s ...*Snapshot) *EnvUpdate {
	ids := make([]uuid.UUID, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return eu.AddSnapshotIDs(ids...)
}

// Mutation returns the EnvMutation object of the builder.
func (eu *EnvUpdate) Mutation() *EnvMutation {
	return eu.mutation
}

// ClearTeam clears the "team" edge to the Team entity.
func (eu *EnvUpdate) ClearTeam() *EnvUpdate {
	eu.mutation.ClearTeam()
	return eu
}

// ClearCreator clears the "creator" edge to the User entity.
func (eu *EnvUpdate) ClearCreator() *EnvUpdate {
	eu.mutation.ClearCreator()
	return eu
}

// ClearEnvAliases clears all "env_aliases" edges to the EnvAlias entity.
func (eu *EnvUpdate) ClearEnvAliases() *EnvUpdate {
	eu.mutation.ClearEnvAliases()
	return eu
}

// RemoveEnvAliasIDs removes the "env_aliases" edge to EnvAlias entities by IDs.
func (eu *EnvUpdate) RemoveEnvAliasIDs(ids ...string) *EnvUpdate {
	eu.mutation.RemoveEnvAliasIDs(ids...)
	return eu
}

// RemoveEnvAliases removes "env_aliases" edges to EnvAlias entities.
func (eu *EnvUpdate) RemoveEnvAliases(e ...*EnvAlias) *EnvUpdate {
	ids := make([]string, len(e))
	for i := range e {
		ids[i] = e[i].ID
	}
	return eu.RemoveEnvAliasIDs(ids...)
}

// ClearBuilds clears all "builds" edges to the EnvBuild entity.
func (eu *EnvUpdate) ClearBuilds() *EnvUpdate {
	eu.mutation.ClearBuilds()
	return eu
}

// RemoveBuildIDs removes the "builds" edge to EnvBuild entities by IDs.
func (eu *EnvUpdate) RemoveBuildIDs(ids ...uuid.UUID) *EnvUpdate {
	eu.mutation.RemoveBuildIDs(ids...)
	return eu
}

// RemoveBuilds removes "builds" edges to EnvBuild entities.
func (eu *EnvUpdate) RemoveBuilds(e ...*EnvBuild) *EnvUpdate {
	ids := make([]uuid.UUID, len(e))
	for i := range e {
		ids[i] = e[i].ID
	}
	return eu.RemoveBuildIDs(ids...)
}

// ClearSnapshots clears all "snapshots" edges to the Snapshot entity.
func (eu *EnvUpdate) ClearSnapshots() *EnvUpdate {
	eu.mutation.ClearSnapshots()
	return eu
}

// RemoveSnapshotIDs removes the "snapshots" edge to Snapshot entities by IDs.
func (eu *EnvUpdate) RemoveSnapshotIDs(ids ...uuid.UUID) *EnvUpdate {
	eu.mutation.RemoveSnapshotIDs(ids...)
	return eu
}

// RemoveSnapshots removes "snapshots" edges to Snapshot entities.
func (eu *EnvUpdate) RemoveSnapshots(s ...*Snapshot) *EnvUpdate {
	ids := make([]uuid.UUID, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return eu.RemoveSnapshotIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (eu *EnvUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, eu.sqlSave, eu.mutation, eu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (eu *EnvUpdate) SaveX(ctx context.Context) int {
	affected, err := eu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (eu *EnvUpdate) Exec(ctx context.Context) error {
	_, err := eu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (eu *EnvUpdate) ExecX(ctx context.Context) {
	if err := eu.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (eu *EnvUpdate) check() error {
	if _, ok := eu.mutation.TeamID(); eu.mutation.TeamCleared() && !ok {
		return errors.New(`models: clearing a required unique edge "Env.team"`)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (eu *EnvUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *EnvUpdate {
	eu.modifiers = append(eu.modifiers, modifiers...)
	return eu
}

func (eu *EnvUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := eu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(env.Table, env.Columns, sqlgraph.NewFieldSpec(env.FieldID, field.TypeString))
	if ps := eu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := eu.mutation.UpdatedAt(); ok {
		_spec.SetField(env.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := eu.mutation.Public(); ok {
		_spec.SetField(env.FieldPublic, field.TypeBool, value)
	}
	if value, ok := eu.mutation.BuildCount(); ok {
		_spec.SetField(env.FieldBuildCount, field.TypeInt32, value)
	}
	if value, ok := eu.mutation.AddedBuildCount(); ok {
		_spec.AddField(env.FieldBuildCount, field.TypeInt32, value)
	}
	if value, ok := eu.mutation.SpawnCount(); ok {
		_spec.SetField(env.FieldSpawnCount, field.TypeInt64, value)
	}
	if value, ok := eu.mutation.AddedSpawnCount(); ok {
		_spec.AddField(env.FieldSpawnCount, field.TypeInt64, value)
	}
	if value, ok := eu.mutation.LastSpawnedAt(); ok {
		_spec.SetField(env.FieldLastSpawnedAt, field.TypeTime, value)
	}
	if eu.mutation.LastSpawnedAtCleared() {
		_spec.ClearField(env.FieldLastSpawnedAt, field.TypeTime)
	}
	if eu.mutation.TeamCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   env.TeamTable,
			Columns: []string{env.TeamColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(team.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = eu.schemaConfig.Env
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := eu.mutation.TeamIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   env.TeamTable,
			Columns: []string{env.TeamColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(team.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = eu.schemaConfig.Env
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if eu.mutation.CreatorCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   env.CreatorTable,
			Columns: []string{env.CreatorColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = eu.schemaConfig.Env
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := eu.mutation.CreatorIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   env.CreatorTable,
			Columns: []string{env.CreatorColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = eu.schemaConfig.Env
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if eu.mutation.EnvAliasesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   env.EnvAliasesTable,
			Columns: []string{env.EnvAliasesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(envalias.FieldID, field.TypeString),
			},
		}
		edge.Schema = eu.schemaConfig.EnvAlias
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := eu.mutation.RemovedEnvAliasesIDs(); len(nodes) > 0 && !eu.mutation.EnvAliasesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   env.EnvAliasesTable,
			Columns: []string{env.EnvAliasesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(envalias.FieldID, field.TypeString),
			},
		}
		edge.Schema = eu.schemaConfig.EnvAlias
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := eu.mutation.EnvAliasesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   env.EnvAliasesTable,
			Columns: []string{env.EnvAliasesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(envalias.FieldID, field.TypeString),
			},
		}
		edge.Schema = eu.schemaConfig.EnvAlias
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if eu.mutation.BuildsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   env.BuildsTable,
			Columns: []string{env.BuildsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(envbuild.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = eu.schemaConfig.EnvBuild
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := eu.mutation.RemovedBuildsIDs(); len(nodes) > 0 && !eu.mutation.BuildsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   env.BuildsTable,
			Columns: []string{env.BuildsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(envbuild.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = eu.schemaConfig.EnvBuild
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := eu.mutation.BuildsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   env.BuildsTable,
			Columns: []string{env.BuildsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(envbuild.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = eu.schemaConfig.EnvBuild
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if eu.mutation.SnapshotsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   env.SnapshotsTable,
			Columns: []string{env.SnapshotsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(snapshot.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = eu.schemaConfig.Snapshot
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := eu.mutation.RemovedSnapshotsIDs(); len(nodes) > 0 && !eu.mutation.SnapshotsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   env.SnapshotsTable,
			Columns: []string{env.SnapshotsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(snapshot.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = eu.schemaConfig.Snapshot
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := eu.mutation.SnapshotsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   env.SnapshotsTable,
			Columns: []string{env.SnapshotsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(snapshot.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = eu.schemaConfig.Snapshot
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.Node.Schema = eu.schemaConfig.Env
	ctx = internal.NewSchemaConfigContext(ctx, eu.schemaConfig)
	_spec.AddModifiers(eu.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, eu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{env.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	eu.mutation.done = true
	return n, nil
}

// EnvUpdateOne is the builder for updating a single Env entity.
type EnvUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *EnvMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetUpdatedAt sets the "updated_at" field.
func (euo *EnvUpdateOne) SetUpdatedAt(t time.Time) *EnvUpdateOne {
	euo.mutation.SetUpdatedAt(t)
	return euo
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (euo *EnvUpdateOne) SetNillableUpdatedAt(t *time.Time) *EnvUpdateOne {
	if t != nil {
		euo.SetUpdatedAt(*t)
	}
	return euo
}

// SetTeamID sets the "team_id" field.
func (euo *EnvUpdateOne) SetTeamID(u uuid.UUID) *EnvUpdateOne {
	euo.mutation.SetTeamID(u)
	return euo
}

// SetNillableTeamID sets the "team_id" field if the given value is not nil.
func (euo *EnvUpdateOne) SetNillableTeamID(u *uuid.UUID) *EnvUpdateOne {
	if u != nil {
		euo.SetTeamID(*u)
	}
	return euo
}

// SetCreatedBy sets the "created_by" field.
func (euo *EnvUpdateOne) SetCreatedBy(u uuid.UUID) *EnvUpdateOne {
	euo.mutation.SetCreatedBy(u)
	return euo
}

// SetNillableCreatedBy sets the "created_by" field if the given value is not nil.
func (euo *EnvUpdateOne) SetNillableCreatedBy(u *uuid.UUID) *EnvUpdateOne {
	if u != nil {
		euo.SetCreatedBy(*u)
	}
	return euo
}

// ClearCreatedBy clears the value of the "created_by" field.
func (euo *EnvUpdateOne) ClearCreatedBy() *EnvUpdateOne {
	euo.mutation.ClearCreatedBy()
	return euo
}

// SetPublic sets the "public" field.
func (euo *EnvUpdateOne) SetPublic(b bool) *EnvUpdateOne {
	euo.mutation.SetPublic(b)
	return euo
}

// SetNillablePublic sets the "public" field if the given value is not nil.
func (euo *EnvUpdateOne) SetNillablePublic(b *bool) *EnvUpdateOne {
	if b != nil {
		euo.SetPublic(*b)
	}
	return euo
}

// SetBuildCount sets the "build_count" field.
func (euo *EnvUpdateOne) SetBuildCount(i int32) *EnvUpdateOne {
	euo.mutation.ResetBuildCount()
	euo.mutation.SetBuildCount(i)
	return euo
}

// SetNillableBuildCount sets the "build_count" field if the given value is not nil.
func (euo *EnvUpdateOne) SetNillableBuildCount(i *int32) *EnvUpdateOne {
	if i != nil {
		euo.SetBuildCount(*i)
	}
	return euo
}

// AddBuildCount adds i to the "build_count" field.
func (euo *EnvUpdateOne) AddBuildCount(i int32) *EnvUpdateOne {
	euo.mutation.AddBuildCount(i)
	return euo
}

// SetSpawnCount sets the "spawn_count" field.
func (euo *EnvUpdateOne) SetSpawnCount(i int64) *EnvUpdateOne {
	euo.mutation.ResetSpawnCount()
	euo.mutation.SetSpawnCount(i)
	return euo
}

// SetNillableSpawnCount sets the "spawn_count" field if the given value is not nil.
func (euo *EnvUpdateOne) SetNillableSpawnCount(i *int64) *EnvUpdateOne {
	if i != nil {
		euo.SetSpawnCount(*i)
	}
	return euo
}

// AddSpawnCount adds i to the "spawn_count" field.
func (euo *EnvUpdateOne) AddSpawnCount(i int64) *EnvUpdateOne {
	euo.mutation.AddSpawnCount(i)
	return euo
}

// SetLastSpawnedAt sets the "last_spawned_at" field.
func (euo *EnvUpdateOne) SetLastSpawnedAt(t time.Time) *EnvUpdateOne {
	euo.mutation.SetLastSpawnedAt(t)
	return euo
}

// SetNillableLastSpawnedAt sets the "last_spawned_at" field if the given value is not nil.
func (euo *EnvUpdateOne) SetNillableLastSpawnedAt(t *time.Time) *EnvUpdateOne {
	if t != nil {
		euo.SetLastSpawnedAt(*t)
	}
	return euo
}

// ClearLastSpawnedAt clears the value of the "last_spawned_at" field.
func (euo *EnvUpdateOne) ClearLastSpawnedAt() *EnvUpdateOne {
	euo.mutation.ClearLastSpawnedAt()
	return euo
}

// SetTeam sets the "team" edge to the Team entity.
func (euo *EnvUpdateOne) SetTeam(t *Team) *EnvUpdateOne {
	return euo.SetTeamID(t.ID)
}

// SetCreatorID sets the "creator" edge to the User entity by ID.
func (euo *EnvUpdateOne) SetCreatorID(id uuid.UUID) *EnvUpdateOne {
	euo.mutation.SetCreatorID(id)
	return euo
}

// SetNillableCreatorID sets the "creator" edge to the User entity by ID if the given value is not nil.
func (euo *EnvUpdateOne) SetNillableCreatorID(id *uuid.UUID) *EnvUpdateOne {
	if id != nil {
		euo = euo.SetCreatorID(*id)
	}
	return euo
}

// SetCreator sets the "creator" edge to the User entity.
func (euo *EnvUpdateOne) SetCreator(u *User) *EnvUpdateOne {
	return euo.SetCreatorID(u.ID)
}

// AddEnvAliasIDs adds the "env_aliases" edge to the EnvAlias entity by IDs.
func (euo *EnvUpdateOne) AddEnvAliasIDs(ids ...string) *EnvUpdateOne {
	euo.mutation.AddEnvAliasIDs(ids...)
	return euo
}

// AddEnvAliases adds the "env_aliases" edges to the EnvAlias entity.
func (euo *EnvUpdateOne) AddEnvAliases(e ...*EnvAlias) *EnvUpdateOne {
	ids := make([]string, len(e))
	for i := range e {
		ids[i] = e[i].ID
	}
	return euo.AddEnvAliasIDs(ids...)
}

// AddBuildIDs adds the "builds" edge to the EnvBuild entity by IDs.
func (euo *EnvUpdateOne) AddBuildIDs(ids ...uuid.UUID) *EnvUpdateOne {
	euo.mutation.AddBuildIDs(ids...)
	return euo
}

// AddBuilds adds the "builds" edges to the EnvBuild entity.
func (euo *EnvUpdateOne) AddBuilds(e ...*EnvBuild) *EnvUpdateOne {
	ids := make([]uuid.UUID, len(e))
	for i := range e {
		ids[i] = e[i].ID
	}
	return euo.AddBuildIDs(ids...)
}

// AddSnapshotIDs adds the "snapshots" edge to the Snapshot entity by IDs.
func (euo *EnvUpdateOne) AddSnapshotIDs(ids ...uuid.UUID) *EnvUpdateOne {
	euo.mutation.AddSnapshotIDs(ids...)
	return euo
}

// AddSnapshots adds the "snapshots" edges to the Snapshot entity.
func (euo *EnvUpdateOne) AddSnapshots(s ...*Snapshot) *EnvUpdateOne {
	ids := make([]uuid.UUID, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return euo.AddSnapshotIDs(ids...)
}

// Mutation returns the EnvMutation object of the builder.
func (euo *EnvUpdateOne) Mutation() *EnvMutation {
	return euo.mutation
}

// ClearTeam clears the "team" edge to the Team entity.
func (euo *EnvUpdateOne) ClearTeam() *EnvUpdateOne {
	euo.mutation.ClearTeam()
	return euo
}

// ClearCreator clears the "creator" edge to the User entity.
func (euo *EnvUpdateOne) ClearCreator() *EnvUpdateOne {
	euo.mutation.ClearCreator()
	return euo
}

// ClearEnvAliases clears all "env_aliases" edges to the EnvAlias entity.
func (euo *EnvUpdateOne) ClearEnvAliases() *EnvUpdateOne {
	euo.mutation.ClearEnvAliases()
	return euo
}

// RemoveEnvAliasIDs removes the "env_aliases" edge to EnvAlias entities by IDs.
func (euo *EnvUpdateOne) RemoveEnvAliasIDs(ids ...string) *EnvUpdateOne {
	euo.mutation.RemoveEnvAliasIDs(ids...)
	return euo
}

// RemoveEnvAliases removes "env_aliases" edges to EnvAlias entities.
func (euo *EnvUpdateOne) RemoveEnvAliases(e ...*EnvAlias) *EnvUpdateOne {
	ids := make([]string, len(e))
	for i := range e {
		ids[i] = e[i].ID
	}
	return euo.RemoveEnvAliasIDs(ids...)
}

// ClearBuilds clears all "builds" edges to the EnvBuild entity.
func (euo *EnvUpdateOne) ClearBuilds() *EnvUpdateOne {
	euo.mutation.ClearBuilds()
	return euo
}

// RemoveBuildIDs removes the "builds" edge to EnvBuild entities by IDs.
func (euo *EnvUpdateOne) RemoveBuildIDs(ids ...uuid.UUID) *EnvUpdateOne {
	euo.mutation.RemoveBuildIDs(ids...)
	return euo
}

// RemoveBuilds removes "builds" edges to EnvBuild entities.
func (euo *EnvUpdateOne) RemoveBuilds(e ...*EnvBuild) *EnvUpdateOne {
	ids := make([]uuid.UUID, len(e))
	for i := range e {
		ids[i] = e[i].ID
	}
	return euo.RemoveBuildIDs(ids...)
}

// ClearSnapshots clears all "snapshots" edges to the Snapshot entity.
func (euo *EnvUpdateOne) ClearSnapshots() *EnvUpdateOne {
	euo.mutation.ClearSnapshots()
	return euo
}

// RemoveSnapshotIDs removes the "snapshots" edge to Snapshot entities by IDs.
func (euo *EnvUpdateOne) RemoveSnapshotIDs(ids ...uuid.UUID) *EnvUpdateOne {
	euo.mutation.RemoveSnapshotIDs(ids...)
	return euo
}

// RemoveSnapshots removes "snapshots" edges to Snapshot entities.
func (euo *EnvUpdateOne) RemoveSnapshots(s ...*Snapshot) *EnvUpdateOne {
	ids := make([]uuid.UUID, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return euo.RemoveSnapshotIDs(ids...)
}

// Where appends a list predicates to the EnvUpdate builder.
func (euo *EnvUpdateOne) Where(ps ...predicate.Env) *EnvUpdateOne {
	euo.mutation.Where(ps...)
	return euo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (euo *EnvUpdateOne) Select(field string, fields ...string) *EnvUpdateOne {
	euo.fields = append([]string{field}, fields...)
	return euo
}

// Save executes the query and returns the updated Env entity.
func (euo *EnvUpdateOne) Save(ctx context.Context) (*Env, error) {
	return withHooks(ctx, euo.sqlSave, euo.mutation, euo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (euo *EnvUpdateOne) SaveX(ctx context.Context) *Env {
	node, err := euo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (euo *EnvUpdateOne) Exec(ctx context.Context) error {
	_, err := euo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (euo *EnvUpdateOne) ExecX(ctx context.Context) {
	if err := euo.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (euo *EnvUpdateOne) check() error {
	if _, ok := euo.mutation.TeamID(); euo.mutation.TeamCleared() && !ok {
		return errors.New(`models: clearing a required unique edge "Env.team"`)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (euo *EnvUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *EnvUpdateOne {
	euo.modifiers = append(euo.modifiers, modifiers...)
	return euo
}

func (euo *EnvUpdateOne) sqlSave(ctx context.Context) (_node *Env, err error) {
	if err := euo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(env.Table, env.Columns, sqlgraph.NewFieldSpec(env.FieldID, field.TypeString))
	id, ok := euo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`models: missing "Env.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := euo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, env.FieldID)
		for _, f := range fields {
			if !env.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("models: invalid field %q for query", f)}
			}
			if f != env.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := euo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := euo.mutation.UpdatedAt(); ok {
		_spec.SetField(env.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := euo.mutation.Public(); ok {
		_spec.SetField(env.FieldPublic, field.TypeBool, value)
	}
	if value, ok := euo.mutation.BuildCount(); ok {
		_spec.SetField(env.FieldBuildCount, field.TypeInt32, value)
	}
	if value, ok := euo.mutation.AddedBuildCount(); ok {
		_spec.AddField(env.FieldBuildCount, field.TypeInt32, value)
	}
	if value, ok := euo.mutation.SpawnCount(); ok {
		_spec.SetField(env.FieldSpawnCount, field.TypeInt64, value)
	}
	if value, ok := euo.mutation.AddedSpawnCount(); ok {
		_spec.AddField(env.FieldSpawnCount, field.TypeInt64, value)
	}
	if value, ok := euo.mutation.LastSpawnedAt(); ok {
		_spec.SetField(env.FieldLastSpawnedAt, field.TypeTime, value)
	}
	if euo.mutation.LastSpawnedAtCleared() {
		_spec.ClearField(env.FieldLastSpawnedAt, field.TypeTime)
	}
	if euo.mutation.TeamCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   env.TeamTable,
			Columns: []string{env.TeamColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(team.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = euo.schemaConfig.Env
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := euo.mutation.TeamIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   env.TeamTable,
			Columns: []string{env.TeamColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(team.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = euo.schemaConfig.Env
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if euo.mutation.CreatorCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   env.CreatorTable,
			Columns: []string{env.CreatorColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = euo.schemaConfig.Env
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := euo.mutation.CreatorIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   env.CreatorTable,
			Columns: []string{env.CreatorColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = euo.schemaConfig.Env
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if euo.mutation.EnvAliasesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   env.EnvAliasesTable,
			Columns: []string{env.EnvAliasesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(envalias.FieldID, field.TypeString),
			},
		}
		edge.Schema = euo.schemaConfig.EnvAlias
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := euo.mutation.RemovedEnvAliasesIDs(); len(nodes) > 0 && !euo.mutation.EnvAliasesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   env.EnvAliasesTable,
			Columns: []string{env.EnvAliasesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(envalias.FieldID, field.TypeString),
			},
		}
		edge.Schema = euo.schemaConfig.EnvAlias
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := euo.mutation.EnvAliasesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   env.EnvAliasesTable,
			Columns: []string{env.EnvAliasesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(envalias.FieldID, field.TypeString),
			},
		}
		edge.Schema = euo.schemaConfig.EnvAlias
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if euo.mutation.BuildsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   env.BuildsTable,
			Columns: []string{env.BuildsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(envbuild.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = euo.schemaConfig.EnvBuild
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := euo.mutation.RemovedBuildsIDs(); len(nodes) > 0 && !euo.mutation.BuildsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   env.BuildsTable,
			Columns: []string{env.BuildsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(envbuild.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = euo.schemaConfig.EnvBuild
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := euo.mutation.BuildsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   env.BuildsTable,
			Columns: []string{env.BuildsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(envbuild.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = euo.schemaConfig.EnvBuild
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if euo.mutation.SnapshotsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   env.SnapshotsTable,
			Columns: []string{env.SnapshotsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(snapshot.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = euo.schemaConfig.Snapshot
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := euo.mutation.RemovedSnapshotsIDs(); len(nodes) > 0 && !euo.mutation.SnapshotsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   env.SnapshotsTable,
			Columns: []string{env.SnapshotsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(snapshot.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = euo.schemaConfig.Snapshot
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := euo.mutation.SnapshotsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   env.SnapshotsTable,
			Columns: []string{env.SnapshotsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(snapshot.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = euo.schemaConfig.Snapshot
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.Node.Schema = euo.schemaConfig.Env
	ctx = internal.NewSchemaConfigContext(ctx, euo.schemaConfig)
	_spec.AddModifiers(euo.modifiers...)
	_node = &Env{config: euo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, euo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{env.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	euo.mutation.done = true
	return _node, nil
}
