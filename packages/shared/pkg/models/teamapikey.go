// Code generated by ent, DO NOT EDIT.

package models

import (
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/team"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/teamapikey"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/user"
	"github.com/google/uuid"
)

// TeamAPIKey is the model entity for the TeamAPIKey schema.
type TeamAPIKey struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// APIKey holds the value of the "api_key" field.
	APIKey string `json:"-"`
	// APIKeyHash holds the value of the "api_key_hash" field.
	APIKeyHash string `json:"-"`
	// APIKeyPrefix holds the value of the "api_key_prefix" field.
	APIKeyPrefix string `json:"api_key_prefix,omitempty"`
	// APIKeyLength holds the value of the "api_key_length" field.
	APIKeyLength int `json:"api_key_length,omitempty"`
	// APIKeyMaskPrefix holds the value of the "api_key_mask_prefix" field.
	APIKeyMaskPrefix string `json:"api_key_mask_prefix,omitempty"`
	// APIKeyMaskSuffix holds the value of the "api_key_mask_suffix" field.
	APIKeyMaskSuffix string `json:"api_key_mask_suffix,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt *time.Time `json:"updated_at,omitempty"`
	// TeamID holds the value of the "team_id" field.
	TeamID uuid.UUID `json:"team_id,omitempty"`
	// Name holds the value of the "name" field.
	Name string `json:"name,omitempty"`
	// CreatedBy holds the value of the "created_by" field.
	CreatedBy *uuid.UUID `json:"created_by,omitempty"`
	// LastUsed holds the value of the "last_used" field.
	LastUsed *time.Time `json:"last_used,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the TeamAPIKeyQuery when eager-loading is set.
	Edges        TeamAPIKeyEdges `json:"edges"`
	selectValues sql.SelectValues
}

// TeamAPIKeyEdges holds the relations/edges for other nodes in the graph.
type TeamAPIKeyEdges struct {
	// Team holds the value of the team edge.
	Team *Team `json:"team,omitempty"`
	// Creator holds the value of the creator edge.
	Creator *User `json:"creator,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [2]bool
}

// TeamOrErr returns the Team value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e TeamAPIKeyEdges) TeamOrErr() (*Team, error) {
	if e.loadedTypes[0] {
		if e.Team == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: team.Label}
		}
		return e.Team, nil
	}
	return nil, &NotLoadedError{edge: "team"}
}

// CreatorOrErr returns the Creator value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e TeamAPIKeyEdges) CreatorOrErr() (*User, error) {
	if e.loadedTypes[1] {
		if e.Creator == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: user.Label}
		}
		return e.Creator, nil
	}
	return nil, &NotLoadedError{edge: "creator"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*TeamAPIKey) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case teamapikey.FieldCreatedBy:
			values[i] = &sql.NullScanner{S: new(uuid.UUID)}
		case teamapikey.FieldAPIKeyLength:
			values[i] = new(sql.NullInt64)
		case teamapikey.FieldAPIKey, teamapikey.FieldAPIKeyHash, teamapikey.FieldAPIKeyPrefix, teamapikey.FieldAPIKeyMaskPrefix, teamapikey.FieldAPIKeyMaskSuffix, teamapikey.FieldName:
			values[i] = new(sql.NullString)
		case teamapikey.FieldCreatedAt, teamapikey.FieldUpdatedAt, teamapikey.FieldLastUsed:
			values[i] = new(sql.NullTime)
		case teamapikey.FieldID, teamapikey.FieldTeamID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the TeamAPIKey fields.
func (tak *TeamAPIKey) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case teamapikey.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				tak.ID = *value
			}
		case teamapikey.FieldAPIKey:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field api_key", values[i])
			} else if value.Valid {
				tak.APIKey = value.String
			}
		case teamapikey.FieldAPIKeyHash:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field api_key_hash", values[i])
			} else if value.Valid {
				tak.APIKeyHash = value.String
			}
		case teamapikey.FieldAPIKeyPrefix:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field api_key_prefix", values[i])
			} else if value.Valid {
				tak.APIKeyPrefix = value.String
			}
		case teamapikey.FieldAPIKeyLength:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field api_key_length", values[i])
			} else if value.Valid {
				tak.APIKeyLength = int(value.Int64)
			}
		case teamapikey.FieldAPIKeyMaskPrefix:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field api_key_mask_prefix", values[i])
			} else if value.Valid {
				tak.APIKeyMaskPrefix = value.String
			}
		case teamapikey.FieldAPIKeyMaskSuffix:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field api_key_mask_suffix", values[i])
			} else if value.Valid {
				tak.APIKeyMaskSuffix = value.String
			}
		case teamapikey.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				tak.CreatedAt = value.Time
			}
		case teamapikey.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				tak.UpdatedAt = new(time.Time)
				*tak.UpdatedAt = value.Time
			}
		case teamapikey.FieldTeamID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field team_id", values[i])
			} else if value != nil {
				tak.TeamID = *value
			}
		case teamapikey.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				tak.Name = value.String
			}
		case teamapikey.FieldCreatedBy:
			if value, ok := values[i].(*sql.NullScanner); !ok {
				return fmt.Errorf("unexpected type %T for field created_by", values[i])
			} else if value.Valid {
				tak.CreatedBy = new(uuid.UUID)
				*tak.CreatedBy = *value.S.(*uuid.UUID)
			}
		case teamapikey.FieldLastUsed:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field last_used", values[i])
			} else if value.Valid {
				tak.LastUsed = new(time.Time)
				*tak.LastUsed = value.Time
			}
		default:
			tak.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the TeamAPIKey.
// This includes values selected through modifiers, order, etc.
func (tak *TeamAPIKey) Value(name string) (ent.Value, error) {
	return tak.selectValues.Get(name)
}

// QueryTeam queries the "team" edge of the TeamAPIKey entity.
func (tak *TeamAPIKey) QueryTeam() *TeamQuery {
	return NewTeamAPIKeyClient(tak.config).QueryTeam(tak)
}

// QueryCreator queries the "creator" edge of the TeamAPIKey entity.
func (tak *TeamAPIKey) QueryCreator() *UserQuery {
	return NewTeamAPIKeyClient(tak.config).QueryCreator(tak)
}

// Update returns a builder for updating this TeamAPIKey.
// Note that you need to call TeamAPIKey.Unwrap() before calling this method if this TeamAPIKey
// was returned from a transaction, and the transaction was committed or rolled back.
func (tak *TeamAPIKey) Update() *TeamAPIKeyUpdateOne {
	return NewTeamAPIKeyClient(tak.config).UpdateOne(tak)
}

// Unwrap unwraps the TeamAPIKey entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (tak *TeamAPIKey) Unwrap() *TeamAPIKey {
	_tx, ok := tak.config.driver.(*txDriver)
	if !ok {
		panic("models: TeamAPIKey is not a transactional entity")
	}
	tak.config.driver = _tx.drv
	return tak
}

// String implements the fmt.Stringer.
func (tak *TeamAPIKey) String() string {
	var builder strings.Builder
	builder.WriteString("TeamAPIKey(")
	builder.WriteString(fmt.Sprintf("id=%v, ", tak.ID))
	builder.WriteString("api_key=<sensitive>")
	builder.WriteString(", ")
	builder.WriteString("api_key_hash=<sensitive>")
	builder.WriteString(", ")
	builder.WriteString("api_key_prefix=")
	builder.WriteString(tak.APIKeyPrefix)
	builder.WriteString(", ")
	builder.WriteString("api_key_length=")
	builder.WriteString(fmt.Sprintf("%v", tak.APIKeyLength))
	builder.WriteString(", ")
	builder.WriteString("api_key_mask_prefix=")
	builder.WriteString(tak.APIKeyMaskPrefix)
	builder.WriteString(", ")
	builder.WriteString("api_key_mask_suffix=")
	builder.WriteString(tak.APIKeyMaskSuffix)
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(tak.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := tak.UpdatedAt; v != nil {
		builder.WriteString("updated_at=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	builder.WriteString("team_id=")
	builder.WriteString(fmt.Sprintf("%v", tak.TeamID))
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(tak.Name)
	builder.WriteString(", ")
	if v := tak.CreatedBy; v != nil {
		builder.WriteString("created_by=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := tak.LastUsed; v != nil {
		builder.WriteString("last_used=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteByte(')')
	return builder.String()
}

// TeamAPIKeys is a parsable slice of TeamAPIKey.
type TeamAPIKeys []*TeamAPIKey
