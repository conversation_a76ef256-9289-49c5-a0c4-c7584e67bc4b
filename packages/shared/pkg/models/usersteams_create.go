// Code generated by ent, DO NOT EDIT.

package models

import (
	"context"
	"errors"
	"fmt"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/team"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/user"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/usersteams"
	"github.com/google/uuid"
)

// UsersTeamsCreate is the builder for creating a UsersTeams entity.
type UsersTeamsCreate struct {
	config
	mutation *UsersTeamsMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetUserID sets the "user_id" field.
func (utc *UsersTeamsCreate) SetUserID(u uuid.UUID) *UsersTeamsCreate {
	utc.mutation.SetUserID(u)
	return utc
}

// SetTeamID sets the "team_id" field.
func (utc *UsersTeamsCreate) SetTeamID(u uuid.UUID) *UsersTeamsCreate {
	utc.mutation.SetTeamID(u)
	return utc
}

// SetIsDefault sets the "is_default" field.
func (utc *UsersTeamsCreate) SetIsDefault(b bool) *UsersTeamsCreate {
	utc.mutation.SetIsDefault(b)
	return utc
}

// SetNillableIsDefault sets the "is_default" field if the given value is not nil.
func (utc *UsersTeamsCreate) SetNillableIsDefault(b *bool) *UsersTeamsCreate {
	if b != nil {
		utc.SetIsDefault(*b)
	}
	return utc
}

// SetUsersID sets the "users" edge to the User entity by ID.
func (utc *UsersTeamsCreate) SetUsersID(id uuid.UUID) *UsersTeamsCreate {
	utc.mutation.SetUsersID(id)
	return utc
}

// SetUsers sets the "users" edge to the User entity.
func (utc *UsersTeamsCreate) SetUsers(u *User) *UsersTeamsCreate {
	return utc.SetUsersID(u.ID)
}

// SetTeamsID sets the "teams" edge to the Team entity by ID.
func (utc *UsersTeamsCreate) SetTeamsID(id uuid.UUID) *UsersTeamsCreate {
	utc.mutation.SetTeamsID(id)
	return utc
}

// SetTeams sets the "teams" edge to the Team entity.
func (utc *UsersTeamsCreate) SetTeams(t *Team) *UsersTeamsCreate {
	return utc.SetTeamsID(t.ID)
}

// Mutation returns the UsersTeamsMutation object of the builder.
func (utc *UsersTeamsCreate) Mutation() *UsersTeamsMutation {
	return utc.mutation
}

// Save creates the UsersTeams in the database.
func (utc *UsersTeamsCreate) Save(ctx context.Context) (*UsersTeams, error) {
	utc.defaults()
	return withHooks(ctx, utc.sqlSave, utc.mutation, utc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (utc *UsersTeamsCreate) SaveX(ctx context.Context) *UsersTeams {
	v, err := utc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (utc *UsersTeamsCreate) Exec(ctx context.Context) error {
	_, err := utc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (utc *UsersTeamsCreate) ExecX(ctx context.Context) {
	if err := utc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (utc *UsersTeamsCreate) defaults() {
	if _, ok := utc.mutation.IsDefault(); !ok {
		v := usersteams.DefaultIsDefault
		utc.mutation.SetIsDefault(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (utc *UsersTeamsCreate) check() error {
	if _, ok := utc.mutation.UserID(); !ok {
		return &ValidationError{Name: "user_id", err: errors.New(`models: missing required field "UsersTeams.user_id"`)}
	}
	if _, ok := utc.mutation.TeamID(); !ok {
		return &ValidationError{Name: "team_id", err: errors.New(`models: missing required field "UsersTeams.team_id"`)}
	}
	if _, ok := utc.mutation.IsDefault(); !ok {
		return &ValidationError{Name: "is_default", err: errors.New(`models: missing required field "UsersTeams.is_default"`)}
	}
	if _, ok := utc.mutation.UsersID(); !ok {
		return &ValidationError{Name: "users", err: errors.New(`models: missing required edge "UsersTeams.users"`)}
	}
	if _, ok := utc.mutation.TeamsID(); !ok {
		return &ValidationError{Name: "teams", err: errors.New(`models: missing required edge "UsersTeams.teams"`)}
	}
	return nil
}

func (utc *UsersTeamsCreate) sqlSave(ctx context.Context) (*UsersTeams, error) {
	if err := utc.check(); err != nil {
		return nil, err
	}
	_node, _spec := utc.createSpec()
	if err := sqlgraph.CreateNode(ctx, utc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	utc.mutation.id = &_node.ID
	utc.mutation.done = true
	return _node, nil
}

func (utc *UsersTeamsCreate) createSpec() (*UsersTeams, *sqlgraph.CreateSpec) {
	var (
		_node = &UsersTeams{config: utc.config}
		_spec = sqlgraph.NewCreateSpec(usersteams.Table, sqlgraph.NewFieldSpec(usersteams.FieldID, field.TypeInt))
	)
	_spec.Schema = utc.schemaConfig.UsersTeams
	_spec.OnConflict = utc.conflict
	if value, ok := utc.mutation.IsDefault(); ok {
		_spec.SetField(usersteams.FieldIsDefault, field.TypeBool, value)
		_node.IsDefault = value
	}
	if nodes := utc.mutation.UsersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   usersteams.UsersTable,
			Columns: []string{usersteams.UsersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = utc.schemaConfig.UsersTeams
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.UserID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := utc.mutation.TeamsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   usersteams.TeamsTable,
			Columns: []string{usersteams.TeamsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(team.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = utc.schemaConfig.UsersTeams
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.TeamID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.UsersTeams.Create().
//		SetUserID(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.UsersTeamsUpsert) {
//			SetUserID(v+v).
//		}).
//		Exec(ctx)
func (utc *UsersTeamsCreate) OnConflict(opts ...sql.ConflictOption) *UsersTeamsUpsertOne {
	utc.conflict = opts
	return &UsersTeamsUpsertOne{
		create: utc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.UsersTeams.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (utc *UsersTeamsCreate) OnConflictColumns(columns ...string) *UsersTeamsUpsertOne {
	utc.conflict = append(utc.conflict, sql.ConflictColumns(columns...))
	return &UsersTeamsUpsertOne{
		create: utc,
	}
}

type (
	// UsersTeamsUpsertOne is the builder for "upsert"-ing
	//  one UsersTeams node.
	UsersTeamsUpsertOne struct {
		create *UsersTeamsCreate
	}

	// UsersTeamsUpsert is the "OnConflict" setter.
	UsersTeamsUpsert struct {
		*sql.UpdateSet
	}
)

// SetUserID sets the "user_id" field.
func (u *UsersTeamsUpsert) SetUserID(v uuid.UUID) *UsersTeamsUpsert {
	u.Set(usersteams.FieldUserID, v)
	return u
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *UsersTeamsUpsert) UpdateUserID() *UsersTeamsUpsert {
	u.SetExcluded(usersteams.FieldUserID)
	return u
}

// SetTeamID sets the "team_id" field.
func (u *UsersTeamsUpsert) SetTeamID(v uuid.UUID) *UsersTeamsUpsert {
	u.Set(usersteams.FieldTeamID, v)
	return u
}

// UpdateTeamID sets the "team_id" field to the value that was provided on create.
func (u *UsersTeamsUpsert) UpdateTeamID() *UsersTeamsUpsert {
	u.SetExcluded(usersteams.FieldTeamID)
	return u
}

// SetIsDefault sets the "is_default" field.
func (u *UsersTeamsUpsert) SetIsDefault(v bool) *UsersTeamsUpsert {
	u.Set(usersteams.FieldIsDefault, v)
	return u
}

// UpdateIsDefault sets the "is_default" field to the value that was provided on create.
func (u *UsersTeamsUpsert) UpdateIsDefault() *UsersTeamsUpsert {
	u.SetExcluded(usersteams.FieldIsDefault)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create.
// Using this option is equivalent to using:
//
//	client.UsersTeams.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//		).
//		Exec(ctx)
func (u *UsersTeamsUpsertOne) UpdateNewValues() *UsersTeamsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.UsersTeams.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *UsersTeamsUpsertOne) Ignore() *UsersTeamsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *UsersTeamsUpsertOne) DoNothing() *UsersTeamsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the UsersTeamsCreate.OnConflict
// documentation for more info.
func (u *UsersTeamsUpsertOne) Update(set func(*UsersTeamsUpsert)) *UsersTeamsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&UsersTeamsUpsert{UpdateSet: update})
	}))
	return u
}

// SetUserID sets the "user_id" field.
func (u *UsersTeamsUpsertOne) SetUserID(v uuid.UUID) *UsersTeamsUpsertOne {
	return u.Update(func(s *UsersTeamsUpsert) {
		s.SetUserID(v)
	})
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *UsersTeamsUpsertOne) UpdateUserID() *UsersTeamsUpsertOne {
	return u.Update(func(s *UsersTeamsUpsert) {
		s.UpdateUserID()
	})
}

// SetTeamID sets the "team_id" field.
func (u *UsersTeamsUpsertOne) SetTeamID(v uuid.UUID) *UsersTeamsUpsertOne {
	return u.Update(func(s *UsersTeamsUpsert) {
		s.SetTeamID(v)
	})
}

// UpdateTeamID sets the "team_id" field to the value that was provided on create.
func (u *UsersTeamsUpsertOne) UpdateTeamID() *UsersTeamsUpsertOne {
	return u.Update(func(s *UsersTeamsUpsert) {
		s.UpdateTeamID()
	})
}

// SetIsDefault sets the "is_default" field.
func (u *UsersTeamsUpsertOne) SetIsDefault(v bool) *UsersTeamsUpsertOne {
	return u.Update(func(s *UsersTeamsUpsert) {
		s.SetIsDefault(v)
	})
}

// UpdateIsDefault sets the "is_default" field to the value that was provided on create.
func (u *UsersTeamsUpsertOne) UpdateIsDefault() *UsersTeamsUpsertOne {
	return u.Update(func(s *UsersTeamsUpsert) {
		s.UpdateIsDefault()
	})
}

// Exec executes the query.
func (u *UsersTeamsUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("models: missing options for UsersTeamsCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *UsersTeamsUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *UsersTeamsUpsertOne) ID(ctx context.Context) (id int, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *UsersTeamsUpsertOne) IDX(ctx context.Context) int {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// UsersTeamsCreateBulk is the builder for creating many UsersTeams entities in bulk.
type UsersTeamsCreateBulk struct {
	config
	err      error
	builders []*UsersTeamsCreate
	conflict []sql.ConflictOption
}

// Save creates the UsersTeams entities in the database.
func (utcb *UsersTeamsCreateBulk) Save(ctx context.Context) ([]*UsersTeams, error) {
	if utcb.err != nil {
		return nil, utcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(utcb.builders))
	nodes := make([]*UsersTeams, len(utcb.builders))
	mutators := make([]Mutator, len(utcb.builders))
	for i := range utcb.builders {
		func(i int, root context.Context) {
			builder := utcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*UsersTeamsMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, utcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = utcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, utcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, utcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (utcb *UsersTeamsCreateBulk) SaveX(ctx context.Context) []*UsersTeams {
	v, err := utcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (utcb *UsersTeamsCreateBulk) Exec(ctx context.Context) error {
	_, err := utcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (utcb *UsersTeamsCreateBulk) ExecX(ctx context.Context) {
	if err := utcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.UsersTeams.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.UsersTeamsUpsert) {
//			SetUserID(v+v).
//		}).
//		Exec(ctx)
func (utcb *UsersTeamsCreateBulk) OnConflict(opts ...sql.ConflictOption) *UsersTeamsUpsertBulk {
	utcb.conflict = opts
	return &UsersTeamsUpsertBulk{
		create: utcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.UsersTeams.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (utcb *UsersTeamsCreateBulk) OnConflictColumns(columns ...string) *UsersTeamsUpsertBulk {
	utcb.conflict = append(utcb.conflict, sql.ConflictColumns(columns...))
	return &UsersTeamsUpsertBulk{
		create: utcb,
	}
}

// UsersTeamsUpsertBulk is the builder for "upsert"-ing
// a bulk of UsersTeams nodes.
type UsersTeamsUpsertBulk struct {
	create *UsersTeamsCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.UsersTeams.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//		).
//		Exec(ctx)
func (u *UsersTeamsUpsertBulk) UpdateNewValues() *UsersTeamsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.UsersTeams.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *UsersTeamsUpsertBulk) Ignore() *UsersTeamsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *UsersTeamsUpsertBulk) DoNothing() *UsersTeamsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the UsersTeamsCreateBulk.OnConflict
// documentation for more info.
func (u *UsersTeamsUpsertBulk) Update(set func(*UsersTeamsUpsert)) *UsersTeamsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&UsersTeamsUpsert{UpdateSet: update})
	}))
	return u
}

// SetUserID sets the "user_id" field.
func (u *UsersTeamsUpsertBulk) SetUserID(v uuid.UUID) *UsersTeamsUpsertBulk {
	return u.Update(func(s *UsersTeamsUpsert) {
		s.SetUserID(v)
	})
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *UsersTeamsUpsertBulk) UpdateUserID() *UsersTeamsUpsertBulk {
	return u.Update(func(s *UsersTeamsUpsert) {
		s.UpdateUserID()
	})
}

// SetTeamID sets the "team_id" field.
func (u *UsersTeamsUpsertBulk) SetTeamID(v uuid.UUID) *UsersTeamsUpsertBulk {
	return u.Update(func(s *UsersTeamsUpsert) {
		s.SetTeamID(v)
	})
}

// UpdateTeamID sets the "team_id" field to the value that was provided on create.
func (u *UsersTeamsUpsertBulk) UpdateTeamID() *UsersTeamsUpsertBulk {
	return u.Update(func(s *UsersTeamsUpsert) {
		s.UpdateTeamID()
	})
}

// SetIsDefault sets the "is_default" field.
func (u *UsersTeamsUpsertBulk) SetIsDefault(v bool) *UsersTeamsUpsertBulk {
	return u.Update(func(s *UsersTeamsUpsert) {
		s.SetIsDefault(v)
	})
}

// UpdateIsDefault sets the "is_default" field to the value that was provided on create.
func (u *UsersTeamsUpsertBulk) UpdateIsDefault() *UsersTeamsUpsertBulk {
	return u.Update(func(s *UsersTeamsUpsert) {
		s.UpdateIsDefault()
	})
}

// Exec executes the query.
func (u *UsersTeamsUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("models: OnConflict was set for builder %d. Set it on the UsersTeamsCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("models: missing options for UsersTeamsCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *UsersTeamsUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
