// Code generated by ent, DO NOT EDIT.

package models

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/envbuild"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/internal"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/predicate"
)

// EnvBuildDelete is the builder for deleting a EnvBuild entity.
type EnvBuildDelete struct {
	config
	hooks    []Hook
	mutation *EnvBuildMutation
}

// Where appends a list predicates to the EnvBuildDelete builder.
func (ebd *EnvBuildDelete) Where(ps ...predicate.EnvBuild) *EnvBuildDelete {
	ebd.mutation.Where(ps...)
	return ebd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (ebd *EnvBuildDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, ebd.sqlExec, ebd.mutation, ebd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (ebd *EnvBuildDelete) ExecX(ctx context.Context) int {
	n, err := ebd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (ebd *EnvBuildDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(envbuild.Table, sqlgraph.NewFieldSpec(envbuild.FieldID, field.TypeUUID))
	_spec.Node.Schema = ebd.schemaConfig.EnvBuild
	ctx = internal.NewSchemaConfigContext(ctx, ebd.schemaConfig)
	if ps := ebd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, ebd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	ebd.mutation.done = true
	return affected, err
}

// EnvBuildDeleteOne is the builder for deleting a single EnvBuild entity.
type EnvBuildDeleteOne struct {
	ebd *EnvBuildDelete
}

// Where appends a list predicates to the EnvBuildDelete builder.
func (ebdo *EnvBuildDeleteOne) Where(ps ...predicate.EnvBuild) *EnvBuildDeleteOne {
	ebdo.ebd.mutation.Where(ps...)
	return ebdo
}

// Exec executes the deletion query.
func (ebdo *EnvBuildDeleteOne) Exec(ctx context.Context) error {
	n, err := ebdo.ebd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{envbuild.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (ebdo *EnvBuildDeleteOne) ExecX(ctx context.Context) {
	if err := ebdo.Exec(ctx); err != nil {
		panic(err)
	}
}
