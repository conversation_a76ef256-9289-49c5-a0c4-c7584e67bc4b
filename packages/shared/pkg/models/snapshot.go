// Code generated by ent, DO NOT EDIT.

package models

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/env"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/snapshot"
	"github.com/google/uuid"
)

// Snapshot is the model entity for the Snapshot schema.
type Snapshot struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// BaseEnvID holds the value of the "base_env_id" field.
	BaseEnvID string `json:"base_env_id,omitempty"`
	// EnvID holds the value of the "env_id" field.
	EnvID string `json:"env_id,omitempty"`
	// SandboxID holds the value of the "sandbox_id" field.
	SandboxID string `json:"sandbox_id,omitempty"`
	// Metadata holds the value of the "metadata" field.
	Metadata map[string]string `json:"metadata,omitempty"`
	// SandboxStartedAt holds the value of the "sandbox_started_at" field.
	SandboxStartedAt time.Time `json:"sandbox_started_at,omitempty"`
	// EnvSecure holds the value of the "env_secure" field.
	EnvSecure bool `json:"env_secure,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the SnapshotQuery when eager-loading is set.
	Edges        SnapshotEdges `json:"edges"`
	selectValues sql.SelectValues
}

// SnapshotEdges holds the relations/edges for other nodes in the graph.
type SnapshotEdges struct {
	// Env holds the value of the env edge.
	Env *Env `json:"env,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [1]bool
}

// EnvOrErr returns the Env value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e SnapshotEdges) EnvOrErr() (*Env, error) {
	if e.loadedTypes[0] {
		if e.Env == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: env.Label}
		}
		return e.Env, nil
	}
	return nil, &NotLoadedError{edge: "env"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Snapshot) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case snapshot.FieldMetadata:
			values[i] = new([]byte)
		case snapshot.FieldEnvSecure:
			values[i] = new(sql.NullBool)
		case snapshot.FieldBaseEnvID, snapshot.FieldEnvID, snapshot.FieldSandboxID:
			values[i] = new(sql.NullString)
		case snapshot.FieldCreatedAt, snapshot.FieldSandboxStartedAt:
			values[i] = new(sql.NullTime)
		case snapshot.FieldID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Snapshot fields.
func (s *Snapshot) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case snapshot.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				s.ID = *value
			}
		case snapshot.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				s.CreatedAt = value.Time
			}
		case snapshot.FieldBaseEnvID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field base_env_id", values[i])
			} else if value.Valid {
				s.BaseEnvID = value.String
			}
		case snapshot.FieldEnvID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field env_id", values[i])
			} else if value.Valid {
				s.EnvID = value.String
			}
		case snapshot.FieldSandboxID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field sandbox_id", values[i])
			} else if value.Valid {
				s.SandboxID = value.String
			}
		case snapshot.FieldMetadata:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field metadata", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &s.Metadata); err != nil {
					return fmt.Errorf("unmarshal field metadata: %w", err)
				}
			}
		case snapshot.FieldSandboxStartedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field sandbox_started_at", values[i])
			} else if value.Valid {
				s.SandboxStartedAt = value.Time
			}
		case snapshot.FieldEnvSecure:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field env_secure", values[i])
			} else if value.Valid {
				s.EnvSecure = value.Bool
			}
		default:
			s.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Snapshot.
// This includes values selected through modifiers, order, etc.
func (s *Snapshot) Value(name string) (ent.Value, error) {
	return s.selectValues.Get(name)
}

// QueryEnv queries the "env" edge of the Snapshot entity.
func (s *Snapshot) QueryEnv() *EnvQuery {
	return NewSnapshotClient(s.config).QueryEnv(s)
}

// Update returns a builder for updating this Snapshot.
// Note that you need to call Snapshot.Unwrap() before calling this method if this Snapshot
// was returned from a transaction, and the transaction was committed or rolled back.
func (s *Snapshot) Update() *SnapshotUpdateOne {
	return NewSnapshotClient(s.config).UpdateOne(s)
}

// Unwrap unwraps the Snapshot entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (s *Snapshot) Unwrap() *Snapshot {
	_tx, ok := s.config.driver.(*txDriver)
	if !ok {
		panic("models: Snapshot is not a transactional entity")
	}
	s.config.driver = _tx.drv
	return s
}

// String implements the fmt.Stringer.
func (s *Snapshot) String() string {
	var builder strings.Builder
	builder.WriteString("Snapshot(")
	builder.WriteString(fmt.Sprintf("id=%v, ", s.ID))
	builder.WriteString("created_at=")
	builder.WriteString(s.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("base_env_id=")
	builder.WriteString(s.BaseEnvID)
	builder.WriteString(", ")
	builder.WriteString("env_id=")
	builder.WriteString(s.EnvID)
	builder.WriteString(", ")
	builder.WriteString("sandbox_id=")
	builder.WriteString(s.SandboxID)
	builder.WriteString(", ")
	builder.WriteString("metadata=")
	builder.WriteString(fmt.Sprintf("%v", s.Metadata))
	builder.WriteString(", ")
	builder.WriteString("sandbox_started_at=")
	builder.WriteString(s.SandboxStartedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("env_secure=")
	builder.WriteString(fmt.Sprintf("%v", s.EnvSecure))
	builder.WriteByte(')')
	return builder.String()
}

// Snapshots is a parsable slice of Snapshot.
type Snapshots []*Snapshot
