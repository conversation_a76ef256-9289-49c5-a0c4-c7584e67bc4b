// Code generated by ent, DO NOT EDIT.

package models

import (
	"context"
	"fmt"
	"math"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/internal"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/predicate"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/team"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/user"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/usersteams"
	"github.com/google/uuid"
)

// UsersTeamsQuery is the builder for querying UsersTeams entities.
type UsersTeamsQuery struct {
	config
	ctx        *QueryContext
	order      []usersteams.OrderOption
	inters     []Interceptor
	predicates []predicate.UsersTeams
	withUsers  *UserQuery
	withTeams  *TeamQuery
	modifiers  []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the UsersTeamsQuery builder.
func (utq *UsersTeamsQuery) Where(ps ...predicate.UsersTeams) *UsersTeamsQuery {
	utq.predicates = append(utq.predicates, ps...)
	return utq
}

// Limit the number of records to be returned by this query.
func (utq *UsersTeamsQuery) Limit(limit int) *UsersTeamsQuery {
	utq.ctx.Limit = &limit
	return utq
}

// Offset to start from.
func (utq *UsersTeamsQuery) Offset(offset int) *UsersTeamsQuery {
	utq.ctx.Offset = &offset
	return utq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (utq *UsersTeamsQuery) Unique(unique bool) *UsersTeamsQuery {
	utq.ctx.Unique = &unique
	return utq
}

// Order specifies how the records should be ordered.
func (utq *UsersTeamsQuery) Order(o ...usersteams.OrderOption) *UsersTeamsQuery {
	utq.order = append(utq.order, o...)
	return utq
}

// QueryUsers chains the current query on the "users" edge.
func (utq *UsersTeamsQuery) QueryUsers() *UserQuery {
	query := (&UserClient{config: utq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := utq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := utq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(usersteams.Table, usersteams.FieldID, selector),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, usersteams.UsersTable, usersteams.UsersColumn),
		)
		schemaConfig := utq.schemaConfig
		step.To.Schema = schemaConfig.User
		step.Edge.Schema = schemaConfig.UsersTeams
		fromU = sqlgraph.SetNeighbors(utq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryTeams chains the current query on the "teams" edge.
func (utq *UsersTeamsQuery) QueryTeams() *TeamQuery {
	query := (&TeamClient{config: utq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := utq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := utq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(usersteams.Table, usersteams.FieldID, selector),
			sqlgraph.To(team.Table, team.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, usersteams.TeamsTable, usersteams.TeamsColumn),
		)
		schemaConfig := utq.schemaConfig
		step.To.Schema = schemaConfig.Team
		step.Edge.Schema = schemaConfig.UsersTeams
		fromU = sqlgraph.SetNeighbors(utq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first UsersTeams entity from the query.
// Returns a *NotFoundError when no UsersTeams was found.
func (utq *UsersTeamsQuery) First(ctx context.Context) (*UsersTeams, error) {
	nodes, err := utq.Limit(1).All(setContextOp(ctx, utq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{usersteams.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (utq *UsersTeamsQuery) FirstX(ctx context.Context) *UsersTeams {
	node, err := utq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first UsersTeams ID from the query.
// Returns a *NotFoundError when no UsersTeams ID was found.
func (utq *UsersTeamsQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = utq.Limit(1).IDs(setContextOp(ctx, utq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{usersteams.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (utq *UsersTeamsQuery) FirstIDX(ctx context.Context) int {
	id, err := utq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single UsersTeams entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one UsersTeams entity is found.
// Returns a *NotFoundError when no UsersTeams entities are found.
func (utq *UsersTeamsQuery) Only(ctx context.Context) (*UsersTeams, error) {
	nodes, err := utq.Limit(2).All(setContextOp(ctx, utq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{usersteams.Label}
	default:
		return nil, &NotSingularError{usersteams.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (utq *UsersTeamsQuery) OnlyX(ctx context.Context) *UsersTeams {
	node, err := utq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only UsersTeams ID in the query.
// Returns a *NotSingularError when more than one UsersTeams ID is found.
// Returns a *NotFoundError when no entities are found.
func (utq *UsersTeamsQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = utq.Limit(2).IDs(setContextOp(ctx, utq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{usersteams.Label}
	default:
		err = &NotSingularError{usersteams.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (utq *UsersTeamsQuery) OnlyIDX(ctx context.Context) int {
	id, err := utq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of UsersTeamsSlice.
func (utq *UsersTeamsQuery) All(ctx context.Context) ([]*UsersTeams, error) {
	ctx = setContextOp(ctx, utq.ctx, "All")
	if err := utq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*UsersTeams, *UsersTeamsQuery]()
	return withInterceptors[[]*UsersTeams](ctx, utq, qr, utq.inters)
}

// AllX is like All, but panics if an error occurs.
func (utq *UsersTeamsQuery) AllX(ctx context.Context) []*UsersTeams {
	nodes, err := utq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of UsersTeams IDs.
func (utq *UsersTeamsQuery) IDs(ctx context.Context) (ids []int, err error) {
	if utq.ctx.Unique == nil && utq.path != nil {
		utq.Unique(true)
	}
	ctx = setContextOp(ctx, utq.ctx, "IDs")
	if err = utq.Select(usersteams.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (utq *UsersTeamsQuery) IDsX(ctx context.Context) []int {
	ids, err := utq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (utq *UsersTeamsQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, utq.ctx, "Count")
	if err := utq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, utq, querierCount[*UsersTeamsQuery](), utq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (utq *UsersTeamsQuery) CountX(ctx context.Context) int {
	count, err := utq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (utq *UsersTeamsQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, utq.ctx, "Exist")
	switch _, err := utq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("models: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (utq *UsersTeamsQuery) ExistX(ctx context.Context) bool {
	exist, err := utq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the UsersTeamsQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (utq *UsersTeamsQuery) Clone() *UsersTeamsQuery {
	if utq == nil {
		return nil
	}
	return &UsersTeamsQuery{
		config:     utq.config,
		ctx:        utq.ctx.Clone(),
		order:      append([]usersteams.OrderOption{}, utq.order...),
		inters:     append([]Interceptor{}, utq.inters...),
		predicates: append([]predicate.UsersTeams{}, utq.predicates...),
		withUsers:  utq.withUsers.Clone(),
		withTeams:  utq.withTeams.Clone(),
		// clone intermediate query.
		sql:  utq.sql.Clone(),
		path: utq.path,
	}
}

// WithUsers tells the query-builder to eager-load the nodes that are connected to
// the "users" edge. The optional arguments are used to configure the query builder of the edge.
func (utq *UsersTeamsQuery) WithUsers(opts ...func(*UserQuery)) *UsersTeamsQuery {
	query := (&UserClient{config: utq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	utq.withUsers = query
	return utq
}

// WithTeams tells the query-builder to eager-load the nodes that are connected to
// the "teams" edge. The optional arguments are used to configure the query builder of the edge.
func (utq *UsersTeamsQuery) WithTeams(opts ...func(*TeamQuery)) *UsersTeamsQuery {
	query := (&TeamClient{config: utq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	utq.withTeams = query
	return utq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		UserID uuid.UUID `json:"user_id,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.UsersTeams.Query().
//		GroupBy(usersteams.FieldUserID).
//		Aggregate(models.Count()).
//		Scan(ctx, &v)
func (utq *UsersTeamsQuery) GroupBy(field string, fields ...string) *UsersTeamsGroupBy {
	utq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &UsersTeamsGroupBy{build: utq}
	grbuild.flds = &utq.ctx.Fields
	grbuild.label = usersteams.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		UserID uuid.UUID `json:"user_id,omitempty"`
//	}
//
//	client.UsersTeams.Query().
//		Select(usersteams.FieldUserID).
//		Scan(ctx, &v)
func (utq *UsersTeamsQuery) Select(fields ...string) *UsersTeamsSelect {
	utq.ctx.Fields = append(utq.ctx.Fields, fields...)
	sbuild := &UsersTeamsSelect{UsersTeamsQuery: utq}
	sbuild.label = usersteams.Label
	sbuild.flds, sbuild.scan = &utq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a UsersTeamsSelect configured with the given aggregations.
func (utq *UsersTeamsQuery) Aggregate(fns ...AggregateFunc) *UsersTeamsSelect {
	return utq.Select().Aggregate(fns...)
}

func (utq *UsersTeamsQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range utq.inters {
		if inter == nil {
			return fmt.Errorf("models: uninitialized interceptor (forgotten import models/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, utq); err != nil {
				return err
			}
		}
	}
	for _, f := range utq.ctx.Fields {
		if !usersteams.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("models: invalid field %q for query", f)}
		}
	}
	if utq.path != nil {
		prev, err := utq.path(ctx)
		if err != nil {
			return err
		}
		utq.sql = prev
	}
	return nil
}

func (utq *UsersTeamsQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*UsersTeams, error) {
	var (
		nodes       = []*UsersTeams{}
		_spec       = utq.querySpec()
		loadedTypes = [2]bool{
			utq.withUsers != nil,
			utq.withTeams != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*UsersTeams).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &UsersTeams{config: utq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	_spec.Node.Schema = utq.schemaConfig.UsersTeams
	ctx = internal.NewSchemaConfigContext(ctx, utq.schemaConfig)
	if len(utq.modifiers) > 0 {
		_spec.Modifiers = utq.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, utq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := utq.withUsers; query != nil {
		if err := utq.loadUsers(ctx, query, nodes, nil,
			func(n *UsersTeams, e *User) { n.Edges.Users = e }); err != nil {
			return nil, err
		}
	}
	if query := utq.withTeams; query != nil {
		if err := utq.loadTeams(ctx, query, nodes, nil,
			func(n *UsersTeams, e *Team) { n.Edges.Teams = e }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (utq *UsersTeamsQuery) loadUsers(ctx context.Context, query *UserQuery, nodes []*UsersTeams, init func(*UsersTeams), assign func(*UsersTeams, *User)) error {
	ids := make([]uuid.UUID, 0, len(nodes))
	nodeids := make(map[uuid.UUID][]*UsersTeams)
	for i := range nodes {
		fk := nodes[i].UserID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(user.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "user_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (utq *UsersTeamsQuery) loadTeams(ctx context.Context, query *TeamQuery, nodes []*UsersTeams, init func(*UsersTeams), assign func(*UsersTeams, *Team)) error {
	ids := make([]uuid.UUID, 0, len(nodes))
	nodeids := make(map[uuid.UUID][]*UsersTeams)
	for i := range nodes {
		fk := nodes[i].TeamID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(team.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "team_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}

func (utq *UsersTeamsQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := utq.querySpec()
	_spec.Node.Schema = utq.schemaConfig.UsersTeams
	ctx = internal.NewSchemaConfigContext(ctx, utq.schemaConfig)
	if len(utq.modifiers) > 0 {
		_spec.Modifiers = utq.modifiers
	}
	_spec.Node.Columns = utq.ctx.Fields
	if len(utq.ctx.Fields) > 0 {
		_spec.Unique = utq.ctx.Unique != nil && *utq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, utq.driver, _spec)
}

func (utq *UsersTeamsQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(usersteams.Table, usersteams.Columns, sqlgraph.NewFieldSpec(usersteams.FieldID, field.TypeInt))
	_spec.From = utq.sql
	if unique := utq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if utq.path != nil {
		_spec.Unique = true
	}
	if fields := utq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, usersteams.FieldID)
		for i := range fields {
			if fields[i] != usersteams.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if utq.withUsers != nil {
			_spec.Node.AddColumnOnce(usersteams.FieldUserID)
		}
		if utq.withTeams != nil {
			_spec.Node.AddColumnOnce(usersteams.FieldTeamID)
		}
	}
	if ps := utq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := utq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := utq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := utq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (utq *UsersTeamsQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(utq.driver.Dialect())
	t1 := builder.Table(usersteams.Table)
	columns := utq.ctx.Fields
	if len(columns) == 0 {
		columns = usersteams.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if utq.sql != nil {
		selector = utq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if utq.ctx.Unique != nil && *utq.ctx.Unique {
		selector.Distinct()
	}
	t1.Schema(utq.schemaConfig.UsersTeams)
	ctx = internal.NewSchemaConfigContext(ctx, utq.schemaConfig)
	selector.WithContext(ctx)
	for _, m := range utq.modifiers {
		m(selector)
	}
	for _, p := range utq.predicates {
		p(selector)
	}
	for _, p := range utq.order {
		p(selector)
	}
	if offset := utq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := utq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// Modify adds a query modifier for attaching custom logic to queries.
func (utq *UsersTeamsQuery) Modify(modifiers ...func(s *sql.Selector)) *UsersTeamsSelect {
	utq.modifiers = append(utq.modifiers, modifiers...)
	return utq.Select()
}

// UsersTeamsGroupBy is the group-by builder for UsersTeams entities.
type UsersTeamsGroupBy struct {
	selector
	build *UsersTeamsQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (utgb *UsersTeamsGroupBy) Aggregate(fns ...AggregateFunc) *UsersTeamsGroupBy {
	utgb.fns = append(utgb.fns, fns...)
	return utgb
}

// Scan applies the selector query and scans the result into the given value.
func (utgb *UsersTeamsGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, utgb.build.ctx, "GroupBy")
	if err := utgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*UsersTeamsQuery, *UsersTeamsGroupBy](ctx, utgb.build, utgb, utgb.build.inters, v)
}

func (utgb *UsersTeamsGroupBy) sqlScan(ctx context.Context, root *UsersTeamsQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(utgb.fns))
	for _, fn := range utgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*utgb.flds)+len(utgb.fns))
		for _, f := range *utgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*utgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := utgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// UsersTeamsSelect is the builder for selecting fields of UsersTeams entities.
type UsersTeamsSelect struct {
	*UsersTeamsQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (uts *UsersTeamsSelect) Aggregate(fns ...AggregateFunc) *UsersTeamsSelect {
	uts.fns = append(uts.fns, fns...)
	return uts
}

// Scan applies the selector query and scans the result into the given value.
func (uts *UsersTeamsSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, uts.ctx, "Select")
	if err := uts.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*UsersTeamsQuery, *UsersTeamsSelect](ctx, uts.UsersTeamsQuery, uts, uts.inters, v)
}

func (uts *UsersTeamsSelect) sqlScan(ctx context.Context, root *UsersTeamsQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(uts.fns))
	for _, fn := range uts.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*uts.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := uts.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (uts *UsersTeamsSelect) Modify(modifiers ...func(s *sql.Selector)) *UsersTeamsSelect {
	uts.modifiers = append(uts.modifiers, modifiers...)
	return uts
}
