// Code generated by ent, DO NOT EDIT.

package teamapikey

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/internal"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/predicate"
	"github.com/google/uuid"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldLTE(FieldID, id))
}

// APIKey applies equality check predicate on the "api_key" field. It's identical to APIKeyEQ.
func APIKey(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldEQ(FieldAPIKey, v))
}

// APIKeyHash applies equality check predicate on the "api_key_hash" field. It's identical to APIKeyHashEQ.
func APIKeyHash(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldEQ(FieldAPIKeyHash, v))
}

// APIKeyPrefix applies equality check predicate on the "api_key_prefix" field. It's identical to APIKeyPrefixEQ.
func APIKeyPrefix(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldEQ(FieldAPIKeyPrefix, v))
}

// APIKeyLength applies equality check predicate on the "api_key_length" field. It's identical to APIKeyLengthEQ.
func APIKeyLength(v int) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldEQ(FieldAPIKeyLength, v))
}

// APIKeyMaskPrefix applies equality check predicate on the "api_key_mask_prefix" field. It's identical to APIKeyMaskPrefixEQ.
func APIKeyMaskPrefix(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldEQ(FieldAPIKeyMaskPrefix, v))
}

// APIKeyMaskSuffix applies equality check predicate on the "api_key_mask_suffix" field. It's identical to APIKeyMaskSuffixEQ.
func APIKeyMaskSuffix(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldEQ(FieldAPIKeyMaskSuffix, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldEQ(FieldUpdatedAt, v))
}

// TeamID applies equality check predicate on the "team_id" field. It's identical to TeamIDEQ.
func TeamID(v uuid.UUID) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldEQ(FieldTeamID, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldEQ(FieldName, v))
}

// CreatedBy applies equality check predicate on the "created_by" field. It's identical to CreatedByEQ.
func CreatedBy(v uuid.UUID) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldEQ(FieldCreatedBy, v))
}

// LastUsed applies equality check predicate on the "last_used" field. It's identical to LastUsedEQ.
func LastUsed(v time.Time) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldEQ(FieldLastUsed, v))
}

// APIKeyEQ applies the EQ predicate on the "api_key" field.
func APIKeyEQ(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldEQ(FieldAPIKey, v))
}

// APIKeyNEQ applies the NEQ predicate on the "api_key" field.
func APIKeyNEQ(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldNEQ(FieldAPIKey, v))
}

// APIKeyIn applies the In predicate on the "api_key" field.
func APIKeyIn(vs ...string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldIn(FieldAPIKey, vs...))
}

// APIKeyNotIn applies the NotIn predicate on the "api_key" field.
func APIKeyNotIn(vs ...string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldNotIn(FieldAPIKey, vs...))
}

// APIKeyGT applies the GT predicate on the "api_key" field.
func APIKeyGT(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldGT(FieldAPIKey, v))
}

// APIKeyGTE applies the GTE predicate on the "api_key" field.
func APIKeyGTE(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldGTE(FieldAPIKey, v))
}

// APIKeyLT applies the LT predicate on the "api_key" field.
func APIKeyLT(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldLT(FieldAPIKey, v))
}

// APIKeyLTE applies the LTE predicate on the "api_key" field.
func APIKeyLTE(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldLTE(FieldAPIKey, v))
}

// APIKeyContains applies the Contains predicate on the "api_key" field.
func APIKeyContains(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldContains(FieldAPIKey, v))
}

// APIKeyHasPrefix applies the HasPrefix predicate on the "api_key" field.
func APIKeyHasPrefix(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldHasPrefix(FieldAPIKey, v))
}

// APIKeyHasSuffix applies the HasSuffix predicate on the "api_key" field.
func APIKeyHasSuffix(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldHasSuffix(FieldAPIKey, v))
}

// APIKeyEqualFold applies the EqualFold predicate on the "api_key" field.
func APIKeyEqualFold(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldEqualFold(FieldAPIKey, v))
}

// APIKeyContainsFold applies the ContainsFold predicate on the "api_key" field.
func APIKeyContainsFold(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldContainsFold(FieldAPIKey, v))
}

// APIKeyHashEQ applies the EQ predicate on the "api_key_hash" field.
func APIKeyHashEQ(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldEQ(FieldAPIKeyHash, v))
}

// APIKeyHashNEQ applies the NEQ predicate on the "api_key_hash" field.
func APIKeyHashNEQ(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldNEQ(FieldAPIKeyHash, v))
}

// APIKeyHashIn applies the In predicate on the "api_key_hash" field.
func APIKeyHashIn(vs ...string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldIn(FieldAPIKeyHash, vs...))
}

// APIKeyHashNotIn applies the NotIn predicate on the "api_key_hash" field.
func APIKeyHashNotIn(vs ...string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldNotIn(FieldAPIKeyHash, vs...))
}

// APIKeyHashGT applies the GT predicate on the "api_key_hash" field.
func APIKeyHashGT(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldGT(FieldAPIKeyHash, v))
}

// APIKeyHashGTE applies the GTE predicate on the "api_key_hash" field.
func APIKeyHashGTE(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldGTE(FieldAPIKeyHash, v))
}

// APIKeyHashLT applies the LT predicate on the "api_key_hash" field.
func APIKeyHashLT(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldLT(FieldAPIKeyHash, v))
}

// APIKeyHashLTE applies the LTE predicate on the "api_key_hash" field.
func APIKeyHashLTE(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldLTE(FieldAPIKeyHash, v))
}

// APIKeyHashContains applies the Contains predicate on the "api_key_hash" field.
func APIKeyHashContains(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldContains(FieldAPIKeyHash, v))
}

// APIKeyHashHasPrefix applies the HasPrefix predicate on the "api_key_hash" field.
func APIKeyHashHasPrefix(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldHasPrefix(FieldAPIKeyHash, v))
}

// APIKeyHashHasSuffix applies the HasSuffix predicate on the "api_key_hash" field.
func APIKeyHashHasSuffix(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldHasSuffix(FieldAPIKeyHash, v))
}

// APIKeyHashEqualFold applies the EqualFold predicate on the "api_key_hash" field.
func APIKeyHashEqualFold(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldEqualFold(FieldAPIKeyHash, v))
}

// APIKeyHashContainsFold applies the ContainsFold predicate on the "api_key_hash" field.
func APIKeyHashContainsFold(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldContainsFold(FieldAPIKeyHash, v))
}

// APIKeyPrefixEQ applies the EQ predicate on the "api_key_prefix" field.
func APIKeyPrefixEQ(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldEQ(FieldAPIKeyPrefix, v))
}

// APIKeyPrefixNEQ applies the NEQ predicate on the "api_key_prefix" field.
func APIKeyPrefixNEQ(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldNEQ(FieldAPIKeyPrefix, v))
}

// APIKeyPrefixIn applies the In predicate on the "api_key_prefix" field.
func APIKeyPrefixIn(vs ...string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldIn(FieldAPIKeyPrefix, vs...))
}

// APIKeyPrefixNotIn applies the NotIn predicate on the "api_key_prefix" field.
func APIKeyPrefixNotIn(vs ...string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldNotIn(FieldAPIKeyPrefix, vs...))
}

// APIKeyPrefixGT applies the GT predicate on the "api_key_prefix" field.
func APIKeyPrefixGT(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldGT(FieldAPIKeyPrefix, v))
}

// APIKeyPrefixGTE applies the GTE predicate on the "api_key_prefix" field.
func APIKeyPrefixGTE(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldGTE(FieldAPIKeyPrefix, v))
}

// APIKeyPrefixLT applies the LT predicate on the "api_key_prefix" field.
func APIKeyPrefixLT(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldLT(FieldAPIKeyPrefix, v))
}

// APIKeyPrefixLTE applies the LTE predicate on the "api_key_prefix" field.
func APIKeyPrefixLTE(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldLTE(FieldAPIKeyPrefix, v))
}

// APIKeyPrefixContains applies the Contains predicate on the "api_key_prefix" field.
func APIKeyPrefixContains(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldContains(FieldAPIKeyPrefix, v))
}

// APIKeyPrefixHasPrefix applies the HasPrefix predicate on the "api_key_prefix" field.
func APIKeyPrefixHasPrefix(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldHasPrefix(FieldAPIKeyPrefix, v))
}

// APIKeyPrefixHasSuffix applies the HasSuffix predicate on the "api_key_prefix" field.
func APIKeyPrefixHasSuffix(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldHasSuffix(FieldAPIKeyPrefix, v))
}

// APIKeyPrefixEqualFold applies the EqualFold predicate on the "api_key_prefix" field.
func APIKeyPrefixEqualFold(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldEqualFold(FieldAPIKeyPrefix, v))
}

// APIKeyPrefixContainsFold applies the ContainsFold predicate on the "api_key_prefix" field.
func APIKeyPrefixContainsFold(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldContainsFold(FieldAPIKeyPrefix, v))
}

// APIKeyLengthEQ applies the EQ predicate on the "api_key_length" field.
func APIKeyLengthEQ(v int) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldEQ(FieldAPIKeyLength, v))
}

// APIKeyLengthNEQ applies the NEQ predicate on the "api_key_length" field.
func APIKeyLengthNEQ(v int) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldNEQ(FieldAPIKeyLength, v))
}

// APIKeyLengthIn applies the In predicate on the "api_key_length" field.
func APIKeyLengthIn(vs ...int) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldIn(FieldAPIKeyLength, vs...))
}

// APIKeyLengthNotIn applies the NotIn predicate on the "api_key_length" field.
func APIKeyLengthNotIn(vs ...int) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldNotIn(FieldAPIKeyLength, vs...))
}

// APIKeyLengthGT applies the GT predicate on the "api_key_length" field.
func APIKeyLengthGT(v int) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldGT(FieldAPIKeyLength, v))
}

// APIKeyLengthGTE applies the GTE predicate on the "api_key_length" field.
func APIKeyLengthGTE(v int) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldGTE(FieldAPIKeyLength, v))
}

// APIKeyLengthLT applies the LT predicate on the "api_key_length" field.
func APIKeyLengthLT(v int) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldLT(FieldAPIKeyLength, v))
}

// APIKeyLengthLTE applies the LTE predicate on the "api_key_length" field.
func APIKeyLengthLTE(v int) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldLTE(FieldAPIKeyLength, v))
}

// APIKeyMaskPrefixEQ applies the EQ predicate on the "api_key_mask_prefix" field.
func APIKeyMaskPrefixEQ(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldEQ(FieldAPIKeyMaskPrefix, v))
}

// APIKeyMaskPrefixNEQ applies the NEQ predicate on the "api_key_mask_prefix" field.
func APIKeyMaskPrefixNEQ(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldNEQ(FieldAPIKeyMaskPrefix, v))
}

// APIKeyMaskPrefixIn applies the In predicate on the "api_key_mask_prefix" field.
func APIKeyMaskPrefixIn(vs ...string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldIn(FieldAPIKeyMaskPrefix, vs...))
}

// APIKeyMaskPrefixNotIn applies the NotIn predicate on the "api_key_mask_prefix" field.
func APIKeyMaskPrefixNotIn(vs ...string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldNotIn(FieldAPIKeyMaskPrefix, vs...))
}

// APIKeyMaskPrefixGT applies the GT predicate on the "api_key_mask_prefix" field.
func APIKeyMaskPrefixGT(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldGT(FieldAPIKeyMaskPrefix, v))
}

// APIKeyMaskPrefixGTE applies the GTE predicate on the "api_key_mask_prefix" field.
func APIKeyMaskPrefixGTE(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldGTE(FieldAPIKeyMaskPrefix, v))
}

// APIKeyMaskPrefixLT applies the LT predicate on the "api_key_mask_prefix" field.
func APIKeyMaskPrefixLT(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldLT(FieldAPIKeyMaskPrefix, v))
}

// APIKeyMaskPrefixLTE applies the LTE predicate on the "api_key_mask_prefix" field.
func APIKeyMaskPrefixLTE(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldLTE(FieldAPIKeyMaskPrefix, v))
}

// APIKeyMaskPrefixContains applies the Contains predicate on the "api_key_mask_prefix" field.
func APIKeyMaskPrefixContains(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldContains(FieldAPIKeyMaskPrefix, v))
}

// APIKeyMaskPrefixHasPrefix applies the HasPrefix predicate on the "api_key_mask_prefix" field.
func APIKeyMaskPrefixHasPrefix(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldHasPrefix(FieldAPIKeyMaskPrefix, v))
}

// APIKeyMaskPrefixHasSuffix applies the HasSuffix predicate on the "api_key_mask_prefix" field.
func APIKeyMaskPrefixHasSuffix(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldHasSuffix(FieldAPIKeyMaskPrefix, v))
}

// APIKeyMaskPrefixEqualFold applies the EqualFold predicate on the "api_key_mask_prefix" field.
func APIKeyMaskPrefixEqualFold(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldEqualFold(FieldAPIKeyMaskPrefix, v))
}

// APIKeyMaskPrefixContainsFold applies the ContainsFold predicate on the "api_key_mask_prefix" field.
func APIKeyMaskPrefixContainsFold(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldContainsFold(FieldAPIKeyMaskPrefix, v))
}

// APIKeyMaskSuffixEQ applies the EQ predicate on the "api_key_mask_suffix" field.
func APIKeyMaskSuffixEQ(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldEQ(FieldAPIKeyMaskSuffix, v))
}

// APIKeyMaskSuffixNEQ applies the NEQ predicate on the "api_key_mask_suffix" field.
func APIKeyMaskSuffixNEQ(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldNEQ(FieldAPIKeyMaskSuffix, v))
}

// APIKeyMaskSuffixIn applies the In predicate on the "api_key_mask_suffix" field.
func APIKeyMaskSuffixIn(vs ...string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldIn(FieldAPIKeyMaskSuffix, vs...))
}

// APIKeyMaskSuffixNotIn applies the NotIn predicate on the "api_key_mask_suffix" field.
func APIKeyMaskSuffixNotIn(vs ...string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldNotIn(FieldAPIKeyMaskSuffix, vs...))
}

// APIKeyMaskSuffixGT applies the GT predicate on the "api_key_mask_suffix" field.
func APIKeyMaskSuffixGT(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldGT(FieldAPIKeyMaskSuffix, v))
}

// APIKeyMaskSuffixGTE applies the GTE predicate on the "api_key_mask_suffix" field.
func APIKeyMaskSuffixGTE(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldGTE(FieldAPIKeyMaskSuffix, v))
}

// APIKeyMaskSuffixLT applies the LT predicate on the "api_key_mask_suffix" field.
func APIKeyMaskSuffixLT(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldLT(FieldAPIKeyMaskSuffix, v))
}

// APIKeyMaskSuffixLTE applies the LTE predicate on the "api_key_mask_suffix" field.
func APIKeyMaskSuffixLTE(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldLTE(FieldAPIKeyMaskSuffix, v))
}

// APIKeyMaskSuffixContains applies the Contains predicate on the "api_key_mask_suffix" field.
func APIKeyMaskSuffixContains(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldContains(FieldAPIKeyMaskSuffix, v))
}

// APIKeyMaskSuffixHasPrefix applies the HasPrefix predicate on the "api_key_mask_suffix" field.
func APIKeyMaskSuffixHasPrefix(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldHasPrefix(FieldAPIKeyMaskSuffix, v))
}

// APIKeyMaskSuffixHasSuffix applies the HasSuffix predicate on the "api_key_mask_suffix" field.
func APIKeyMaskSuffixHasSuffix(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldHasSuffix(FieldAPIKeyMaskSuffix, v))
}

// APIKeyMaskSuffixEqualFold applies the EqualFold predicate on the "api_key_mask_suffix" field.
func APIKeyMaskSuffixEqualFold(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldEqualFold(FieldAPIKeyMaskSuffix, v))
}

// APIKeyMaskSuffixContainsFold applies the ContainsFold predicate on the "api_key_mask_suffix" field.
func APIKeyMaskSuffixContainsFold(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldContainsFold(FieldAPIKeyMaskSuffix, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldLTE(FieldUpdatedAt, v))
}

// UpdatedAtIsNil applies the IsNil predicate on the "updated_at" field.
func UpdatedAtIsNil() predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldIsNull(FieldUpdatedAt))
}

// UpdatedAtNotNil applies the NotNil predicate on the "updated_at" field.
func UpdatedAtNotNil() predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldNotNull(FieldUpdatedAt))
}

// TeamIDEQ applies the EQ predicate on the "team_id" field.
func TeamIDEQ(v uuid.UUID) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldEQ(FieldTeamID, v))
}

// TeamIDNEQ applies the NEQ predicate on the "team_id" field.
func TeamIDNEQ(v uuid.UUID) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldNEQ(FieldTeamID, v))
}

// TeamIDIn applies the In predicate on the "team_id" field.
func TeamIDIn(vs ...uuid.UUID) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldIn(FieldTeamID, vs...))
}

// TeamIDNotIn applies the NotIn predicate on the "team_id" field.
func TeamIDNotIn(vs ...uuid.UUID) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldNotIn(FieldTeamID, vs...))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldContainsFold(FieldName, v))
}

// CreatedByEQ applies the EQ predicate on the "created_by" field.
func CreatedByEQ(v uuid.UUID) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldEQ(FieldCreatedBy, v))
}

// CreatedByNEQ applies the NEQ predicate on the "created_by" field.
func CreatedByNEQ(v uuid.UUID) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldNEQ(FieldCreatedBy, v))
}

// CreatedByIn applies the In predicate on the "created_by" field.
func CreatedByIn(vs ...uuid.UUID) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldIn(FieldCreatedBy, vs...))
}

// CreatedByNotIn applies the NotIn predicate on the "created_by" field.
func CreatedByNotIn(vs ...uuid.UUID) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldNotIn(FieldCreatedBy, vs...))
}

// CreatedByIsNil applies the IsNil predicate on the "created_by" field.
func CreatedByIsNil() predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldIsNull(FieldCreatedBy))
}

// CreatedByNotNil applies the NotNil predicate on the "created_by" field.
func CreatedByNotNil() predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldNotNull(FieldCreatedBy))
}

// LastUsedEQ applies the EQ predicate on the "last_used" field.
func LastUsedEQ(v time.Time) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldEQ(FieldLastUsed, v))
}

// LastUsedNEQ applies the NEQ predicate on the "last_used" field.
func LastUsedNEQ(v time.Time) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldNEQ(FieldLastUsed, v))
}

// LastUsedIn applies the In predicate on the "last_used" field.
func LastUsedIn(vs ...time.Time) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldIn(FieldLastUsed, vs...))
}

// LastUsedNotIn applies the NotIn predicate on the "last_used" field.
func LastUsedNotIn(vs ...time.Time) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldNotIn(FieldLastUsed, vs...))
}

// LastUsedGT applies the GT predicate on the "last_used" field.
func LastUsedGT(v time.Time) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldGT(FieldLastUsed, v))
}

// LastUsedGTE applies the GTE predicate on the "last_used" field.
func LastUsedGTE(v time.Time) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldGTE(FieldLastUsed, v))
}

// LastUsedLT applies the LT predicate on the "last_used" field.
func LastUsedLT(v time.Time) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldLT(FieldLastUsed, v))
}

// LastUsedLTE applies the LTE predicate on the "last_used" field.
func LastUsedLTE(v time.Time) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldLTE(FieldLastUsed, v))
}

// LastUsedIsNil applies the IsNil predicate on the "last_used" field.
func LastUsedIsNil() predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldIsNull(FieldLastUsed))
}

// LastUsedNotNil applies the NotNil predicate on the "last_used" field.
func LastUsedNotNil() predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.FieldNotNull(FieldLastUsed))
}

// HasTeam applies the HasEdge predicate on the "team" edge.
func HasTeam() predicate.TeamAPIKey {
	return predicate.TeamAPIKey(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, TeamTable, TeamColumn),
		)
		schemaConfig := internal.SchemaConfigFromContext(s.Context())
		step.To.Schema = schemaConfig.Team
		step.Edge.Schema = schemaConfig.TeamAPIKey
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasTeamWith applies the HasEdge predicate on the "team" edge with a given conditions (other predicates).
func HasTeamWith(preds ...predicate.Team) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(func(s *sql.Selector) {
		step := newTeamStep()
		schemaConfig := internal.SchemaConfigFromContext(s.Context())
		step.To.Schema = schemaConfig.Team
		step.Edge.Schema = schemaConfig.TeamAPIKey
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasCreator applies the HasEdge predicate on the "creator" edge.
func HasCreator() predicate.TeamAPIKey {
	return predicate.TeamAPIKey(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, CreatorTable, CreatorColumn),
		)
		schemaConfig := internal.SchemaConfigFromContext(s.Context())
		step.To.Schema = schemaConfig.User
		step.Edge.Schema = schemaConfig.TeamAPIKey
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasCreatorWith applies the HasEdge predicate on the "creator" edge with a given conditions (other predicates).
func HasCreatorWith(preds ...predicate.User) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(func(s *sql.Selector) {
		step := newCreatorStep()
		schemaConfig := internal.SchemaConfigFromContext(s.Context())
		step.To.Schema = schemaConfig.User
		step.Edge.Schema = schemaConfig.TeamAPIKey
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.TeamAPIKey) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.TeamAPIKey) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.TeamAPIKey) predicate.TeamAPIKey {
	return predicate.TeamAPIKey(sql.NotPredicates(p))
}
