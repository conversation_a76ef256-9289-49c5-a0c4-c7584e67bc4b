// Code generated by ent, DO NOT EDIT.

package teamapikey

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the teamapikey type in the database.
	Label = "team_api_key"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldAPIKey holds the string denoting the api_key field in the database.
	FieldAPIKey = "api_key"
	// FieldAPIKeyHash holds the string denoting the api_key_hash field in the database.
	FieldAPIKeyHash = "api_key_hash"
	// FieldAPIKeyPrefix holds the string denoting the api_key_prefix field in the database.
	FieldAPIKeyPrefix = "api_key_prefix"
	// FieldAPIKeyLength holds the string denoting the api_key_length field in the database.
	FieldAPIKeyLength = "api_key_length"
	// FieldAPIKeyMaskPrefix holds the string denoting the api_key_mask_prefix field in the database.
	FieldAPIKeyMaskPrefix = "api_key_mask_prefix"
	// FieldAPIKeyMaskSuffix holds the string denoting the api_key_mask_suffix field in the database.
	FieldAPIKeyMaskSuffix = "api_key_mask_suffix"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldTeamID holds the string denoting the team_id field in the database.
	FieldTeamID = "team_id"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldCreatedBy holds the string denoting the created_by field in the database.
	FieldCreatedBy = "created_by"
	// FieldLastUsed holds the string denoting the last_used field in the database.
	FieldLastUsed = "last_used"
	// EdgeTeam holds the string denoting the team edge name in mutations.
	EdgeTeam = "team"
	// EdgeCreator holds the string denoting the creator edge name in mutations.
	EdgeCreator = "creator"
	// Table holds the table name of the teamapikey in the database.
	Table = "team_api_keys"
	// TeamTable is the table that holds the team relation/edge.
	TeamTable = "team_api_keys"
	// TeamInverseTable is the table name for the Team entity.
	// It exists in this package in order to avoid circular dependency with the "team" package.
	TeamInverseTable = "teams"
	// TeamColumn is the table column denoting the team relation/edge.
	TeamColumn = "team_id"
	// CreatorTable is the table that holds the creator relation/edge.
	CreatorTable = "team_api_keys"
	// CreatorInverseTable is the table name for the User entity.
	// It exists in this package in order to avoid circular dependency with the "user" package.
	CreatorInverseTable = "users"
	// CreatorColumn is the table column denoting the creator relation/edge.
	CreatorColumn = "created_by"
)

// Columns holds all SQL columns for teamapikey fields.
var Columns = []string{
	FieldID,
	FieldAPIKey,
	FieldAPIKeyHash,
	FieldAPIKeyPrefix,
	FieldAPIKeyLength,
	FieldAPIKeyMaskPrefix,
	FieldAPIKeyMaskSuffix,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldTeamID,
	FieldName,
	FieldCreatedBy,
	FieldLastUsed,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultName holds the default value on creation for the "name" field.
	DefaultName string
)

// OrderOption defines the ordering options for the TeamAPIKey queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByAPIKey orders the results by the api_key field.
func ByAPIKey(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAPIKey, opts...).ToFunc()
}

// ByAPIKeyHash orders the results by the api_key_hash field.
func ByAPIKeyHash(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAPIKeyHash, opts...).ToFunc()
}

// ByAPIKeyPrefix orders the results by the api_key_prefix field.
func ByAPIKeyPrefix(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAPIKeyPrefix, opts...).ToFunc()
}

// ByAPIKeyLength orders the results by the api_key_length field.
func ByAPIKeyLength(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAPIKeyLength, opts...).ToFunc()
}

// ByAPIKeyMaskPrefix orders the results by the api_key_mask_prefix field.
func ByAPIKeyMaskPrefix(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAPIKeyMaskPrefix, opts...).ToFunc()
}

// ByAPIKeyMaskSuffix orders the results by the api_key_mask_suffix field.
func ByAPIKeyMaskSuffix(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAPIKeyMaskSuffix, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByTeamID orders the results by the team_id field.
func ByTeamID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTeamID, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByCreatedBy orders the results by the created_by field.
func ByCreatedBy(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedBy, opts...).ToFunc()
}

// ByLastUsed orders the results by the last_used field.
func ByLastUsed(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldLastUsed, opts...).ToFunc()
}

// ByTeamField orders the results by team field.
func ByTeamField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newTeamStep(), sql.OrderByField(field, opts...))
	}
}

// ByCreatorField orders the results by creator field.
func ByCreatorField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newCreatorStep(), sql.OrderByField(field, opts...))
	}
}
func newTeamStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(TeamInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, TeamTable, TeamColumn),
	)
}
func newCreatorStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(CreatorInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, CreatorTable, CreatorColumn),
	)
}
