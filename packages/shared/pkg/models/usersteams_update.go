// Code generated by ent, DO NOT EDIT.

package models

import (
	"context"
	"errors"
	"fmt"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/internal"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/predicate"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/team"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/user"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/usersteams"
	"github.com/google/uuid"
)

// UsersTeamsUpdate is the builder for updating UsersTeams entities.
type UsersTeamsUpdate struct {
	config
	hooks     []Hook
	mutation  *UsersTeamsMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the UsersTeamsUpdate builder.
func (utu *UsersTeamsUpdate) Where(ps ...predicate.UsersTeams) *UsersTeamsUpdate {
	utu.mutation.Where(ps...)
	return utu
}

// SetUserID sets the "user_id" field.
func (utu *UsersTeamsUpdate) SetUserID(u uuid.UUID) *UsersTeamsUpdate {
	utu.mutation.SetUserID(u)
	return utu
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (utu *UsersTeamsUpdate) SetNillableUserID(u *uuid.UUID) *UsersTeamsUpdate {
	if u != nil {
		utu.SetUserID(*u)
	}
	return utu
}

// SetTeamID sets the "team_id" field.
func (utu *UsersTeamsUpdate) SetTeamID(u uuid.UUID) *UsersTeamsUpdate {
	utu.mutation.SetTeamID(u)
	return utu
}

// SetNillableTeamID sets the "team_id" field if the given value is not nil.
func (utu *UsersTeamsUpdate) SetNillableTeamID(u *uuid.UUID) *UsersTeamsUpdate {
	if u != nil {
		utu.SetTeamID(*u)
	}
	return utu
}

// SetIsDefault sets the "is_default" field.
func (utu *UsersTeamsUpdate) SetIsDefault(b bool) *UsersTeamsUpdate {
	utu.mutation.SetIsDefault(b)
	return utu
}

// SetNillableIsDefault sets the "is_default" field if the given value is not nil.
func (utu *UsersTeamsUpdate) SetNillableIsDefault(b *bool) *UsersTeamsUpdate {
	if b != nil {
		utu.SetIsDefault(*b)
	}
	return utu
}

// SetUsersID sets the "users" edge to the User entity by ID.
func (utu *UsersTeamsUpdate) SetUsersID(id uuid.UUID) *UsersTeamsUpdate {
	utu.mutation.SetUsersID(id)
	return utu
}

// SetUsers sets the "users" edge to the User entity.
func (utu *UsersTeamsUpdate) SetUsers(u *User) *UsersTeamsUpdate {
	return utu.SetUsersID(u.ID)
}

// SetTeamsID sets the "teams" edge to the Team entity by ID.
func (utu *UsersTeamsUpdate) SetTeamsID(id uuid.UUID) *UsersTeamsUpdate {
	utu.mutation.SetTeamsID(id)
	return utu
}

// SetTeams sets the "teams" edge to the Team entity.
func (utu *UsersTeamsUpdate) SetTeams(t *Team) *UsersTeamsUpdate {
	return utu.SetTeamsID(t.ID)
}

// Mutation returns the UsersTeamsMutation object of the builder.
func (utu *UsersTeamsUpdate) Mutation() *UsersTeamsMutation {
	return utu.mutation
}

// ClearUsers clears the "users" edge to the User entity.
func (utu *UsersTeamsUpdate) ClearUsers() *UsersTeamsUpdate {
	utu.mutation.ClearUsers()
	return utu
}

// ClearTeams clears the "teams" edge to the Team entity.
func (utu *UsersTeamsUpdate) ClearTeams() *UsersTeamsUpdate {
	utu.mutation.ClearTeams()
	return utu
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (utu *UsersTeamsUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, utu.sqlSave, utu.mutation, utu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (utu *UsersTeamsUpdate) SaveX(ctx context.Context) int {
	affected, err := utu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (utu *UsersTeamsUpdate) Exec(ctx context.Context) error {
	_, err := utu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (utu *UsersTeamsUpdate) ExecX(ctx context.Context) {
	if err := utu.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (utu *UsersTeamsUpdate) check() error {
	if _, ok := utu.mutation.UsersID(); utu.mutation.UsersCleared() && !ok {
		return errors.New(`models: clearing a required unique edge "UsersTeams.users"`)
	}
	if _, ok := utu.mutation.TeamsID(); utu.mutation.TeamsCleared() && !ok {
		return errors.New(`models: clearing a required unique edge "UsersTeams.teams"`)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (utu *UsersTeamsUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *UsersTeamsUpdate {
	utu.modifiers = append(utu.modifiers, modifiers...)
	return utu
}

func (utu *UsersTeamsUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := utu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(usersteams.Table, usersteams.Columns, sqlgraph.NewFieldSpec(usersteams.FieldID, field.TypeInt))
	if ps := utu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := utu.mutation.IsDefault(); ok {
		_spec.SetField(usersteams.FieldIsDefault, field.TypeBool, value)
	}
	if utu.mutation.UsersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   usersteams.UsersTable,
			Columns: []string{usersteams.UsersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = utu.schemaConfig.UsersTeams
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := utu.mutation.UsersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   usersteams.UsersTable,
			Columns: []string{usersteams.UsersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = utu.schemaConfig.UsersTeams
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if utu.mutation.TeamsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   usersteams.TeamsTable,
			Columns: []string{usersteams.TeamsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(team.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = utu.schemaConfig.UsersTeams
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := utu.mutation.TeamsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   usersteams.TeamsTable,
			Columns: []string{usersteams.TeamsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(team.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = utu.schemaConfig.UsersTeams
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.Node.Schema = utu.schemaConfig.UsersTeams
	ctx = internal.NewSchemaConfigContext(ctx, utu.schemaConfig)
	_spec.AddModifiers(utu.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, utu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{usersteams.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	utu.mutation.done = true
	return n, nil
}

// UsersTeamsUpdateOne is the builder for updating a single UsersTeams entity.
type UsersTeamsUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *UsersTeamsMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetUserID sets the "user_id" field.
func (utuo *UsersTeamsUpdateOne) SetUserID(u uuid.UUID) *UsersTeamsUpdateOne {
	utuo.mutation.SetUserID(u)
	return utuo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (utuo *UsersTeamsUpdateOne) SetNillableUserID(u *uuid.UUID) *UsersTeamsUpdateOne {
	if u != nil {
		utuo.SetUserID(*u)
	}
	return utuo
}

// SetTeamID sets the "team_id" field.
func (utuo *UsersTeamsUpdateOne) SetTeamID(u uuid.UUID) *UsersTeamsUpdateOne {
	utuo.mutation.SetTeamID(u)
	return utuo
}

// SetNillableTeamID sets the "team_id" field if the given value is not nil.
func (utuo *UsersTeamsUpdateOne) SetNillableTeamID(u *uuid.UUID) *UsersTeamsUpdateOne {
	if u != nil {
		utuo.SetTeamID(*u)
	}
	return utuo
}

// SetIsDefault sets the "is_default" field.
func (utuo *UsersTeamsUpdateOne) SetIsDefault(b bool) *UsersTeamsUpdateOne {
	utuo.mutation.SetIsDefault(b)
	return utuo
}

// SetNillableIsDefault sets the "is_default" field if the given value is not nil.
func (utuo *UsersTeamsUpdateOne) SetNillableIsDefault(b *bool) *UsersTeamsUpdateOne {
	if b != nil {
		utuo.SetIsDefault(*b)
	}
	return utuo
}

// SetUsersID sets the "users" edge to the User entity by ID.
func (utuo *UsersTeamsUpdateOne) SetUsersID(id uuid.UUID) *UsersTeamsUpdateOne {
	utuo.mutation.SetUsersID(id)
	return utuo
}

// SetUsers sets the "users" edge to the User entity.
func (utuo *UsersTeamsUpdateOne) SetUsers(u *User) *UsersTeamsUpdateOne {
	return utuo.SetUsersID(u.ID)
}

// SetTeamsID sets the "teams" edge to the Team entity by ID.
func (utuo *UsersTeamsUpdateOne) SetTeamsID(id uuid.UUID) *UsersTeamsUpdateOne {
	utuo.mutation.SetTeamsID(id)
	return utuo
}

// SetTeams sets the "teams" edge to the Team entity.
func (utuo *UsersTeamsUpdateOne) SetTeams(t *Team) *UsersTeamsUpdateOne {
	return utuo.SetTeamsID(t.ID)
}

// Mutation returns the UsersTeamsMutation object of the builder.
func (utuo *UsersTeamsUpdateOne) Mutation() *UsersTeamsMutation {
	return utuo.mutation
}

// ClearUsers clears the "users" edge to the User entity.
func (utuo *UsersTeamsUpdateOne) ClearUsers() *UsersTeamsUpdateOne {
	utuo.mutation.ClearUsers()
	return utuo
}

// ClearTeams clears the "teams" edge to the Team entity.
func (utuo *UsersTeamsUpdateOne) ClearTeams() *UsersTeamsUpdateOne {
	utuo.mutation.ClearTeams()
	return utuo
}

// Where appends a list predicates to the UsersTeamsUpdate builder.
func (utuo *UsersTeamsUpdateOne) Where(ps ...predicate.UsersTeams) *UsersTeamsUpdateOne {
	utuo.mutation.Where(ps...)
	return utuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (utuo *UsersTeamsUpdateOne) Select(field string, fields ...string) *UsersTeamsUpdateOne {
	utuo.fields = append([]string{field}, fields...)
	return utuo
}

// Save executes the query and returns the updated UsersTeams entity.
func (utuo *UsersTeamsUpdateOne) Save(ctx context.Context) (*UsersTeams, error) {
	return withHooks(ctx, utuo.sqlSave, utuo.mutation, utuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (utuo *UsersTeamsUpdateOne) SaveX(ctx context.Context) *UsersTeams {
	node, err := utuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (utuo *UsersTeamsUpdateOne) Exec(ctx context.Context) error {
	_, err := utuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (utuo *UsersTeamsUpdateOne) ExecX(ctx context.Context) {
	if err := utuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (utuo *UsersTeamsUpdateOne) check() error {
	if _, ok := utuo.mutation.UsersID(); utuo.mutation.UsersCleared() && !ok {
		return errors.New(`models: clearing a required unique edge "UsersTeams.users"`)
	}
	if _, ok := utuo.mutation.TeamsID(); utuo.mutation.TeamsCleared() && !ok {
		return errors.New(`models: clearing a required unique edge "UsersTeams.teams"`)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (utuo *UsersTeamsUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *UsersTeamsUpdateOne {
	utuo.modifiers = append(utuo.modifiers, modifiers...)
	return utuo
}

func (utuo *UsersTeamsUpdateOne) sqlSave(ctx context.Context) (_node *UsersTeams, err error) {
	if err := utuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(usersteams.Table, usersteams.Columns, sqlgraph.NewFieldSpec(usersteams.FieldID, field.TypeInt))
	id, ok := utuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`models: missing "UsersTeams.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := utuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, usersteams.FieldID)
		for _, f := range fields {
			if !usersteams.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("models: invalid field %q for query", f)}
			}
			if f != usersteams.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := utuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := utuo.mutation.IsDefault(); ok {
		_spec.SetField(usersteams.FieldIsDefault, field.TypeBool, value)
	}
	if utuo.mutation.UsersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   usersteams.UsersTable,
			Columns: []string{usersteams.UsersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = utuo.schemaConfig.UsersTeams
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := utuo.mutation.UsersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   usersteams.UsersTable,
			Columns: []string{usersteams.UsersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = utuo.schemaConfig.UsersTeams
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if utuo.mutation.TeamsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   usersteams.TeamsTable,
			Columns: []string{usersteams.TeamsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(team.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = utuo.schemaConfig.UsersTeams
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := utuo.mutation.TeamsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   usersteams.TeamsTable,
			Columns: []string{usersteams.TeamsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(team.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = utuo.schemaConfig.UsersTeams
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.Node.Schema = utuo.schemaConfig.UsersTeams
	ctx = internal.NewSchemaConfigContext(ctx, utuo.schemaConfig)
	_spec.AddModifiers(utuo.modifiers...)
	_node = &UsersTeams{config: utuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, utuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{usersteams.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	utuo.mutation.done = true
	return _node, nil
}
