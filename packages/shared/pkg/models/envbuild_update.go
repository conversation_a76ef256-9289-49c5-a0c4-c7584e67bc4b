// Code generated by ent, DO NOT EDIT.

package models

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/env"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/envbuild"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/internal"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/predicate"
)

// EnvBuildUpdate is the builder for updating EnvBuild entities.
type EnvBuildUpdate struct {
	config
	hooks     []Hook
	mutation  *EnvBuildMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the EnvBuildUpdate builder.
func (ebu *EnvBuildUpdate) Where(ps ...predicate.EnvBuild) *EnvBuildUpdate {
	ebu.mutation.Where(ps...)
	return ebu
}

// SetUpdatedAt sets the "updated_at" field.
func (ebu *EnvBuildUpdate) SetUpdatedAt(t time.Time) *EnvBuildUpdate {
	ebu.mutation.SetUpdatedAt(t)
	return ebu
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (ebu *EnvBuildUpdate) SetNillableUpdatedAt(t *time.Time) *EnvBuildUpdate {
	if t != nil {
		ebu.SetUpdatedAt(*t)
	}
	return ebu
}

// SetFinishedAt sets the "finished_at" field.
func (ebu *EnvBuildUpdate) SetFinishedAt(t time.Time) *EnvBuildUpdate {
	ebu.mutation.SetFinishedAt(t)
	return ebu
}

// SetNillableFinishedAt sets the "finished_at" field if the given value is not nil.
func (ebu *EnvBuildUpdate) SetNillableFinishedAt(t *time.Time) *EnvBuildUpdate {
	if t != nil {
		ebu.SetFinishedAt(*t)
	}
	return ebu
}

// ClearFinishedAt clears the value of the "finished_at" field.
func (ebu *EnvBuildUpdate) ClearFinishedAt() *EnvBuildUpdate {
	ebu.mutation.ClearFinishedAt()
	return ebu
}

// SetEnvID sets the "env_id" field.
func (ebu *EnvBuildUpdate) SetEnvID(s string) *EnvBuildUpdate {
	ebu.mutation.SetEnvID(s)
	return ebu
}

// SetNillableEnvID sets the "env_id" field if the given value is not nil.
func (ebu *EnvBuildUpdate) SetNillableEnvID(s *string) *EnvBuildUpdate {
	if s != nil {
		ebu.SetEnvID(*s)
	}
	return ebu
}

// ClearEnvID clears the value of the "env_id" field.
func (ebu *EnvBuildUpdate) ClearEnvID() *EnvBuildUpdate {
	ebu.mutation.ClearEnvID()
	return ebu
}

// SetStatus sets the "status" field.
func (ebu *EnvBuildUpdate) SetStatus(e envbuild.Status) *EnvBuildUpdate {
	ebu.mutation.SetStatus(e)
	return ebu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (ebu *EnvBuildUpdate) SetNillableStatus(e *envbuild.Status) *EnvBuildUpdate {
	if e != nil {
		ebu.SetStatus(*e)
	}
	return ebu
}

// SetDockerfile sets the "dockerfile" field.
func (ebu *EnvBuildUpdate) SetDockerfile(s string) *EnvBuildUpdate {
	ebu.mutation.SetDockerfile(s)
	return ebu
}

// SetNillableDockerfile sets the "dockerfile" field if the given value is not nil.
func (ebu *EnvBuildUpdate) SetNillableDockerfile(s *string) *EnvBuildUpdate {
	if s != nil {
		ebu.SetDockerfile(*s)
	}
	return ebu
}

// ClearDockerfile clears the value of the "dockerfile" field.
func (ebu *EnvBuildUpdate) ClearDockerfile() *EnvBuildUpdate {
	ebu.mutation.ClearDockerfile()
	return ebu
}

// SetStartCmd sets the "start_cmd" field.
func (ebu *EnvBuildUpdate) SetStartCmd(s string) *EnvBuildUpdate {
	ebu.mutation.SetStartCmd(s)
	return ebu
}

// SetNillableStartCmd sets the "start_cmd" field if the given value is not nil.
func (ebu *EnvBuildUpdate) SetNillableStartCmd(s *string) *EnvBuildUpdate {
	if s != nil {
		ebu.SetStartCmd(*s)
	}
	return ebu
}

// ClearStartCmd clears the value of the "start_cmd" field.
func (ebu *EnvBuildUpdate) ClearStartCmd() *EnvBuildUpdate {
	ebu.mutation.ClearStartCmd()
	return ebu
}

// SetReadyCmd sets the "ready_cmd" field.
func (ebu *EnvBuildUpdate) SetReadyCmd(s string) *EnvBuildUpdate {
	ebu.mutation.SetReadyCmd(s)
	return ebu
}

// SetNillableReadyCmd sets the "ready_cmd" field if the given value is not nil.
func (ebu *EnvBuildUpdate) SetNillableReadyCmd(s *string) *EnvBuildUpdate {
	if s != nil {
		ebu.SetReadyCmd(*s)
	}
	return ebu
}

// ClearReadyCmd clears the value of the "ready_cmd" field.
func (ebu *EnvBuildUpdate) ClearReadyCmd() *EnvBuildUpdate {
	ebu.mutation.ClearReadyCmd()
	return ebu
}

// SetVcpu sets the "vcpu" field.
func (ebu *EnvBuildUpdate) SetVcpu(i int64) *EnvBuildUpdate {
	ebu.mutation.ResetVcpu()
	ebu.mutation.SetVcpu(i)
	return ebu
}

// SetNillableVcpu sets the "vcpu" field if the given value is not nil.
func (ebu *EnvBuildUpdate) SetNillableVcpu(i *int64) *EnvBuildUpdate {
	if i != nil {
		ebu.SetVcpu(*i)
	}
	return ebu
}

// AddVcpu adds i to the "vcpu" field.
func (ebu *EnvBuildUpdate) AddVcpu(i int64) *EnvBuildUpdate {
	ebu.mutation.AddVcpu(i)
	return ebu
}

// SetRAMMB sets the "ram_mb" field.
func (ebu *EnvBuildUpdate) SetRAMMB(i int64) *EnvBuildUpdate {
	ebu.mutation.ResetRAMMB()
	ebu.mutation.SetRAMMB(i)
	return ebu
}

// SetNillableRAMMB sets the "ram_mb" field if the given value is not nil.
func (ebu *EnvBuildUpdate) SetNillableRAMMB(i *int64) *EnvBuildUpdate {
	if i != nil {
		ebu.SetRAMMB(*i)
	}
	return ebu
}

// AddRAMMB adds i to the "ram_mb" field.
func (ebu *EnvBuildUpdate) AddRAMMB(i int64) *EnvBuildUpdate {
	ebu.mutation.AddRAMMB(i)
	return ebu
}

// SetFreeDiskSizeMB sets the "free_disk_size_mb" field.
func (ebu *EnvBuildUpdate) SetFreeDiskSizeMB(i int64) *EnvBuildUpdate {
	ebu.mutation.ResetFreeDiskSizeMB()
	ebu.mutation.SetFreeDiskSizeMB(i)
	return ebu
}

// SetNillableFreeDiskSizeMB sets the "free_disk_size_mb" field if the given value is not nil.
func (ebu *EnvBuildUpdate) SetNillableFreeDiskSizeMB(i *int64) *EnvBuildUpdate {
	if i != nil {
		ebu.SetFreeDiskSizeMB(*i)
	}
	return ebu
}

// AddFreeDiskSizeMB adds i to the "free_disk_size_mb" field.
func (ebu *EnvBuildUpdate) AddFreeDiskSizeMB(i int64) *EnvBuildUpdate {
	ebu.mutation.AddFreeDiskSizeMB(i)
	return ebu
}

// SetTotalDiskSizeMB sets the "total_disk_size_mb" field.
func (ebu *EnvBuildUpdate) SetTotalDiskSizeMB(i int64) *EnvBuildUpdate {
	ebu.mutation.ResetTotalDiskSizeMB()
	ebu.mutation.SetTotalDiskSizeMB(i)
	return ebu
}

// SetNillableTotalDiskSizeMB sets the "total_disk_size_mb" field if the given value is not nil.
func (ebu *EnvBuildUpdate) SetNillableTotalDiskSizeMB(i *int64) *EnvBuildUpdate {
	if i != nil {
		ebu.SetTotalDiskSizeMB(*i)
	}
	return ebu
}

// AddTotalDiskSizeMB adds i to the "total_disk_size_mb" field.
func (ebu *EnvBuildUpdate) AddTotalDiskSizeMB(i int64) *EnvBuildUpdate {
	ebu.mutation.AddTotalDiskSizeMB(i)
	return ebu
}

// ClearTotalDiskSizeMB clears the value of the "total_disk_size_mb" field.
func (ebu *EnvBuildUpdate) ClearTotalDiskSizeMB() *EnvBuildUpdate {
	ebu.mutation.ClearTotalDiskSizeMB()
	return ebu
}

// SetKernelVersion sets the "kernel_version" field.
func (ebu *EnvBuildUpdate) SetKernelVersion(s string) *EnvBuildUpdate {
	ebu.mutation.SetKernelVersion(s)
	return ebu
}

// SetNillableKernelVersion sets the "kernel_version" field if the given value is not nil.
func (ebu *EnvBuildUpdate) SetNillableKernelVersion(s *string) *EnvBuildUpdate {
	if s != nil {
		ebu.SetKernelVersion(*s)
	}
	return ebu
}

// SetFirecrackerVersion sets the "firecracker_version" field.
func (ebu *EnvBuildUpdate) SetFirecrackerVersion(s string) *EnvBuildUpdate {
	ebu.mutation.SetFirecrackerVersion(s)
	return ebu
}

// SetNillableFirecrackerVersion sets the "firecracker_version" field if the given value is not nil.
func (ebu *EnvBuildUpdate) SetNillableFirecrackerVersion(s *string) *EnvBuildUpdate {
	if s != nil {
		ebu.SetFirecrackerVersion(*s)
	}
	return ebu
}

// SetEnvdVersion sets the "envd_version" field.
func (ebu *EnvBuildUpdate) SetEnvdVersion(s string) *EnvBuildUpdate {
	ebu.mutation.SetEnvdVersion(s)
	return ebu
}

// SetNillableEnvdVersion sets the "envd_version" field if the given value is not nil.
func (ebu *EnvBuildUpdate) SetNillableEnvdVersion(s *string) *EnvBuildUpdate {
	if s != nil {
		ebu.SetEnvdVersion(*s)
	}
	return ebu
}

// ClearEnvdVersion clears the value of the "envd_version" field.
func (ebu *EnvBuildUpdate) ClearEnvdVersion() *EnvBuildUpdate {
	ebu.mutation.ClearEnvdVersion()
	return ebu
}

// SetEnv sets the "env" edge to the Env entity.
func (ebu *EnvBuildUpdate) SetEnv(e *Env) *EnvBuildUpdate {
	return ebu.SetEnvID(e.ID)
}

// Mutation returns the EnvBuildMutation object of the builder.
func (ebu *EnvBuildUpdate) Mutation() *EnvBuildMutation {
	return ebu.mutation
}

// ClearEnv clears the "env" edge to the Env entity.
func (ebu *EnvBuildUpdate) ClearEnv() *EnvBuildUpdate {
	ebu.mutation.ClearEnv()
	return ebu
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (ebu *EnvBuildUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, ebu.sqlSave, ebu.mutation, ebu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ebu *EnvBuildUpdate) SaveX(ctx context.Context) int {
	affected, err := ebu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (ebu *EnvBuildUpdate) Exec(ctx context.Context) error {
	_, err := ebu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ebu *EnvBuildUpdate) ExecX(ctx context.Context) {
	if err := ebu.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ebu *EnvBuildUpdate) check() error {
	if v, ok := ebu.mutation.Status(); ok {
		if err := envbuild.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`models: validator failed for field "EnvBuild.status": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (ebu *EnvBuildUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *EnvBuildUpdate {
	ebu.modifiers = append(ebu.modifiers, modifiers...)
	return ebu
}

func (ebu *EnvBuildUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := ebu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(envbuild.Table, envbuild.Columns, sqlgraph.NewFieldSpec(envbuild.FieldID, field.TypeUUID))
	if ps := ebu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := ebu.mutation.UpdatedAt(); ok {
		_spec.SetField(envbuild.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := ebu.mutation.FinishedAt(); ok {
		_spec.SetField(envbuild.FieldFinishedAt, field.TypeTime, value)
	}
	if ebu.mutation.FinishedAtCleared() {
		_spec.ClearField(envbuild.FieldFinishedAt, field.TypeTime)
	}
	if value, ok := ebu.mutation.Status(); ok {
		_spec.SetField(envbuild.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := ebu.mutation.Dockerfile(); ok {
		_spec.SetField(envbuild.FieldDockerfile, field.TypeString, value)
	}
	if ebu.mutation.DockerfileCleared() {
		_spec.ClearField(envbuild.FieldDockerfile, field.TypeString)
	}
	if value, ok := ebu.mutation.StartCmd(); ok {
		_spec.SetField(envbuild.FieldStartCmd, field.TypeString, value)
	}
	if ebu.mutation.StartCmdCleared() {
		_spec.ClearField(envbuild.FieldStartCmd, field.TypeString)
	}
	if value, ok := ebu.mutation.ReadyCmd(); ok {
		_spec.SetField(envbuild.FieldReadyCmd, field.TypeString, value)
	}
	if ebu.mutation.ReadyCmdCleared() {
		_spec.ClearField(envbuild.FieldReadyCmd, field.TypeString)
	}
	if value, ok := ebu.mutation.Vcpu(); ok {
		_spec.SetField(envbuild.FieldVcpu, field.TypeInt64, value)
	}
	if value, ok := ebu.mutation.AddedVcpu(); ok {
		_spec.AddField(envbuild.FieldVcpu, field.TypeInt64, value)
	}
	if value, ok := ebu.mutation.RAMMB(); ok {
		_spec.SetField(envbuild.FieldRAMMB, field.TypeInt64, value)
	}
	if value, ok := ebu.mutation.AddedRAMMB(); ok {
		_spec.AddField(envbuild.FieldRAMMB, field.TypeInt64, value)
	}
	if value, ok := ebu.mutation.FreeDiskSizeMB(); ok {
		_spec.SetField(envbuild.FieldFreeDiskSizeMB, field.TypeInt64, value)
	}
	if value, ok := ebu.mutation.AddedFreeDiskSizeMB(); ok {
		_spec.AddField(envbuild.FieldFreeDiskSizeMB, field.TypeInt64, value)
	}
	if value, ok := ebu.mutation.TotalDiskSizeMB(); ok {
		_spec.SetField(envbuild.FieldTotalDiskSizeMB, field.TypeInt64, value)
	}
	if value, ok := ebu.mutation.AddedTotalDiskSizeMB(); ok {
		_spec.AddField(envbuild.FieldTotalDiskSizeMB, field.TypeInt64, value)
	}
	if ebu.mutation.TotalDiskSizeMBCleared() {
		_spec.ClearField(envbuild.FieldTotalDiskSizeMB, field.TypeInt64)
	}
	if value, ok := ebu.mutation.KernelVersion(); ok {
		_spec.SetField(envbuild.FieldKernelVersion, field.TypeString, value)
	}
	if value, ok := ebu.mutation.FirecrackerVersion(); ok {
		_spec.SetField(envbuild.FieldFirecrackerVersion, field.TypeString, value)
	}
	if value, ok := ebu.mutation.EnvdVersion(); ok {
		_spec.SetField(envbuild.FieldEnvdVersion, field.TypeString, value)
	}
	if ebu.mutation.EnvdVersionCleared() {
		_spec.ClearField(envbuild.FieldEnvdVersion, field.TypeString)
	}
	if ebu.mutation.EnvCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   envbuild.EnvTable,
			Columns: []string{envbuild.EnvColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(env.FieldID, field.TypeString),
			},
		}
		edge.Schema = ebu.schemaConfig.EnvBuild
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ebu.mutation.EnvIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   envbuild.EnvTable,
			Columns: []string{envbuild.EnvColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(env.FieldID, field.TypeString),
			},
		}
		edge.Schema = ebu.schemaConfig.EnvBuild
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.Node.Schema = ebu.schemaConfig.EnvBuild
	ctx = internal.NewSchemaConfigContext(ctx, ebu.schemaConfig)
	_spec.AddModifiers(ebu.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, ebu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{envbuild.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	ebu.mutation.done = true
	return n, nil
}

// EnvBuildUpdateOne is the builder for updating a single EnvBuild entity.
type EnvBuildUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *EnvBuildMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetUpdatedAt sets the "updated_at" field.
func (ebuo *EnvBuildUpdateOne) SetUpdatedAt(t time.Time) *EnvBuildUpdateOne {
	ebuo.mutation.SetUpdatedAt(t)
	return ebuo
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (ebuo *EnvBuildUpdateOne) SetNillableUpdatedAt(t *time.Time) *EnvBuildUpdateOne {
	if t != nil {
		ebuo.SetUpdatedAt(*t)
	}
	return ebuo
}

// SetFinishedAt sets the "finished_at" field.
func (ebuo *EnvBuildUpdateOne) SetFinishedAt(t time.Time) *EnvBuildUpdateOne {
	ebuo.mutation.SetFinishedAt(t)
	return ebuo
}

// SetNillableFinishedAt sets the "finished_at" field if the given value is not nil.
func (ebuo *EnvBuildUpdateOne) SetNillableFinishedAt(t *time.Time) *EnvBuildUpdateOne {
	if t != nil {
		ebuo.SetFinishedAt(*t)
	}
	return ebuo
}

// ClearFinishedAt clears the value of the "finished_at" field.
func (ebuo *EnvBuildUpdateOne) ClearFinishedAt() *EnvBuildUpdateOne {
	ebuo.mutation.ClearFinishedAt()
	return ebuo
}

// SetEnvID sets the "env_id" field.
func (ebuo *EnvBuildUpdateOne) SetEnvID(s string) *EnvBuildUpdateOne {
	ebuo.mutation.SetEnvID(s)
	return ebuo
}

// SetNillableEnvID sets the "env_id" field if the given value is not nil.
func (ebuo *EnvBuildUpdateOne) SetNillableEnvID(s *string) *EnvBuildUpdateOne {
	if s != nil {
		ebuo.SetEnvID(*s)
	}
	return ebuo
}

// ClearEnvID clears the value of the "env_id" field.
func (ebuo *EnvBuildUpdateOne) ClearEnvID() *EnvBuildUpdateOne {
	ebuo.mutation.ClearEnvID()
	return ebuo
}

// SetStatus sets the "status" field.
func (ebuo *EnvBuildUpdateOne) SetStatus(e envbuild.Status) *EnvBuildUpdateOne {
	ebuo.mutation.SetStatus(e)
	return ebuo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (ebuo *EnvBuildUpdateOne) SetNillableStatus(e *envbuild.Status) *EnvBuildUpdateOne {
	if e != nil {
		ebuo.SetStatus(*e)
	}
	return ebuo
}

// SetDockerfile sets the "dockerfile" field.
func (ebuo *EnvBuildUpdateOne) SetDockerfile(s string) *EnvBuildUpdateOne {
	ebuo.mutation.SetDockerfile(s)
	return ebuo
}

// SetNillableDockerfile sets the "dockerfile" field if the given value is not nil.
func (ebuo *EnvBuildUpdateOne) SetNillableDockerfile(s *string) *EnvBuildUpdateOne {
	if s != nil {
		ebuo.SetDockerfile(*s)
	}
	return ebuo
}

// ClearDockerfile clears the value of the "dockerfile" field.
func (ebuo *EnvBuildUpdateOne) ClearDockerfile() *EnvBuildUpdateOne {
	ebuo.mutation.ClearDockerfile()
	return ebuo
}

// SetStartCmd sets the "start_cmd" field.
func (ebuo *EnvBuildUpdateOne) SetStartCmd(s string) *EnvBuildUpdateOne {
	ebuo.mutation.SetStartCmd(s)
	return ebuo
}

// SetNillableStartCmd sets the "start_cmd" field if the given value is not nil.
func (ebuo *EnvBuildUpdateOne) SetNillableStartCmd(s *string) *EnvBuildUpdateOne {
	if s != nil {
		ebuo.SetStartCmd(*s)
	}
	return ebuo
}

// ClearStartCmd clears the value of the "start_cmd" field.
func (ebuo *EnvBuildUpdateOne) ClearStartCmd() *EnvBuildUpdateOne {
	ebuo.mutation.ClearStartCmd()
	return ebuo
}

// SetReadyCmd sets the "ready_cmd" field.
func (ebuo *EnvBuildUpdateOne) SetReadyCmd(s string) *EnvBuildUpdateOne {
	ebuo.mutation.SetReadyCmd(s)
	return ebuo
}

// SetNillableReadyCmd sets the "ready_cmd" field if the given value is not nil.
func (ebuo *EnvBuildUpdateOne) SetNillableReadyCmd(s *string) *EnvBuildUpdateOne {
	if s != nil {
		ebuo.SetReadyCmd(*s)
	}
	return ebuo
}

// ClearReadyCmd clears the value of the "ready_cmd" field.
func (ebuo *EnvBuildUpdateOne) ClearReadyCmd() *EnvBuildUpdateOne {
	ebuo.mutation.ClearReadyCmd()
	return ebuo
}

// SetVcpu sets the "vcpu" field.
func (ebuo *EnvBuildUpdateOne) SetVcpu(i int64) *EnvBuildUpdateOne {
	ebuo.mutation.ResetVcpu()
	ebuo.mutation.SetVcpu(i)
	return ebuo
}

// SetNillableVcpu sets the "vcpu" field if the given value is not nil.
func (ebuo *EnvBuildUpdateOne) SetNillableVcpu(i *int64) *EnvBuildUpdateOne {
	if i != nil {
		ebuo.SetVcpu(*i)
	}
	return ebuo
}

// AddVcpu adds i to the "vcpu" field.
func (ebuo *EnvBuildUpdateOne) AddVcpu(i int64) *EnvBuildUpdateOne {
	ebuo.mutation.AddVcpu(i)
	return ebuo
}

// SetRAMMB sets the "ram_mb" field.
func (ebuo *EnvBuildUpdateOne) SetRAMMB(i int64) *EnvBuildUpdateOne {
	ebuo.mutation.ResetRAMMB()
	ebuo.mutation.SetRAMMB(i)
	return ebuo
}

// SetNillableRAMMB sets the "ram_mb" field if the given value is not nil.
func (ebuo *EnvBuildUpdateOne) SetNillableRAMMB(i *int64) *EnvBuildUpdateOne {
	if i != nil {
		ebuo.SetRAMMB(*i)
	}
	return ebuo
}

// AddRAMMB adds i to the "ram_mb" field.
func (ebuo *EnvBuildUpdateOne) AddRAMMB(i int64) *EnvBuildUpdateOne {
	ebuo.mutation.AddRAMMB(i)
	return ebuo
}

// SetFreeDiskSizeMB sets the "free_disk_size_mb" field.
func (ebuo *EnvBuildUpdateOne) SetFreeDiskSizeMB(i int64) *EnvBuildUpdateOne {
	ebuo.mutation.ResetFreeDiskSizeMB()
	ebuo.mutation.SetFreeDiskSizeMB(i)
	return ebuo
}

// SetNillableFreeDiskSizeMB sets the "free_disk_size_mb" field if the given value is not nil.
func (ebuo *EnvBuildUpdateOne) SetNillableFreeDiskSizeMB(i *int64) *EnvBuildUpdateOne {
	if i != nil {
		ebuo.SetFreeDiskSizeMB(*i)
	}
	return ebuo
}

// AddFreeDiskSizeMB adds i to the "free_disk_size_mb" field.
func (ebuo *EnvBuildUpdateOne) AddFreeDiskSizeMB(i int64) *EnvBuildUpdateOne {
	ebuo.mutation.AddFreeDiskSizeMB(i)
	return ebuo
}

// SetTotalDiskSizeMB sets the "total_disk_size_mb" field.
func (ebuo *EnvBuildUpdateOne) SetTotalDiskSizeMB(i int64) *EnvBuildUpdateOne {
	ebuo.mutation.ResetTotalDiskSizeMB()
	ebuo.mutation.SetTotalDiskSizeMB(i)
	return ebuo
}

// SetNillableTotalDiskSizeMB sets the "total_disk_size_mb" field if the given value is not nil.
func (ebuo *EnvBuildUpdateOne) SetNillableTotalDiskSizeMB(i *int64) *EnvBuildUpdateOne {
	if i != nil {
		ebuo.SetTotalDiskSizeMB(*i)
	}
	return ebuo
}

// AddTotalDiskSizeMB adds i to the "total_disk_size_mb" field.
func (ebuo *EnvBuildUpdateOne) AddTotalDiskSizeMB(i int64) *EnvBuildUpdateOne {
	ebuo.mutation.AddTotalDiskSizeMB(i)
	return ebuo
}

// ClearTotalDiskSizeMB clears the value of the "total_disk_size_mb" field.
func (ebuo *EnvBuildUpdateOne) ClearTotalDiskSizeMB() *EnvBuildUpdateOne {
	ebuo.mutation.ClearTotalDiskSizeMB()
	return ebuo
}

// SetKernelVersion sets the "kernel_version" field.
func (ebuo *EnvBuildUpdateOne) SetKernelVersion(s string) *EnvBuildUpdateOne {
	ebuo.mutation.SetKernelVersion(s)
	return ebuo
}

// SetNillableKernelVersion sets the "kernel_version" field if the given value is not nil.
func (ebuo *EnvBuildUpdateOne) SetNillableKernelVersion(s *string) *EnvBuildUpdateOne {
	if s != nil {
		ebuo.SetKernelVersion(*s)
	}
	return ebuo
}

// SetFirecrackerVersion sets the "firecracker_version" field.
func (ebuo *EnvBuildUpdateOne) SetFirecrackerVersion(s string) *EnvBuildUpdateOne {
	ebuo.mutation.SetFirecrackerVersion(s)
	return ebuo
}

// SetNillableFirecrackerVersion sets the "firecracker_version" field if the given value is not nil.
func (ebuo *EnvBuildUpdateOne) SetNillableFirecrackerVersion(s *string) *EnvBuildUpdateOne {
	if s != nil {
		ebuo.SetFirecrackerVersion(*s)
	}
	return ebuo
}

// SetEnvdVersion sets the "envd_version" field.
func (ebuo *EnvBuildUpdateOne) SetEnvdVersion(s string) *EnvBuildUpdateOne {
	ebuo.mutation.SetEnvdVersion(s)
	return ebuo
}

// SetNillableEnvdVersion sets the "envd_version" field if the given value is not nil.
func (ebuo *EnvBuildUpdateOne) SetNillableEnvdVersion(s *string) *EnvBuildUpdateOne {
	if s != nil {
		ebuo.SetEnvdVersion(*s)
	}
	return ebuo
}

// ClearEnvdVersion clears the value of the "envd_version" field.
func (ebuo *EnvBuildUpdateOne) ClearEnvdVersion() *EnvBuildUpdateOne {
	ebuo.mutation.ClearEnvdVersion()
	return ebuo
}

// SetEnv sets the "env" edge to the Env entity.
func (ebuo *EnvBuildUpdateOne) SetEnv(e *Env) *EnvBuildUpdateOne {
	return ebuo.SetEnvID(e.ID)
}

// Mutation returns the EnvBuildMutation object of the builder.
func (ebuo *EnvBuildUpdateOne) Mutation() *EnvBuildMutation {
	return ebuo.mutation
}

// ClearEnv clears the "env" edge to the Env entity.
func (ebuo *EnvBuildUpdateOne) ClearEnv() *EnvBuildUpdateOne {
	ebuo.mutation.ClearEnv()
	return ebuo
}

// Where appends a list predicates to the EnvBuildUpdate builder.
func (ebuo *EnvBuildUpdateOne) Where(ps ...predicate.EnvBuild) *EnvBuildUpdateOne {
	ebuo.mutation.Where(ps...)
	return ebuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (ebuo *EnvBuildUpdateOne) Select(field string, fields ...string) *EnvBuildUpdateOne {
	ebuo.fields = append([]string{field}, fields...)
	return ebuo
}

// Save executes the query and returns the updated EnvBuild entity.
func (ebuo *EnvBuildUpdateOne) Save(ctx context.Context) (*EnvBuild, error) {
	return withHooks(ctx, ebuo.sqlSave, ebuo.mutation, ebuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ebuo *EnvBuildUpdateOne) SaveX(ctx context.Context) *EnvBuild {
	node, err := ebuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (ebuo *EnvBuildUpdateOne) Exec(ctx context.Context) error {
	_, err := ebuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ebuo *EnvBuildUpdateOne) ExecX(ctx context.Context) {
	if err := ebuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ebuo *EnvBuildUpdateOne) check() error {
	if v, ok := ebuo.mutation.Status(); ok {
		if err := envbuild.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`models: validator failed for field "EnvBuild.status": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (ebuo *EnvBuildUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *EnvBuildUpdateOne {
	ebuo.modifiers = append(ebuo.modifiers, modifiers...)
	return ebuo
}

func (ebuo *EnvBuildUpdateOne) sqlSave(ctx context.Context) (_node *EnvBuild, err error) {
	if err := ebuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(envbuild.Table, envbuild.Columns, sqlgraph.NewFieldSpec(envbuild.FieldID, field.TypeUUID))
	id, ok := ebuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`models: missing "EnvBuild.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := ebuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, envbuild.FieldID)
		for _, f := range fields {
			if !envbuild.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("models: invalid field %q for query", f)}
			}
			if f != envbuild.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := ebuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := ebuo.mutation.UpdatedAt(); ok {
		_spec.SetField(envbuild.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := ebuo.mutation.FinishedAt(); ok {
		_spec.SetField(envbuild.FieldFinishedAt, field.TypeTime, value)
	}
	if ebuo.mutation.FinishedAtCleared() {
		_spec.ClearField(envbuild.FieldFinishedAt, field.TypeTime)
	}
	if value, ok := ebuo.mutation.Status(); ok {
		_spec.SetField(envbuild.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := ebuo.mutation.Dockerfile(); ok {
		_spec.SetField(envbuild.FieldDockerfile, field.TypeString, value)
	}
	if ebuo.mutation.DockerfileCleared() {
		_spec.ClearField(envbuild.FieldDockerfile, field.TypeString)
	}
	if value, ok := ebuo.mutation.StartCmd(); ok {
		_spec.SetField(envbuild.FieldStartCmd, field.TypeString, value)
	}
	if ebuo.mutation.StartCmdCleared() {
		_spec.ClearField(envbuild.FieldStartCmd, field.TypeString)
	}
	if value, ok := ebuo.mutation.ReadyCmd(); ok {
		_spec.SetField(envbuild.FieldReadyCmd, field.TypeString, value)
	}
	if ebuo.mutation.ReadyCmdCleared() {
		_spec.ClearField(envbuild.FieldReadyCmd, field.TypeString)
	}
	if value, ok := ebuo.mutation.Vcpu(); ok {
		_spec.SetField(envbuild.FieldVcpu, field.TypeInt64, value)
	}
	if value, ok := ebuo.mutation.AddedVcpu(); ok {
		_spec.AddField(envbuild.FieldVcpu, field.TypeInt64, value)
	}
	if value, ok := ebuo.mutation.RAMMB(); ok {
		_spec.SetField(envbuild.FieldRAMMB, field.TypeInt64, value)
	}
	if value, ok := ebuo.mutation.AddedRAMMB(); ok {
		_spec.AddField(envbuild.FieldRAMMB, field.TypeInt64, value)
	}
	if value, ok := ebuo.mutation.FreeDiskSizeMB(); ok {
		_spec.SetField(envbuild.FieldFreeDiskSizeMB, field.TypeInt64, value)
	}
	if value, ok := ebuo.mutation.AddedFreeDiskSizeMB(); ok {
		_spec.AddField(envbuild.FieldFreeDiskSizeMB, field.TypeInt64, value)
	}
	if value, ok := ebuo.mutation.TotalDiskSizeMB(); ok {
		_spec.SetField(envbuild.FieldTotalDiskSizeMB, field.TypeInt64, value)
	}
	if value, ok := ebuo.mutation.AddedTotalDiskSizeMB(); ok {
		_spec.AddField(envbuild.FieldTotalDiskSizeMB, field.TypeInt64, value)
	}
	if ebuo.mutation.TotalDiskSizeMBCleared() {
		_spec.ClearField(envbuild.FieldTotalDiskSizeMB, field.TypeInt64)
	}
	if value, ok := ebuo.mutation.KernelVersion(); ok {
		_spec.SetField(envbuild.FieldKernelVersion, field.TypeString, value)
	}
	if value, ok := ebuo.mutation.FirecrackerVersion(); ok {
		_spec.SetField(envbuild.FieldFirecrackerVersion, field.TypeString, value)
	}
	if value, ok := ebuo.mutation.EnvdVersion(); ok {
		_spec.SetField(envbuild.FieldEnvdVersion, field.TypeString, value)
	}
	if ebuo.mutation.EnvdVersionCleared() {
		_spec.ClearField(envbuild.FieldEnvdVersion, field.TypeString)
	}
	if ebuo.mutation.EnvCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   envbuild.EnvTable,
			Columns: []string{envbuild.EnvColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(env.FieldID, field.TypeString),
			},
		}
		edge.Schema = ebuo.schemaConfig.EnvBuild
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ebuo.mutation.EnvIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   envbuild.EnvTable,
			Columns: []string{envbuild.EnvColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(env.FieldID, field.TypeString),
			},
		}
		edge.Schema = ebuo.schemaConfig.EnvBuild
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.Node.Schema = ebuo.schemaConfig.EnvBuild
	ctx = internal.NewSchemaConfigContext(ctx, ebuo.schemaConfig)
	_spec.AddModifiers(ebuo.modifiers...)
	_node = &EnvBuild{config: ebuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, ebuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{envbuild.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	ebuo.mutation.done = true
	return _node, nil
}
