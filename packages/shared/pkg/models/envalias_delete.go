// Code generated by ent, DO NOT EDIT.

package models

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/envalias"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/internal"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/predicate"
)

// EnvAliasDelete is the builder for deleting a EnvAlias entity.
type EnvAliasDelete struct {
	config
	hooks    []Hook
	mutation *EnvAliasMutation
}

// Where appends a list predicates to the EnvAliasDelete builder.
func (ead *EnvAliasDelete) Where(ps ...predicate.EnvAlias) *EnvAliasDelete {
	ead.mutation.Where(ps...)
	return ead
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (ead *EnvAliasDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, ead.sqlExec, ead.mutation, ead.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (ead *EnvAliasDelete) ExecX(ctx context.Context) int {
	n, err := ead.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (ead *EnvAliasDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(envalias.Table, sqlgraph.NewFieldSpec(envalias.FieldID, field.TypeString))
	_spec.Node.Schema = ead.schemaConfig.EnvAlias
	ctx = internal.NewSchemaConfigContext(ctx, ead.schemaConfig)
	if ps := ead.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, ead.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	ead.mutation.done = true
	return affected, err
}

// EnvAliasDeleteOne is the builder for deleting a single EnvAlias entity.
type EnvAliasDeleteOne struct {
	ead *EnvAliasDelete
}

// Where appends a list predicates to the EnvAliasDelete builder.
func (eado *EnvAliasDeleteOne) Where(ps ...predicate.EnvAlias) *EnvAliasDeleteOne {
	eado.ead.mutation.Where(ps...)
	return eado
}

// Exec executes the deletion query.
func (eado *EnvAliasDeleteOne) Exec(ctx context.Context) error {
	n, err := eado.ead.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{envalias.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (eado *EnvAliasDeleteOne) ExecX(ctx context.Context) {
	if err := eado.Exec(ctx); err != nil {
		panic(err)
	}
}
