// Code generated by ent, DO NOT EDIT.

package models

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/env"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/envbuild"
	"github.com/google/uuid"
)

// EnvBuildCreate is the builder for creating a EnvBuild entity.
type EnvBuildCreate struct {
	config
	mutation *EnvBuildMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedAt sets the "created_at" field.
func (ebc *EnvBuildCreate) SetCreatedAt(t time.Time) *EnvBuildCreate {
	ebc.mutation.SetCreatedAt(t)
	return ebc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (ebc *EnvBuildCreate) SetNillableCreatedAt(t *time.Time) *EnvBuildCreate {
	if t != nil {
		ebc.SetCreatedAt(*t)
	}
	return ebc
}

// SetUpdatedAt sets the "updated_at" field.
func (ebc *EnvBuildCreate) SetUpdatedAt(t time.Time) *EnvBuildCreate {
	ebc.mutation.SetUpdatedAt(t)
	return ebc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (ebc *EnvBuildCreate) SetNillableUpdatedAt(t *time.Time) *EnvBuildCreate {
	if t != nil {
		ebc.SetUpdatedAt(*t)
	}
	return ebc
}

// SetFinishedAt sets the "finished_at" field.
func (ebc *EnvBuildCreate) SetFinishedAt(t time.Time) *EnvBuildCreate {
	ebc.mutation.SetFinishedAt(t)
	return ebc
}

// SetNillableFinishedAt sets the "finished_at" field if the given value is not nil.
func (ebc *EnvBuildCreate) SetNillableFinishedAt(t *time.Time) *EnvBuildCreate {
	if t != nil {
		ebc.SetFinishedAt(*t)
	}
	return ebc
}

// SetEnvID sets the "env_id" field.
func (ebc *EnvBuildCreate) SetEnvID(s string) *EnvBuildCreate {
	ebc.mutation.SetEnvID(s)
	return ebc
}

// SetNillableEnvID sets the "env_id" field if the given value is not nil.
func (ebc *EnvBuildCreate) SetNillableEnvID(s *string) *EnvBuildCreate {
	if s != nil {
		ebc.SetEnvID(*s)
	}
	return ebc
}

// SetStatus sets the "status" field.
func (ebc *EnvBuildCreate) SetStatus(e envbuild.Status) *EnvBuildCreate {
	ebc.mutation.SetStatus(e)
	return ebc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (ebc *EnvBuildCreate) SetNillableStatus(e *envbuild.Status) *EnvBuildCreate {
	if e != nil {
		ebc.SetStatus(*e)
	}
	return ebc
}

// SetDockerfile sets the "dockerfile" field.
func (ebc *EnvBuildCreate) SetDockerfile(s string) *EnvBuildCreate {
	ebc.mutation.SetDockerfile(s)
	return ebc
}

// SetNillableDockerfile sets the "dockerfile" field if the given value is not nil.
func (ebc *EnvBuildCreate) SetNillableDockerfile(s *string) *EnvBuildCreate {
	if s != nil {
		ebc.SetDockerfile(*s)
	}
	return ebc
}

// SetStartCmd sets the "start_cmd" field.
func (ebc *EnvBuildCreate) SetStartCmd(s string) *EnvBuildCreate {
	ebc.mutation.SetStartCmd(s)
	return ebc
}

// SetNillableStartCmd sets the "start_cmd" field if the given value is not nil.
func (ebc *EnvBuildCreate) SetNillableStartCmd(s *string) *EnvBuildCreate {
	if s != nil {
		ebc.SetStartCmd(*s)
	}
	return ebc
}

// SetReadyCmd sets the "ready_cmd" field.
func (ebc *EnvBuildCreate) SetReadyCmd(s string) *EnvBuildCreate {
	ebc.mutation.SetReadyCmd(s)
	return ebc
}

// SetNillableReadyCmd sets the "ready_cmd" field if the given value is not nil.
func (ebc *EnvBuildCreate) SetNillableReadyCmd(s *string) *EnvBuildCreate {
	if s != nil {
		ebc.SetReadyCmd(*s)
	}
	return ebc
}

// SetVcpu sets the "vcpu" field.
func (ebc *EnvBuildCreate) SetVcpu(i int64) *EnvBuildCreate {
	ebc.mutation.SetVcpu(i)
	return ebc
}

// SetRAMMB sets the "ram_mb" field.
func (ebc *EnvBuildCreate) SetRAMMB(i int64) *EnvBuildCreate {
	ebc.mutation.SetRAMMB(i)
	return ebc
}

// SetFreeDiskSizeMB sets the "free_disk_size_mb" field.
func (ebc *EnvBuildCreate) SetFreeDiskSizeMB(i int64) *EnvBuildCreate {
	ebc.mutation.SetFreeDiskSizeMB(i)
	return ebc
}

// SetTotalDiskSizeMB sets the "total_disk_size_mb" field.
func (ebc *EnvBuildCreate) SetTotalDiskSizeMB(i int64) *EnvBuildCreate {
	ebc.mutation.SetTotalDiskSizeMB(i)
	return ebc
}

// SetNillableTotalDiskSizeMB sets the "total_disk_size_mb" field if the given value is not nil.
func (ebc *EnvBuildCreate) SetNillableTotalDiskSizeMB(i *int64) *EnvBuildCreate {
	if i != nil {
		ebc.SetTotalDiskSizeMB(*i)
	}
	return ebc
}

// SetKernelVersion sets the "kernel_version" field.
func (ebc *EnvBuildCreate) SetKernelVersion(s string) *EnvBuildCreate {
	ebc.mutation.SetKernelVersion(s)
	return ebc
}

// SetNillableKernelVersion sets the "kernel_version" field if the given value is not nil.
func (ebc *EnvBuildCreate) SetNillableKernelVersion(s *string) *EnvBuildCreate {
	if s != nil {
		ebc.SetKernelVersion(*s)
	}
	return ebc
}

// SetFirecrackerVersion sets the "firecracker_version" field.
func (ebc *EnvBuildCreate) SetFirecrackerVersion(s string) *EnvBuildCreate {
	ebc.mutation.SetFirecrackerVersion(s)
	return ebc
}

// SetNillableFirecrackerVersion sets the "firecracker_version" field if the given value is not nil.
func (ebc *EnvBuildCreate) SetNillableFirecrackerVersion(s *string) *EnvBuildCreate {
	if s != nil {
		ebc.SetFirecrackerVersion(*s)
	}
	return ebc
}

// SetEnvdVersion sets the "envd_version" field.
func (ebc *EnvBuildCreate) SetEnvdVersion(s string) *EnvBuildCreate {
	ebc.mutation.SetEnvdVersion(s)
	return ebc
}

// SetNillableEnvdVersion sets the "envd_version" field if the given value is not nil.
func (ebc *EnvBuildCreate) SetNillableEnvdVersion(s *string) *EnvBuildCreate {
	if s != nil {
		ebc.SetEnvdVersion(*s)
	}
	return ebc
}

// SetID sets the "id" field.
func (ebc *EnvBuildCreate) SetID(u uuid.UUID) *EnvBuildCreate {
	ebc.mutation.SetID(u)
	return ebc
}

// SetEnv sets the "env" edge to the Env entity.
func (ebc *EnvBuildCreate) SetEnv(e *Env) *EnvBuildCreate {
	return ebc.SetEnvID(e.ID)
}

// Mutation returns the EnvBuildMutation object of the builder.
func (ebc *EnvBuildCreate) Mutation() *EnvBuildMutation {
	return ebc.mutation
}

// Save creates the EnvBuild in the database.
func (ebc *EnvBuildCreate) Save(ctx context.Context) (*EnvBuild, error) {
	ebc.defaults()
	return withHooks(ctx, ebc.sqlSave, ebc.mutation, ebc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (ebc *EnvBuildCreate) SaveX(ctx context.Context) *EnvBuild {
	v, err := ebc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ebc *EnvBuildCreate) Exec(ctx context.Context) error {
	_, err := ebc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ebc *EnvBuildCreate) ExecX(ctx context.Context) {
	if err := ebc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ebc *EnvBuildCreate) defaults() {
	if _, ok := ebc.mutation.CreatedAt(); !ok {
		v := envbuild.DefaultCreatedAt()
		ebc.mutation.SetCreatedAt(v)
	}
	if _, ok := ebc.mutation.UpdatedAt(); !ok {
		v := envbuild.DefaultUpdatedAt()
		ebc.mutation.SetUpdatedAt(v)
	}
	if _, ok := ebc.mutation.Status(); !ok {
		v := envbuild.DefaultStatus
		ebc.mutation.SetStatus(v)
	}
	if _, ok := ebc.mutation.KernelVersion(); !ok {
		v := envbuild.DefaultKernelVersion
		ebc.mutation.SetKernelVersion(v)
	}
	if _, ok := ebc.mutation.FirecrackerVersion(); !ok {
		v := envbuild.DefaultFirecrackerVersion
		ebc.mutation.SetFirecrackerVersion(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ebc *EnvBuildCreate) check() error {
	if _, ok := ebc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`models: missing required field "EnvBuild.created_at"`)}
	}
	if _, ok := ebc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`models: missing required field "EnvBuild.updated_at"`)}
	}
	if _, ok := ebc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`models: missing required field "EnvBuild.status"`)}
	}
	if v, ok := ebc.mutation.Status(); ok {
		if err := envbuild.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`models: validator failed for field "EnvBuild.status": %w`, err)}
		}
	}
	if _, ok := ebc.mutation.Vcpu(); !ok {
		return &ValidationError{Name: "vcpu", err: errors.New(`models: missing required field "EnvBuild.vcpu"`)}
	}
	if _, ok := ebc.mutation.RAMMB(); !ok {
		return &ValidationError{Name: "ram_mb", err: errors.New(`models: missing required field "EnvBuild.ram_mb"`)}
	}
	if _, ok := ebc.mutation.FreeDiskSizeMB(); !ok {
		return &ValidationError{Name: "free_disk_size_mb", err: errors.New(`models: missing required field "EnvBuild.free_disk_size_mb"`)}
	}
	if _, ok := ebc.mutation.KernelVersion(); !ok {
		return &ValidationError{Name: "kernel_version", err: errors.New(`models: missing required field "EnvBuild.kernel_version"`)}
	}
	if _, ok := ebc.mutation.FirecrackerVersion(); !ok {
		return &ValidationError{Name: "firecracker_version", err: errors.New(`models: missing required field "EnvBuild.firecracker_version"`)}
	}
	return nil
}

func (ebc *EnvBuildCreate) sqlSave(ctx context.Context) (*EnvBuild, error) {
	if err := ebc.check(); err != nil {
		return nil, err
	}
	_node, _spec := ebc.createSpec()
	if err := sqlgraph.CreateNode(ctx, ebc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	ebc.mutation.id = &_node.ID
	ebc.mutation.done = true
	return _node, nil
}

func (ebc *EnvBuildCreate) createSpec() (*EnvBuild, *sqlgraph.CreateSpec) {
	var (
		_node = &EnvBuild{config: ebc.config}
		_spec = sqlgraph.NewCreateSpec(envbuild.Table, sqlgraph.NewFieldSpec(envbuild.FieldID, field.TypeUUID))
	)
	_spec.Schema = ebc.schemaConfig.EnvBuild
	_spec.OnConflict = ebc.conflict
	if id, ok := ebc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := ebc.mutation.CreatedAt(); ok {
		_spec.SetField(envbuild.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := ebc.mutation.UpdatedAt(); ok {
		_spec.SetField(envbuild.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := ebc.mutation.FinishedAt(); ok {
		_spec.SetField(envbuild.FieldFinishedAt, field.TypeTime, value)
		_node.FinishedAt = &value
	}
	if value, ok := ebc.mutation.Status(); ok {
		_spec.SetField(envbuild.FieldStatus, field.TypeEnum, value)
		_node.Status = value
	}
	if value, ok := ebc.mutation.Dockerfile(); ok {
		_spec.SetField(envbuild.FieldDockerfile, field.TypeString, value)
		_node.Dockerfile = &value
	}
	if value, ok := ebc.mutation.StartCmd(); ok {
		_spec.SetField(envbuild.FieldStartCmd, field.TypeString, value)
		_node.StartCmd = &value
	}
	if value, ok := ebc.mutation.ReadyCmd(); ok {
		_spec.SetField(envbuild.FieldReadyCmd, field.TypeString, value)
		_node.ReadyCmd = &value
	}
	if value, ok := ebc.mutation.Vcpu(); ok {
		_spec.SetField(envbuild.FieldVcpu, field.TypeInt64, value)
		_node.Vcpu = value
	}
	if value, ok := ebc.mutation.RAMMB(); ok {
		_spec.SetField(envbuild.FieldRAMMB, field.TypeInt64, value)
		_node.RAMMB = value
	}
	if value, ok := ebc.mutation.FreeDiskSizeMB(); ok {
		_spec.SetField(envbuild.FieldFreeDiskSizeMB, field.TypeInt64, value)
		_node.FreeDiskSizeMB = value
	}
	if value, ok := ebc.mutation.TotalDiskSizeMB(); ok {
		_spec.SetField(envbuild.FieldTotalDiskSizeMB, field.TypeInt64, value)
		_node.TotalDiskSizeMB = &value
	}
	if value, ok := ebc.mutation.KernelVersion(); ok {
		_spec.SetField(envbuild.FieldKernelVersion, field.TypeString, value)
		_node.KernelVersion = value
	}
	if value, ok := ebc.mutation.FirecrackerVersion(); ok {
		_spec.SetField(envbuild.FieldFirecrackerVersion, field.TypeString, value)
		_node.FirecrackerVersion = value
	}
	if value, ok := ebc.mutation.EnvdVersion(); ok {
		_spec.SetField(envbuild.FieldEnvdVersion, field.TypeString, value)
		_node.EnvdVersion = &value
	}
	if nodes := ebc.mutation.EnvIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   envbuild.EnvTable,
			Columns: []string{envbuild.EnvColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(env.FieldID, field.TypeString),
			},
		}
		edge.Schema = ebc.schemaConfig.EnvBuild
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.EnvID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.EnvBuild.Create().
//		SetCreatedAt(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.EnvBuildUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (ebc *EnvBuildCreate) OnConflict(opts ...sql.ConflictOption) *EnvBuildUpsertOne {
	ebc.conflict = opts
	return &EnvBuildUpsertOne{
		create: ebc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.EnvBuild.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (ebc *EnvBuildCreate) OnConflictColumns(columns ...string) *EnvBuildUpsertOne {
	ebc.conflict = append(ebc.conflict, sql.ConflictColumns(columns...))
	return &EnvBuildUpsertOne{
		create: ebc,
	}
}

type (
	// EnvBuildUpsertOne is the builder for "upsert"-ing
	//  one EnvBuild node.
	EnvBuildUpsertOne struct {
		create *EnvBuildCreate
	}

	// EnvBuildUpsert is the "OnConflict" setter.
	EnvBuildUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedAt sets the "updated_at" field.
func (u *EnvBuildUpsert) SetUpdatedAt(v time.Time) *EnvBuildUpsert {
	u.Set(envbuild.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *EnvBuildUpsert) UpdateUpdatedAt() *EnvBuildUpsert {
	u.SetExcluded(envbuild.FieldUpdatedAt)
	return u
}

// SetFinishedAt sets the "finished_at" field.
func (u *EnvBuildUpsert) SetFinishedAt(v time.Time) *EnvBuildUpsert {
	u.Set(envbuild.FieldFinishedAt, v)
	return u
}

// UpdateFinishedAt sets the "finished_at" field to the value that was provided on create.
func (u *EnvBuildUpsert) UpdateFinishedAt() *EnvBuildUpsert {
	u.SetExcluded(envbuild.FieldFinishedAt)
	return u
}

// ClearFinishedAt clears the value of the "finished_at" field.
func (u *EnvBuildUpsert) ClearFinishedAt() *EnvBuildUpsert {
	u.SetNull(envbuild.FieldFinishedAt)
	return u
}

// SetEnvID sets the "env_id" field.
func (u *EnvBuildUpsert) SetEnvID(v string) *EnvBuildUpsert {
	u.Set(envbuild.FieldEnvID, v)
	return u
}

// UpdateEnvID sets the "env_id" field to the value that was provided on create.
func (u *EnvBuildUpsert) UpdateEnvID() *EnvBuildUpsert {
	u.SetExcluded(envbuild.FieldEnvID)
	return u
}

// ClearEnvID clears the value of the "env_id" field.
func (u *EnvBuildUpsert) ClearEnvID() *EnvBuildUpsert {
	u.SetNull(envbuild.FieldEnvID)
	return u
}

// SetStatus sets the "status" field.
func (u *EnvBuildUpsert) SetStatus(v envbuild.Status) *EnvBuildUpsert {
	u.Set(envbuild.FieldStatus, v)
	return u
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *EnvBuildUpsert) UpdateStatus() *EnvBuildUpsert {
	u.SetExcluded(envbuild.FieldStatus)
	return u
}

// SetDockerfile sets the "dockerfile" field.
func (u *EnvBuildUpsert) SetDockerfile(v string) *EnvBuildUpsert {
	u.Set(envbuild.FieldDockerfile, v)
	return u
}

// UpdateDockerfile sets the "dockerfile" field to the value that was provided on create.
func (u *EnvBuildUpsert) UpdateDockerfile() *EnvBuildUpsert {
	u.SetExcluded(envbuild.FieldDockerfile)
	return u
}

// ClearDockerfile clears the value of the "dockerfile" field.
func (u *EnvBuildUpsert) ClearDockerfile() *EnvBuildUpsert {
	u.SetNull(envbuild.FieldDockerfile)
	return u
}

// SetStartCmd sets the "start_cmd" field.
func (u *EnvBuildUpsert) SetStartCmd(v string) *EnvBuildUpsert {
	u.Set(envbuild.FieldStartCmd, v)
	return u
}

// UpdateStartCmd sets the "start_cmd" field to the value that was provided on create.
func (u *EnvBuildUpsert) UpdateStartCmd() *EnvBuildUpsert {
	u.SetExcluded(envbuild.FieldStartCmd)
	return u
}

// ClearStartCmd clears the value of the "start_cmd" field.
func (u *EnvBuildUpsert) ClearStartCmd() *EnvBuildUpsert {
	u.SetNull(envbuild.FieldStartCmd)
	return u
}

// SetReadyCmd sets the "ready_cmd" field.
func (u *EnvBuildUpsert) SetReadyCmd(v string) *EnvBuildUpsert {
	u.Set(envbuild.FieldReadyCmd, v)
	return u
}

// UpdateReadyCmd sets the "ready_cmd" field to the value that was provided on create.
func (u *EnvBuildUpsert) UpdateReadyCmd() *EnvBuildUpsert {
	u.SetExcluded(envbuild.FieldReadyCmd)
	return u
}

// ClearReadyCmd clears the value of the "ready_cmd" field.
func (u *EnvBuildUpsert) ClearReadyCmd() *EnvBuildUpsert {
	u.SetNull(envbuild.FieldReadyCmd)
	return u
}

// SetVcpu sets the "vcpu" field.
func (u *EnvBuildUpsert) SetVcpu(v int64) *EnvBuildUpsert {
	u.Set(envbuild.FieldVcpu, v)
	return u
}

// UpdateVcpu sets the "vcpu" field to the value that was provided on create.
func (u *EnvBuildUpsert) UpdateVcpu() *EnvBuildUpsert {
	u.SetExcluded(envbuild.FieldVcpu)
	return u
}

// AddVcpu adds v to the "vcpu" field.
func (u *EnvBuildUpsert) AddVcpu(v int64) *EnvBuildUpsert {
	u.Add(envbuild.FieldVcpu, v)
	return u
}

// SetRAMMB sets the "ram_mb" field.
func (u *EnvBuildUpsert) SetRAMMB(v int64) *EnvBuildUpsert {
	u.Set(envbuild.FieldRAMMB, v)
	return u
}

// UpdateRAMMB sets the "ram_mb" field to the value that was provided on create.
func (u *EnvBuildUpsert) UpdateRAMMB() *EnvBuildUpsert {
	u.SetExcluded(envbuild.FieldRAMMB)
	return u
}

// AddRAMMB adds v to the "ram_mb" field.
func (u *EnvBuildUpsert) AddRAMMB(v int64) *EnvBuildUpsert {
	u.Add(envbuild.FieldRAMMB, v)
	return u
}

// SetFreeDiskSizeMB sets the "free_disk_size_mb" field.
func (u *EnvBuildUpsert) SetFreeDiskSizeMB(v int64) *EnvBuildUpsert {
	u.Set(envbuild.FieldFreeDiskSizeMB, v)
	return u
}

// UpdateFreeDiskSizeMB sets the "free_disk_size_mb" field to the value that was provided on create.
func (u *EnvBuildUpsert) UpdateFreeDiskSizeMB() *EnvBuildUpsert {
	u.SetExcluded(envbuild.FieldFreeDiskSizeMB)
	return u
}

// AddFreeDiskSizeMB adds v to the "free_disk_size_mb" field.
func (u *EnvBuildUpsert) AddFreeDiskSizeMB(v int64) *EnvBuildUpsert {
	u.Add(envbuild.FieldFreeDiskSizeMB, v)
	return u
}

// SetTotalDiskSizeMB sets the "total_disk_size_mb" field.
func (u *EnvBuildUpsert) SetTotalDiskSizeMB(v int64) *EnvBuildUpsert {
	u.Set(envbuild.FieldTotalDiskSizeMB, v)
	return u
}

// UpdateTotalDiskSizeMB sets the "total_disk_size_mb" field to the value that was provided on create.
func (u *EnvBuildUpsert) UpdateTotalDiskSizeMB() *EnvBuildUpsert {
	u.SetExcluded(envbuild.FieldTotalDiskSizeMB)
	return u
}

// AddTotalDiskSizeMB adds v to the "total_disk_size_mb" field.
func (u *EnvBuildUpsert) AddTotalDiskSizeMB(v int64) *EnvBuildUpsert {
	u.Add(envbuild.FieldTotalDiskSizeMB, v)
	return u
}

// ClearTotalDiskSizeMB clears the value of the "total_disk_size_mb" field.
func (u *EnvBuildUpsert) ClearTotalDiskSizeMB() *EnvBuildUpsert {
	u.SetNull(envbuild.FieldTotalDiskSizeMB)
	return u
}

// SetKernelVersion sets the "kernel_version" field.
func (u *EnvBuildUpsert) SetKernelVersion(v string) *EnvBuildUpsert {
	u.Set(envbuild.FieldKernelVersion, v)
	return u
}

// UpdateKernelVersion sets the "kernel_version" field to the value that was provided on create.
func (u *EnvBuildUpsert) UpdateKernelVersion() *EnvBuildUpsert {
	u.SetExcluded(envbuild.FieldKernelVersion)
	return u
}

// SetFirecrackerVersion sets the "firecracker_version" field.
func (u *EnvBuildUpsert) SetFirecrackerVersion(v string) *EnvBuildUpsert {
	u.Set(envbuild.FieldFirecrackerVersion, v)
	return u
}

// UpdateFirecrackerVersion sets the "firecracker_version" field to the value that was provided on create.
func (u *EnvBuildUpsert) UpdateFirecrackerVersion() *EnvBuildUpsert {
	u.SetExcluded(envbuild.FieldFirecrackerVersion)
	return u
}

// SetEnvdVersion sets the "envd_version" field.
func (u *EnvBuildUpsert) SetEnvdVersion(v string) *EnvBuildUpsert {
	u.Set(envbuild.FieldEnvdVersion, v)
	return u
}

// UpdateEnvdVersion sets the "envd_version" field to the value that was provided on create.
func (u *EnvBuildUpsert) UpdateEnvdVersion() *EnvBuildUpsert {
	u.SetExcluded(envbuild.FieldEnvdVersion)
	return u
}

// ClearEnvdVersion clears the value of the "envd_version" field.
func (u *EnvBuildUpsert) ClearEnvdVersion() *EnvBuildUpsert {
	u.SetNull(envbuild.FieldEnvdVersion)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.EnvBuild.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(envbuild.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *EnvBuildUpsertOne) UpdateNewValues() *EnvBuildUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(envbuild.FieldID)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(envbuild.FieldCreatedAt)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.EnvBuild.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *EnvBuildUpsertOne) Ignore() *EnvBuildUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *EnvBuildUpsertOne) DoNothing() *EnvBuildUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the EnvBuildCreate.OnConflict
// documentation for more info.
func (u *EnvBuildUpsertOne) Update(set func(*EnvBuildUpsert)) *EnvBuildUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&EnvBuildUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *EnvBuildUpsertOne) SetUpdatedAt(v time.Time) *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *EnvBuildUpsertOne) UpdateUpdatedAt() *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetFinishedAt sets the "finished_at" field.
func (u *EnvBuildUpsertOne) SetFinishedAt(v time.Time) *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.SetFinishedAt(v)
	})
}

// UpdateFinishedAt sets the "finished_at" field to the value that was provided on create.
func (u *EnvBuildUpsertOne) UpdateFinishedAt() *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.UpdateFinishedAt()
	})
}

// ClearFinishedAt clears the value of the "finished_at" field.
func (u *EnvBuildUpsertOne) ClearFinishedAt() *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.ClearFinishedAt()
	})
}

// SetEnvID sets the "env_id" field.
func (u *EnvBuildUpsertOne) SetEnvID(v string) *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.SetEnvID(v)
	})
}

// UpdateEnvID sets the "env_id" field to the value that was provided on create.
func (u *EnvBuildUpsertOne) UpdateEnvID() *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.UpdateEnvID()
	})
}

// ClearEnvID clears the value of the "env_id" field.
func (u *EnvBuildUpsertOne) ClearEnvID() *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.ClearEnvID()
	})
}

// SetStatus sets the "status" field.
func (u *EnvBuildUpsertOne) SetStatus(v envbuild.Status) *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *EnvBuildUpsertOne) UpdateStatus() *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.UpdateStatus()
	})
}

// SetDockerfile sets the "dockerfile" field.
func (u *EnvBuildUpsertOne) SetDockerfile(v string) *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.SetDockerfile(v)
	})
}

// UpdateDockerfile sets the "dockerfile" field to the value that was provided on create.
func (u *EnvBuildUpsertOne) UpdateDockerfile() *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.UpdateDockerfile()
	})
}

// ClearDockerfile clears the value of the "dockerfile" field.
func (u *EnvBuildUpsertOne) ClearDockerfile() *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.ClearDockerfile()
	})
}

// SetStartCmd sets the "start_cmd" field.
func (u *EnvBuildUpsertOne) SetStartCmd(v string) *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.SetStartCmd(v)
	})
}

// UpdateStartCmd sets the "start_cmd" field to the value that was provided on create.
func (u *EnvBuildUpsertOne) UpdateStartCmd() *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.UpdateStartCmd()
	})
}

// ClearStartCmd clears the value of the "start_cmd" field.
func (u *EnvBuildUpsertOne) ClearStartCmd() *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.ClearStartCmd()
	})
}

// SetReadyCmd sets the "ready_cmd" field.
func (u *EnvBuildUpsertOne) SetReadyCmd(v string) *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.SetReadyCmd(v)
	})
}

// UpdateReadyCmd sets the "ready_cmd" field to the value that was provided on create.
func (u *EnvBuildUpsertOne) UpdateReadyCmd() *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.UpdateReadyCmd()
	})
}

// ClearReadyCmd clears the value of the "ready_cmd" field.
func (u *EnvBuildUpsertOne) ClearReadyCmd() *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.ClearReadyCmd()
	})
}

// SetVcpu sets the "vcpu" field.
func (u *EnvBuildUpsertOne) SetVcpu(v int64) *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.SetVcpu(v)
	})
}

// AddVcpu adds v to the "vcpu" field.
func (u *EnvBuildUpsertOne) AddVcpu(v int64) *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.AddVcpu(v)
	})
}

// UpdateVcpu sets the "vcpu" field to the value that was provided on create.
func (u *EnvBuildUpsertOne) UpdateVcpu() *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.UpdateVcpu()
	})
}

// SetRAMMB sets the "ram_mb" field.
func (u *EnvBuildUpsertOne) SetRAMMB(v int64) *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.SetRAMMB(v)
	})
}

// AddRAMMB adds v to the "ram_mb" field.
func (u *EnvBuildUpsertOne) AddRAMMB(v int64) *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.AddRAMMB(v)
	})
}

// UpdateRAMMB sets the "ram_mb" field to the value that was provided on create.
func (u *EnvBuildUpsertOne) UpdateRAMMB() *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.UpdateRAMMB()
	})
}

// SetFreeDiskSizeMB sets the "free_disk_size_mb" field.
func (u *EnvBuildUpsertOne) SetFreeDiskSizeMB(v int64) *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.SetFreeDiskSizeMB(v)
	})
}

// AddFreeDiskSizeMB adds v to the "free_disk_size_mb" field.
func (u *EnvBuildUpsertOne) AddFreeDiskSizeMB(v int64) *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.AddFreeDiskSizeMB(v)
	})
}

// UpdateFreeDiskSizeMB sets the "free_disk_size_mb" field to the value that was provided on create.
func (u *EnvBuildUpsertOne) UpdateFreeDiskSizeMB() *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.UpdateFreeDiskSizeMB()
	})
}

// SetTotalDiskSizeMB sets the "total_disk_size_mb" field.
func (u *EnvBuildUpsertOne) SetTotalDiskSizeMB(v int64) *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.SetTotalDiskSizeMB(v)
	})
}

// AddTotalDiskSizeMB adds v to the "total_disk_size_mb" field.
func (u *EnvBuildUpsertOne) AddTotalDiskSizeMB(v int64) *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.AddTotalDiskSizeMB(v)
	})
}

// UpdateTotalDiskSizeMB sets the "total_disk_size_mb" field to the value that was provided on create.
func (u *EnvBuildUpsertOne) UpdateTotalDiskSizeMB() *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.UpdateTotalDiskSizeMB()
	})
}

// ClearTotalDiskSizeMB clears the value of the "total_disk_size_mb" field.
func (u *EnvBuildUpsertOne) ClearTotalDiskSizeMB() *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.ClearTotalDiskSizeMB()
	})
}

// SetKernelVersion sets the "kernel_version" field.
func (u *EnvBuildUpsertOne) SetKernelVersion(v string) *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.SetKernelVersion(v)
	})
}

// UpdateKernelVersion sets the "kernel_version" field to the value that was provided on create.
func (u *EnvBuildUpsertOne) UpdateKernelVersion() *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.UpdateKernelVersion()
	})
}

// SetFirecrackerVersion sets the "firecracker_version" field.
func (u *EnvBuildUpsertOne) SetFirecrackerVersion(v string) *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.SetFirecrackerVersion(v)
	})
}

// UpdateFirecrackerVersion sets the "firecracker_version" field to the value that was provided on create.
func (u *EnvBuildUpsertOne) UpdateFirecrackerVersion() *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.UpdateFirecrackerVersion()
	})
}

// SetEnvdVersion sets the "envd_version" field.
func (u *EnvBuildUpsertOne) SetEnvdVersion(v string) *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.SetEnvdVersion(v)
	})
}

// UpdateEnvdVersion sets the "envd_version" field to the value that was provided on create.
func (u *EnvBuildUpsertOne) UpdateEnvdVersion() *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.UpdateEnvdVersion()
	})
}

// ClearEnvdVersion clears the value of the "envd_version" field.
func (u *EnvBuildUpsertOne) ClearEnvdVersion() *EnvBuildUpsertOne {
	return u.Update(func(s *EnvBuildUpsert) {
		s.ClearEnvdVersion()
	})
}

// Exec executes the query.
func (u *EnvBuildUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("models: missing options for EnvBuildCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *EnvBuildUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *EnvBuildUpsertOne) ID(ctx context.Context) (id uuid.UUID, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("models: EnvBuildUpsertOne.ID is not supported by MySQL driver. Use EnvBuildUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *EnvBuildUpsertOne) IDX(ctx context.Context) uuid.UUID {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// EnvBuildCreateBulk is the builder for creating many EnvBuild entities in bulk.
type EnvBuildCreateBulk struct {
	config
	err      error
	builders []*EnvBuildCreate
	conflict []sql.ConflictOption
}

// Save creates the EnvBuild entities in the database.
func (ebcb *EnvBuildCreateBulk) Save(ctx context.Context) ([]*EnvBuild, error) {
	if ebcb.err != nil {
		return nil, ebcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(ebcb.builders))
	nodes := make([]*EnvBuild, len(ebcb.builders))
	mutators := make([]Mutator, len(ebcb.builders))
	for i := range ebcb.builders {
		func(i int, root context.Context) {
			builder := ebcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*EnvBuildMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, ebcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = ebcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, ebcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, ebcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (ebcb *EnvBuildCreateBulk) SaveX(ctx context.Context) []*EnvBuild {
	v, err := ebcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ebcb *EnvBuildCreateBulk) Exec(ctx context.Context) error {
	_, err := ebcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ebcb *EnvBuildCreateBulk) ExecX(ctx context.Context) {
	if err := ebcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.EnvBuild.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.EnvBuildUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (ebcb *EnvBuildCreateBulk) OnConflict(opts ...sql.ConflictOption) *EnvBuildUpsertBulk {
	ebcb.conflict = opts
	return &EnvBuildUpsertBulk{
		create: ebcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.EnvBuild.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (ebcb *EnvBuildCreateBulk) OnConflictColumns(columns ...string) *EnvBuildUpsertBulk {
	ebcb.conflict = append(ebcb.conflict, sql.ConflictColumns(columns...))
	return &EnvBuildUpsertBulk{
		create: ebcb,
	}
}

// EnvBuildUpsertBulk is the builder for "upsert"-ing
// a bulk of EnvBuild nodes.
type EnvBuildUpsertBulk struct {
	create *EnvBuildCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.EnvBuild.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(envbuild.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *EnvBuildUpsertBulk) UpdateNewValues() *EnvBuildUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(envbuild.FieldID)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(envbuild.FieldCreatedAt)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.EnvBuild.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *EnvBuildUpsertBulk) Ignore() *EnvBuildUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *EnvBuildUpsertBulk) DoNothing() *EnvBuildUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the EnvBuildCreateBulk.OnConflict
// documentation for more info.
func (u *EnvBuildUpsertBulk) Update(set func(*EnvBuildUpsert)) *EnvBuildUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&EnvBuildUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *EnvBuildUpsertBulk) SetUpdatedAt(v time.Time) *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *EnvBuildUpsertBulk) UpdateUpdatedAt() *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetFinishedAt sets the "finished_at" field.
func (u *EnvBuildUpsertBulk) SetFinishedAt(v time.Time) *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.SetFinishedAt(v)
	})
}

// UpdateFinishedAt sets the "finished_at" field to the value that was provided on create.
func (u *EnvBuildUpsertBulk) UpdateFinishedAt() *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.UpdateFinishedAt()
	})
}

// ClearFinishedAt clears the value of the "finished_at" field.
func (u *EnvBuildUpsertBulk) ClearFinishedAt() *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.ClearFinishedAt()
	})
}

// SetEnvID sets the "env_id" field.
func (u *EnvBuildUpsertBulk) SetEnvID(v string) *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.SetEnvID(v)
	})
}

// UpdateEnvID sets the "env_id" field to the value that was provided on create.
func (u *EnvBuildUpsertBulk) UpdateEnvID() *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.UpdateEnvID()
	})
}

// ClearEnvID clears the value of the "env_id" field.
func (u *EnvBuildUpsertBulk) ClearEnvID() *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.ClearEnvID()
	})
}

// SetStatus sets the "status" field.
func (u *EnvBuildUpsertBulk) SetStatus(v envbuild.Status) *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *EnvBuildUpsertBulk) UpdateStatus() *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.UpdateStatus()
	})
}

// SetDockerfile sets the "dockerfile" field.
func (u *EnvBuildUpsertBulk) SetDockerfile(v string) *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.SetDockerfile(v)
	})
}

// UpdateDockerfile sets the "dockerfile" field to the value that was provided on create.
func (u *EnvBuildUpsertBulk) UpdateDockerfile() *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.UpdateDockerfile()
	})
}

// ClearDockerfile clears the value of the "dockerfile" field.
func (u *EnvBuildUpsertBulk) ClearDockerfile() *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.ClearDockerfile()
	})
}

// SetStartCmd sets the "start_cmd" field.
func (u *EnvBuildUpsertBulk) SetStartCmd(v string) *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.SetStartCmd(v)
	})
}

// UpdateStartCmd sets the "start_cmd" field to the value that was provided on create.
func (u *EnvBuildUpsertBulk) UpdateStartCmd() *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.UpdateStartCmd()
	})
}

// ClearStartCmd clears the value of the "start_cmd" field.
func (u *EnvBuildUpsertBulk) ClearStartCmd() *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.ClearStartCmd()
	})
}

// SetReadyCmd sets the "ready_cmd" field.
func (u *EnvBuildUpsertBulk) SetReadyCmd(v string) *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.SetReadyCmd(v)
	})
}

// UpdateReadyCmd sets the "ready_cmd" field to the value that was provided on create.
func (u *EnvBuildUpsertBulk) UpdateReadyCmd() *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.UpdateReadyCmd()
	})
}

// ClearReadyCmd clears the value of the "ready_cmd" field.
func (u *EnvBuildUpsertBulk) ClearReadyCmd() *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.ClearReadyCmd()
	})
}

// SetVcpu sets the "vcpu" field.
func (u *EnvBuildUpsertBulk) SetVcpu(v int64) *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.SetVcpu(v)
	})
}

// AddVcpu adds v to the "vcpu" field.
func (u *EnvBuildUpsertBulk) AddVcpu(v int64) *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.AddVcpu(v)
	})
}

// UpdateVcpu sets the "vcpu" field to the value that was provided on create.
func (u *EnvBuildUpsertBulk) UpdateVcpu() *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.UpdateVcpu()
	})
}

// SetRAMMB sets the "ram_mb" field.
func (u *EnvBuildUpsertBulk) SetRAMMB(v int64) *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.SetRAMMB(v)
	})
}

// AddRAMMB adds v to the "ram_mb" field.
func (u *EnvBuildUpsertBulk) AddRAMMB(v int64) *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.AddRAMMB(v)
	})
}

// UpdateRAMMB sets the "ram_mb" field to the value that was provided on create.
func (u *EnvBuildUpsertBulk) UpdateRAMMB() *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.UpdateRAMMB()
	})
}

// SetFreeDiskSizeMB sets the "free_disk_size_mb" field.
func (u *EnvBuildUpsertBulk) SetFreeDiskSizeMB(v int64) *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.SetFreeDiskSizeMB(v)
	})
}

// AddFreeDiskSizeMB adds v to the "free_disk_size_mb" field.
func (u *EnvBuildUpsertBulk) AddFreeDiskSizeMB(v int64) *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.AddFreeDiskSizeMB(v)
	})
}

// UpdateFreeDiskSizeMB sets the "free_disk_size_mb" field to the value that was provided on create.
func (u *EnvBuildUpsertBulk) UpdateFreeDiskSizeMB() *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.UpdateFreeDiskSizeMB()
	})
}

// SetTotalDiskSizeMB sets the "total_disk_size_mb" field.
func (u *EnvBuildUpsertBulk) SetTotalDiskSizeMB(v int64) *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.SetTotalDiskSizeMB(v)
	})
}

// AddTotalDiskSizeMB adds v to the "total_disk_size_mb" field.
func (u *EnvBuildUpsertBulk) AddTotalDiskSizeMB(v int64) *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.AddTotalDiskSizeMB(v)
	})
}

// UpdateTotalDiskSizeMB sets the "total_disk_size_mb" field to the value that was provided on create.
func (u *EnvBuildUpsertBulk) UpdateTotalDiskSizeMB() *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.UpdateTotalDiskSizeMB()
	})
}

// ClearTotalDiskSizeMB clears the value of the "total_disk_size_mb" field.
func (u *EnvBuildUpsertBulk) ClearTotalDiskSizeMB() *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.ClearTotalDiskSizeMB()
	})
}

// SetKernelVersion sets the "kernel_version" field.
func (u *EnvBuildUpsertBulk) SetKernelVersion(v string) *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.SetKernelVersion(v)
	})
}

// UpdateKernelVersion sets the "kernel_version" field to the value that was provided on create.
func (u *EnvBuildUpsertBulk) UpdateKernelVersion() *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.UpdateKernelVersion()
	})
}

// SetFirecrackerVersion sets the "firecracker_version" field.
func (u *EnvBuildUpsertBulk) SetFirecrackerVersion(v string) *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.SetFirecrackerVersion(v)
	})
}

// UpdateFirecrackerVersion sets the "firecracker_version" field to the value that was provided on create.
func (u *EnvBuildUpsertBulk) UpdateFirecrackerVersion() *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.UpdateFirecrackerVersion()
	})
}

// SetEnvdVersion sets the "envd_version" field.
func (u *EnvBuildUpsertBulk) SetEnvdVersion(v string) *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.SetEnvdVersion(v)
	})
}

// UpdateEnvdVersion sets the "envd_version" field to the value that was provided on create.
func (u *EnvBuildUpsertBulk) UpdateEnvdVersion() *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.UpdateEnvdVersion()
	})
}

// ClearEnvdVersion clears the value of the "envd_version" field.
func (u *EnvBuildUpsertBulk) ClearEnvdVersion() *EnvBuildUpsertBulk {
	return u.Update(func(s *EnvBuildUpsert) {
		s.ClearEnvdVersion()
	})
}

// Exec executes the query.
func (u *EnvBuildUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("models: OnConflict was set for builder %d. Set it on the EnvBuildCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("models: missing options for EnvBuildCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *EnvBuildUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
