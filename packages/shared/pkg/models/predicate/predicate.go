// Code generated by ent, DO NOT EDIT.

package predicate

import (
	"entgo.io/ent/dialect/sql"
)

// AccessToken is the predicate function for accesstoken builders.
type AccessToken func(*sql.Selector)

// Env is the predicate function for env builders.
type Env func(*sql.Selector)

// EnvAlias is the predicate function for envalias builders.
type EnvAlias func(*sql.Selector)

// EnvBuild is the predicate function for envbuild builders.
type EnvBuild func(*sql.Selector)

// Snapshot is the predicate function for snapshot builders.
type Snapshot func(*sql.Selector)

// Team is the predicate function for team builders.
type Team func(*sql.Selector)

// TeamAPIKey is the predicate function for teamapikey builders.
type TeamAPIKey func(*sql.Selector)

// Tier is the predicate function for tier builders.
type Tier func(*sql.Selector)

// User is the predicate function for user builders.
type User func(*sql.Selector)

// UsersTeams is the predicate function for usersteams builders.
type UsersTeams func(*sql.Selector)
