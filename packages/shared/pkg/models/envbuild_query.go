// Code generated by ent, DO NOT EDIT.

package models

import (
	"context"
	"fmt"
	"math"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/env"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/envbuild"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/internal"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/predicate"
	"github.com/google/uuid"
)

// EnvBuildQuery is the builder for querying EnvBuild entities.
type EnvBuildQuery struct {
	config
	ctx        *QueryContext
	order      []envbuild.OrderOption
	inters     []Interceptor
	predicates []predicate.EnvBuild
	withEnv    *EnvQuery
	modifiers  []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the EnvBuildQuery builder.
func (ebq *EnvBuildQuery) Where(ps ...predicate.EnvBuild) *EnvBuildQuery {
	ebq.predicates = append(ebq.predicates, ps...)
	return ebq
}

// Limit the number of records to be returned by this query.
func (ebq *EnvBuildQuery) Limit(limit int) *EnvBuildQuery {
	ebq.ctx.Limit = &limit
	return ebq
}

// Offset to start from.
func (ebq *EnvBuildQuery) Offset(offset int) *EnvBuildQuery {
	ebq.ctx.Offset = &offset
	return ebq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (ebq *EnvBuildQuery) Unique(unique bool) *EnvBuildQuery {
	ebq.ctx.Unique = &unique
	return ebq
}

// Order specifies how the records should be ordered.
func (ebq *EnvBuildQuery) Order(o ...envbuild.OrderOption) *EnvBuildQuery {
	ebq.order = append(ebq.order, o...)
	return ebq
}

// QueryEnv chains the current query on the "env" edge.
func (ebq *EnvBuildQuery) QueryEnv() *EnvQuery {
	query := (&EnvClient{config: ebq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := ebq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := ebq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(envbuild.Table, envbuild.FieldID, selector),
			sqlgraph.To(env.Table, env.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, envbuild.EnvTable, envbuild.EnvColumn),
		)
		schemaConfig := ebq.schemaConfig
		step.To.Schema = schemaConfig.Env
		step.Edge.Schema = schemaConfig.EnvBuild
		fromU = sqlgraph.SetNeighbors(ebq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first EnvBuild entity from the query.
// Returns a *NotFoundError when no EnvBuild was found.
func (ebq *EnvBuildQuery) First(ctx context.Context) (*EnvBuild, error) {
	nodes, err := ebq.Limit(1).All(setContextOp(ctx, ebq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{envbuild.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (ebq *EnvBuildQuery) FirstX(ctx context.Context) *EnvBuild {
	node, err := ebq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first EnvBuild ID from the query.
// Returns a *NotFoundError when no EnvBuild ID was found.
func (ebq *EnvBuildQuery) FirstID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = ebq.Limit(1).IDs(setContextOp(ctx, ebq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{envbuild.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (ebq *EnvBuildQuery) FirstIDX(ctx context.Context) uuid.UUID {
	id, err := ebq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single EnvBuild entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one EnvBuild entity is found.
// Returns a *NotFoundError when no EnvBuild entities are found.
func (ebq *EnvBuildQuery) Only(ctx context.Context) (*EnvBuild, error) {
	nodes, err := ebq.Limit(2).All(setContextOp(ctx, ebq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{envbuild.Label}
	default:
		return nil, &NotSingularError{envbuild.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (ebq *EnvBuildQuery) OnlyX(ctx context.Context) *EnvBuild {
	node, err := ebq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only EnvBuild ID in the query.
// Returns a *NotSingularError when more than one EnvBuild ID is found.
// Returns a *NotFoundError when no entities are found.
func (ebq *EnvBuildQuery) OnlyID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = ebq.Limit(2).IDs(setContextOp(ctx, ebq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{envbuild.Label}
	default:
		err = &NotSingularError{envbuild.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (ebq *EnvBuildQuery) OnlyIDX(ctx context.Context) uuid.UUID {
	id, err := ebq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of EnvBuilds.
func (ebq *EnvBuildQuery) All(ctx context.Context) ([]*EnvBuild, error) {
	ctx = setContextOp(ctx, ebq.ctx, "All")
	if err := ebq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*EnvBuild, *EnvBuildQuery]()
	return withInterceptors[[]*EnvBuild](ctx, ebq, qr, ebq.inters)
}

// AllX is like All, but panics if an error occurs.
func (ebq *EnvBuildQuery) AllX(ctx context.Context) []*EnvBuild {
	nodes, err := ebq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of EnvBuild IDs.
func (ebq *EnvBuildQuery) IDs(ctx context.Context) (ids []uuid.UUID, err error) {
	if ebq.ctx.Unique == nil && ebq.path != nil {
		ebq.Unique(true)
	}
	ctx = setContextOp(ctx, ebq.ctx, "IDs")
	if err = ebq.Select(envbuild.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (ebq *EnvBuildQuery) IDsX(ctx context.Context) []uuid.UUID {
	ids, err := ebq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (ebq *EnvBuildQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, ebq.ctx, "Count")
	if err := ebq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, ebq, querierCount[*EnvBuildQuery](), ebq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (ebq *EnvBuildQuery) CountX(ctx context.Context) int {
	count, err := ebq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (ebq *EnvBuildQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, ebq.ctx, "Exist")
	switch _, err := ebq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("models: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (ebq *EnvBuildQuery) ExistX(ctx context.Context) bool {
	exist, err := ebq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the EnvBuildQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (ebq *EnvBuildQuery) Clone() *EnvBuildQuery {
	if ebq == nil {
		return nil
	}
	return &EnvBuildQuery{
		config:     ebq.config,
		ctx:        ebq.ctx.Clone(),
		order:      append([]envbuild.OrderOption{}, ebq.order...),
		inters:     append([]Interceptor{}, ebq.inters...),
		predicates: append([]predicate.EnvBuild{}, ebq.predicates...),
		withEnv:    ebq.withEnv.Clone(),
		// clone intermediate query.
		sql:  ebq.sql.Clone(),
		path: ebq.path,
	}
}

// WithEnv tells the query-builder to eager-load the nodes that are connected to
// the "env" edge. The optional arguments are used to configure the query builder of the edge.
func (ebq *EnvBuildQuery) WithEnv(opts ...func(*EnvQuery)) *EnvBuildQuery {
	query := (&EnvClient{config: ebq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	ebq.withEnv = query
	return ebq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.EnvBuild.Query().
//		GroupBy(envbuild.FieldCreatedAt).
//		Aggregate(models.Count()).
//		Scan(ctx, &v)
func (ebq *EnvBuildQuery) GroupBy(field string, fields ...string) *EnvBuildGroupBy {
	ebq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &EnvBuildGroupBy{build: ebq}
	grbuild.flds = &ebq.ctx.Fields
	grbuild.label = envbuild.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//	}
//
//	client.EnvBuild.Query().
//		Select(envbuild.FieldCreatedAt).
//		Scan(ctx, &v)
func (ebq *EnvBuildQuery) Select(fields ...string) *EnvBuildSelect {
	ebq.ctx.Fields = append(ebq.ctx.Fields, fields...)
	sbuild := &EnvBuildSelect{EnvBuildQuery: ebq}
	sbuild.label = envbuild.Label
	sbuild.flds, sbuild.scan = &ebq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a EnvBuildSelect configured with the given aggregations.
func (ebq *EnvBuildQuery) Aggregate(fns ...AggregateFunc) *EnvBuildSelect {
	return ebq.Select().Aggregate(fns...)
}

func (ebq *EnvBuildQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range ebq.inters {
		if inter == nil {
			return fmt.Errorf("models: uninitialized interceptor (forgotten import models/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, ebq); err != nil {
				return err
			}
		}
	}
	for _, f := range ebq.ctx.Fields {
		if !envbuild.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("models: invalid field %q for query", f)}
		}
	}
	if ebq.path != nil {
		prev, err := ebq.path(ctx)
		if err != nil {
			return err
		}
		ebq.sql = prev
	}
	return nil
}

func (ebq *EnvBuildQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*EnvBuild, error) {
	var (
		nodes       = []*EnvBuild{}
		_spec       = ebq.querySpec()
		loadedTypes = [1]bool{
			ebq.withEnv != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*EnvBuild).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &EnvBuild{config: ebq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	_spec.Node.Schema = ebq.schemaConfig.EnvBuild
	ctx = internal.NewSchemaConfigContext(ctx, ebq.schemaConfig)
	if len(ebq.modifiers) > 0 {
		_spec.Modifiers = ebq.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, ebq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := ebq.withEnv; query != nil {
		if err := ebq.loadEnv(ctx, query, nodes, nil,
			func(n *EnvBuild, e *Env) { n.Edges.Env = e }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (ebq *EnvBuildQuery) loadEnv(ctx context.Context, query *EnvQuery, nodes []*EnvBuild, init func(*EnvBuild), assign func(*EnvBuild, *Env)) error {
	ids := make([]string, 0, len(nodes))
	nodeids := make(map[string][]*EnvBuild)
	for i := range nodes {
		if nodes[i].EnvID == nil {
			continue
		}
		fk := *nodes[i].EnvID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(env.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "env_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}

func (ebq *EnvBuildQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := ebq.querySpec()
	_spec.Node.Schema = ebq.schemaConfig.EnvBuild
	ctx = internal.NewSchemaConfigContext(ctx, ebq.schemaConfig)
	if len(ebq.modifiers) > 0 {
		_spec.Modifiers = ebq.modifiers
	}
	_spec.Node.Columns = ebq.ctx.Fields
	if len(ebq.ctx.Fields) > 0 {
		_spec.Unique = ebq.ctx.Unique != nil && *ebq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, ebq.driver, _spec)
}

func (ebq *EnvBuildQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(envbuild.Table, envbuild.Columns, sqlgraph.NewFieldSpec(envbuild.FieldID, field.TypeUUID))
	_spec.From = ebq.sql
	if unique := ebq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if ebq.path != nil {
		_spec.Unique = true
	}
	if fields := ebq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, envbuild.FieldID)
		for i := range fields {
			if fields[i] != envbuild.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if ebq.withEnv != nil {
			_spec.Node.AddColumnOnce(envbuild.FieldEnvID)
		}
	}
	if ps := ebq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := ebq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := ebq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := ebq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (ebq *EnvBuildQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(ebq.driver.Dialect())
	t1 := builder.Table(envbuild.Table)
	columns := ebq.ctx.Fields
	if len(columns) == 0 {
		columns = envbuild.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if ebq.sql != nil {
		selector = ebq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if ebq.ctx.Unique != nil && *ebq.ctx.Unique {
		selector.Distinct()
	}
	t1.Schema(ebq.schemaConfig.EnvBuild)
	ctx = internal.NewSchemaConfigContext(ctx, ebq.schemaConfig)
	selector.WithContext(ctx)
	for _, m := range ebq.modifiers {
		m(selector)
	}
	for _, p := range ebq.predicates {
		p(selector)
	}
	for _, p := range ebq.order {
		p(selector)
	}
	if offset := ebq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := ebq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// Modify adds a query modifier for attaching custom logic to queries.
func (ebq *EnvBuildQuery) Modify(modifiers ...func(s *sql.Selector)) *EnvBuildSelect {
	ebq.modifiers = append(ebq.modifiers, modifiers...)
	return ebq.Select()
}

// EnvBuildGroupBy is the group-by builder for EnvBuild entities.
type EnvBuildGroupBy struct {
	selector
	build *EnvBuildQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (ebgb *EnvBuildGroupBy) Aggregate(fns ...AggregateFunc) *EnvBuildGroupBy {
	ebgb.fns = append(ebgb.fns, fns...)
	return ebgb
}

// Scan applies the selector query and scans the result into the given value.
func (ebgb *EnvBuildGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, ebgb.build.ctx, "GroupBy")
	if err := ebgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*EnvBuildQuery, *EnvBuildGroupBy](ctx, ebgb.build, ebgb, ebgb.build.inters, v)
}

func (ebgb *EnvBuildGroupBy) sqlScan(ctx context.Context, root *EnvBuildQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(ebgb.fns))
	for _, fn := range ebgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*ebgb.flds)+len(ebgb.fns))
		for _, f := range *ebgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*ebgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := ebgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// EnvBuildSelect is the builder for selecting fields of EnvBuild entities.
type EnvBuildSelect struct {
	*EnvBuildQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (ebs *EnvBuildSelect) Aggregate(fns ...AggregateFunc) *EnvBuildSelect {
	ebs.fns = append(ebs.fns, fns...)
	return ebs
}

// Scan applies the selector query and scans the result into the given value.
func (ebs *EnvBuildSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, ebs.ctx, "Select")
	if err := ebs.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*EnvBuildQuery, *EnvBuildSelect](ctx, ebs.EnvBuildQuery, ebs, ebs.inters, v)
}

func (ebs *EnvBuildSelect) sqlScan(ctx context.Context, root *EnvBuildQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(ebs.fns))
	for _, fn := range ebs.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*ebs.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := ebs.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (ebs *EnvBuildSelect) Modify(modifiers ...func(s *sql.Selector)) *EnvBuildSelect {
	ebs.modifiers = append(ebs.modifiers, modifiers...)
	return ebs
}
