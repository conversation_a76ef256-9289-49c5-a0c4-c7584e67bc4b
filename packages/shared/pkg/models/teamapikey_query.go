// Code generated by ent, DO NOT EDIT.

package models

import (
	"context"
	"fmt"
	"math"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/internal"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/predicate"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/team"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/teamapikey"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/user"
	"github.com/google/uuid"
)

// TeamAPIKeyQuery is the builder for querying TeamAPIKey entities.
type TeamAPIKeyQuery struct {
	config
	ctx         *QueryContext
	order       []teamapikey.OrderOption
	inters      []Interceptor
	predicates  []predicate.TeamAPIKey
	withTeam    *TeamQuery
	withCreator *UserQuery
	modifiers   []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the TeamAPIKeyQuery builder.
func (takq *TeamAPIKeyQuery) Where(ps ...predicate.TeamAPIKey) *TeamAPIKeyQuery {
	takq.predicates = append(takq.predicates, ps...)
	return takq
}

// Limit the number of records to be returned by this query.
func (takq *TeamAPIKeyQuery) Limit(limit int) *TeamAPIKeyQuery {
	takq.ctx.Limit = &limit
	return takq
}

// Offset to start from.
func (takq *TeamAPIKeyQuery) Offset(offset int) *TeamAPIKeyQuery {
	takq.ctx.Offset = &offset
	return takq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (takq *TeamAPIKeyQuery) Unique(unique bool) *TeamAPIKeyQuery {
	takq.ctx.Unique = &unique
	return takq
}

// Order specifies how the records should be ordered.
func (takq *TeamAPIKeyQuery) Order(o ...teamapikey.OrderOption) *TeamAPIKeyQuery {
	takq.order = append(takq.order, o...)
	return takq
}

// QueryTeam chains the current query on the "team" edge.
func (takq *TeamAPIKeyQuery) QueryTeam() *TeamQuery {
	query := (&TeamClient{config: takq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := takq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := takq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(teamapikey.Table, teamapikey.FieldID, selector),
			sqlgraph.To(team.Table, team.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, teamapikey.TeamTable, teamapikey.TeamColumn),
		)
		schemaConfig := takq.schemaConfig
		step.To.Schema = schemaConfig.Team
		step.Edge.Schema = schemaConfig.TeamAPIKey
		fromU = sqlgraph.SetNeighbors(takq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryCreator chains the current query on the "creator" edge.
func (takq *TeamAPIKeyQuery) QueryCreator() *UserQuery {
	query := (&UserClient{config: takq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := takq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := takq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(teamapikey.Table, teamapikey.FieldID, selector),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, teamapikey.CreatorTable, teamapikey.CreatorColumn),
		)
		schemaConfig := takq.schemaConfig
		step.To.Schema = schemaConfig.User
		step.Edge.Schema = schemaConfig.TeamAPIKey
		fromU = sqlgraph.SetNeighbors(takq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first TeamAPIKey entity from the query.
// Returns a *NotFoundError when no TeamAPIKey was found.
func (takq *TeamAPIKeyQuery) First(ctx context.Context) (*TeamAPIKey, error) {
	nodes, err := takq.Limit(1).All(setContextOp(ctx, takq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{teamapikey.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (takq *TeamAPIKeyQuery) FirstX(ctx context.Context) *TeamAPIKey {
	node, err := takq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first TeamAPIKey ID from the query.
// Returns a *NotFoundError when no TeamAPIKey ID was found.
func (takq *TeamAPIKeyQuery) FirstID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = takq.Limit(1).IDs(setContextOp(ctx, takq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{teamapikey.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (takq *TeamAPIKeyQuery) FirstIDX(ctx context.Context) uuid.UUID {
	id, err := takq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single TeamAPIKey entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one TeamAPIKey entity is found.
// Returns a *NotFoundError when no TeamAPIKey entities are found.
func (takq *TeamAPIKeyQuery) Only(ctx context.Context) (*TeamAPIKey, error) {
	nodes, err := takq.Limit(2).All(setContextOp(ctx, takq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{teamapikey.Label}
	default:
		return nil, &NotSingularError{teamapikey.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (takq *TeamAPIKeyQuery) OnlyX(ctx context.Context) *TeamAPIKey {
	node, err := takq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only TeamAPIKey ID in the query.
// Returns a *NotSingularError when more than one TeamAPIKey ID is found.
// Returns a *NotFoundError when no entities are found.
func (takq *TeamAPIKeyQuery) OnlyID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = takq.Limit(2).IDs(setContextOp(ctx, takq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{teamapikey.Label}
	default:
		err = &NotSingularError{teamapikey.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (takq *TeamAPIKeyQuery) OnlyIDX(ctx context.Context) uuid.UUID {
	id, err := takq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of TeamAPIKeys.
func (takq *TeamAPIKeyQuery) All(ctx context.Context) ([]*TeamAPIKey, error) {
	ctx = setContextOp(ctx, takq.ctx, "All")
	if err := takq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*TeamAPIKey, *TeamAPIKeyQuery]()
	return withInterceptors[[]*TeamAPIKey](ctx, takq, qr, takq.inters)
}

// AllX is like All, but panics if an error occurs.
func (takq *TeamAPIKeyQuery) AllX(ctx context.Context) []*TeamAPIKey {
	nodes, err := takq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of TeamAPIKey IDs.
func (takq *TeamAPIKeyQuery) IDs(ctx context.Context) (ids []uuid.UUID, err error) {
	if takq.ctx.Unique == nil && takq.path != nil {
		takq.Unique(true)
	}
	ctx = setContextOp(ctx, takq.ctx, "IDs")
	if err = takq.Select(teamapikey.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (takq *TeamAPIKeyQuery) IDsX(ctx context.Context) []uuid.UUID {
	ids, err := takq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (takq *TeamAPIKeyQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, takq.ctx, "Count")
	if err := takq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, takq, querierCount[*TeamAPIKeyQuery](), takq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (takq *TeamAPIKeyQuery) CountX(ctx context.Context) int {
	count, err := takq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (takq *TeamAPIKeyQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, takq.ctx, "Exist")
	switch _, err := takq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("models: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (takq *TeamAPIKeyQuery) ExistX(ctx context.Context) bool {
	exist, err := takq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the TeamAPIKeyQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (takq *TeamAPIKeyQuery) Clone() *TeamAPIKeyQuery {
	if takq == nil {
		return nil
	}
	return &TeamAPIKeyQuery{
		config:      takq.config,
		ctx:         takq.ctx.Clone(),
		order:       append([]teamapikey.OrderOption{}, takq.order...),
		inters:      append([]Interceptor{}, takq.inters...),
		predicates:  append([]predicate.TeamAPIKey{}, takq.predicates...),
		withTeam:    takq.withTeam.Clone(),
		withCreator: takq.withCreator.Clone(),
		// clone intermediate query.
		sql:  takq.sql.Clone(),
		path: takq.path,
	}
}

// WithTeam tells the query-builder to eager-load the nodes that are connected to
// the "team" edge. The optional arguments are used to configure the query builder of the edge.
func (takq *TeamAPIKeyQuery) WithTeam(opts ...func(*TeamQuery)) *TeamAPIKeyQuery {
	query := (&TeamClient{config: takq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	takq.withTeam = query
	return takq
}

// WithCreator tells the query-builder to eager-load the nodes that are connected to
// the "creator" edge. The optional arguments are used to configure the query builder of the edge.
func (takq *TeamAPIKeyQuery) WithCreator(opts ...func(*UserQuery)) *TeamAPIKeyQuery {
	query := (&UserClient{config: takq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	takq.withCreator = query
	return takq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		APIKey string `json:"api_key,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.TeamAPIKey.Query().
//		GroupBy(teamapikey.FieldAPIKey).
//		Aggregate(models.Count()).
//		Scan(ctx, &v)
func (takq *TeamAPIKeyQuery) GroupBy(field string, fields ...string) *TeamAPIKeyGroupBy {
	takq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &TeamAPIKeyGroupBy{build: takq}
	grbuild.flds = &takq.ctx.Fields
	grbuild.label = teamapikey.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		APIKey string `json:"api_key,omitempty"`
//	}
//
//	client.TeamAPIKey.Query().
//		Select(teamapikey.FieldAPIKey).
//		Scan(ctx, &v)
func (takq *TeamAPIKeyQuery) Select(fields ...string) *TeamAPIKeySelect {
	takq.ctx.Fields = append(takq.ctx.Fields, fields...)
	sbuild := &TeamAPIKeySelect{TeamAPIKeyQuery: takq}
	sbuild.label = teamapikey.Label
	sbuild.flds, sbuild.scan = &takq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a TeamAPIKeySelect configured with the given aggregations.
func (takq *TeamAPIKeyQuery) Aggregate(fns ...AggregateFunc) *TeamAPIKeySelect {
	return takq.Select().Aggregate(fns...)
}

func (takq *TeamAPIKeyQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range takq.inters {
		if inter == nil {
			return fmt.Errorf("models: uninitialized interceptor (forgotten import models/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, takq); err != nil {
				return err
			}
		}
	}
	for _, f := range takq.ctx.Fields {
		if !teamapikey.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("models: invalid field %q for query", f)}
		}
	}
	if takq.path != nil {
		prev, err := takq.path(ctx)
		if err != nil {
			return err
		}
		takq.sql = prev
	}
	return nil
}

func (takq *TeamAPIKeyQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*TeamAPIKey, error) {
	var (
		nodes       = []*TeamAPIKey{}
		_spec       = takq.querySpec()
		loadedTypes = [2]bool{
			takq.withTeam != nil,
			takq.withCreator != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*TeamAPIKey).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &TeamAPIKey{config: takq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	_spec.Node.Schema = takq.schemaConfig.TeamAPIKey
	ctx = internal.NewSchemaConfigContext(ctx, takq.schemaConfig)
	if len(takq.modifiers) > 0 {
		_spec.Modifiers = takq.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, takq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := takq.withTeam; query != nil {
		if err := takq.loadTeam(ctx, query, nodes, nil,
			func(n *TeamAPIKey, e *Team) { n.Edges.Team = e }); err != nil {
			return nil, err
		}
	}
	if query := takq.withCreator; query != nil {
		if err := takq.loadCreator(ctx, query, nodes, nil,
			func(n *TeamAPIKey, e *User) { n.Edges.Creator = e }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (takq *TeamAPIKeyQuery) loadTeam(ctx context.Context, query *TeamQuery, nodes []*TeamAPIKey, init func(*TeamAPIKey), assign func(*TeamAPIKey, *Team)) error {
	ids := make([]uuid.UUID, 0, len(nodes))
	nodeids := make(map[uuid.UUID][]*TeamAPIKey)
	for i := range nodes {
		fk := nodes[i].TeamID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(team.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "team_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (takq *TeamAPIKeyQuery) loadCreator(ctx context.Context, query *UserQuery, nodes []*TeamAPIKey, init func(*TeamAPIKey), assign func(*TeamAPIKey, *User)) error {
	ids := make([]uuid.UUID, 0, len(nodes))
	nodeids := make(map[uuid.UUID][]*TeamAPIKey)
	for i := range nodes {
		if nodes[i].CreatedBy == nil {
			continue
		}
		fk := *nodes[i].CreatedBy
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(user.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "created_by" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}

func (takq *TeamAPIKeyQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := takq.querySpec()
	_spec.Node.Schema = takq.schemaConfig.TeamAPIKey
	ctx = internal.NewSchemaConfigContext(ctx, takq.schemaConfig)
	if len(takq.modifiers) > 0 {
		_spec.Modifiers = takq.modifiers
	}
	_spec.Node.Columns = takq.ctx.Fields
	if len(takq.ctx.Fields) > 0 {
		_spec.Unique = takq.ctx.Unique != nil && *takq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, takq.driver, _spec)
}

func (takq *TeamAPIKeyQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(teamapikey.Table, teamapikey.Columns, sqlgraph.NewFieldSpec(teamapikey.FieldID, field.TypeUUID))
	_spec.From = takq.sql
	if unique := takq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if takq.path != nil {
		_spec.Unique = true
	}
	if fields := takq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, teamapikey.FieldID)
		for i := range fields {
			if fields[i] != teamapikey.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if takq.withTeam != nil {
			_spec.Node.AddColumnOnce(teamapikey.FieldTeamID)
		}
		if takq.withCreator != nil {
			_spec.Node.AddColumnOnce(teamapikey.FieldCreatedBy)
		}
	}
	if ps := takq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := takq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := takq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := takq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (takq *TeamAPIKeyQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(takq.driver.Dialect())
	t1 := builder.Table(teamapikey.Table)
	columns := takq.ctx.Fields
	if len(columns) == 0 {
		columns = teamapikey.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if takq.sql != nil {
		selector = takq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if takq.ctx.Unique != nil && *takq.ctx.Unique {
		selector.Distinct()
	}
	t1.Schema(takq.schemaConfig.TeamAPIKey)
	ctx = internal.NewSchemaConfigContext(ctx, takq.schemaConfig)
	selector.WithContext(ctx)
	for _, m := range takq.modifiers {
		m(selector)
	}
	for _, p := range takq.predicates {
		p(selector)
	}
	for _, p := range takq.order {
		p(selector)
	}
	if offset := takq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := takq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// Modify adds a query modifier for attaching custom logic to queries.
func (takq *TeamAPIKeyQuery) Modify(modifiers ...func(s *sql.Selector)) *TeamAPIKeySelect {
	takq.modifiers = append(takq.modifiers, modifiers...)
	return takq.Select()
}

// TeamAPIKeyGroupBy is the group-by builder for TeamAPIKey entities.
type TeamAPIKeyGroupBy struct {
	selector
	build *TeamAPIKeyQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (takgb *TeamAPIKeyGroupBy) Aggregate(fns ...AggregateFunc) *TeamAPIKeyGroupBy {
	takgb.fns = append(takgb.fns, fns...)
	return takgb
}

// Scan applies the selector query and scans the result into the given value.
func (takgb *TeamAPIKeyGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, takgb.build.ctx, "GroupBy")
	if err := takgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*TeamAPIKeyQuery, *TeamAPIKeyGroupBy](ctx, takgb.build, takgb, takgb.build.inters, v)
}

func (takgb *TeamAPIKeyGroupBy) sqlScan(ctx context.Context, root *TeamAPIKeyQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(takgb.fns))
	for _, fn := range takgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*takgb.flds)+len(takgb.fns))
		for _, f := range *takgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*takgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := takgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// TeamAPIKeySelect is the builder for selecting fields of TeamAPIKey entities.
type TeamAPIKeySelect struct {
	*TeamAPIKeyQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (taks *TeamAPIKeySelect) Aggregate(fns ...AggregateFunc) *TeamAPIKeySelect {
	taks.fns = append(taks.fns, fns...)
	return taks
}

// Scan applies the selector query and scans the result into the given value.
func (taks *TeamAPIKeySelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, taks.ctx, "Select")
	if err := taks.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*TeamAPIKeyQuery, *TeamAPIKeySelect](ctx, taks.TeamAPIKeyQuery, taks, taks.inters, v)
}

func (taks *TeamAPIKeySelect) sqlScan(ctx context.Context, root *TeamAPIKeyQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(taks.fns))
	for _, fn := range taks.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*taks.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := taks.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (taks *TeamAPIKeySelect) Modify(modifiers ...func(s *sql.Selector)) *TeamAPIKeySelect {
	taks.modifiers = append(taks.modifiers, modifiers...)
	return taks
}
