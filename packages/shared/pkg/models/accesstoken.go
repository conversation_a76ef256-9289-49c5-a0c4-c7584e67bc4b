// Code generated by ent, DO NOT EDIT.

package models

import (
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/accesstoken"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/user"
	"github.com/google/uuid"
)

// AccessToken is the model entity for the AccessToken schema.
type AccessToken struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// AccessToken holds the value of the "access_token" field.
	AccessToken string `json:"-"`
	// AccessTokenHash holds the value of the "access_token_hash" field.
	AccessTokenHash string `json:"-"`
	// AccessTokenPrefix holds the value of the "access_token_prefix" field.
	AccessTokenPrefix string `json:"access_token_prefix,omitempty"`
	// AccessTokenLength holds the value of the "access_token_length" field.
	AccessTokenLength int `json:"access_token_length,omitempty"`
	// AccessTokenMaskPrefix holds the value of the "access_token_mask_prefix" field.
	AccessTokenMaskPrefix string `json:"access_token_mask_prefix,omitempty"`
	// AccessTokenMaskSuffix holds the value of the "access_token_mask_suffix" field.
	AccessTokenMaskSuffix string `json:"access_token_mask_suffix,omitempty"`
	// Name holds the value of the "name" field.
	Name string `json:"name,omitempty"`
	// UserID holds the value of the "user_id" field.
	UserID uuid.UUID `json:"user_id,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the AccessTokenQuery when eager-loading is set.
	Edges        AccessTokenEdges `json:"edges"`
	selectValues sql.SelectValues
}

// AccessTokenEdges holds the relations/edges for other nodes in the graph.
type AccessTokenEdges struct {
	// User holds the value of the user edge.
	User *User `json:"user,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [1]bool
}

// UserOrErr returns the User value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e AccessTokenEdges) UserOrErr() (*User, error) {
	if e.loadedTypes[0] {
		if e.User == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: user.Label}
		}
		return e.User, nil
	}
	return nil, &NotLoadedError{edge: "user"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*AccessToken) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case accesstoken.FieldAccessTokenLength:
			values[i] = new(sql.NullInt64)
		case accesstoken.FieldAccessToken, accesstoken.FieldAccessTokenHash, accesstoken.FieldAccessTokenPrefix, accesstoken.FieldAccessTokenMaskPrefix, accesstoken.FieldAccessTokenMaskSuffix, accesstoken.FieldName:
			values[i] = new(sql.NullString)
		case accesstoken.FieldCreatedAt:
			values[i] = new(sql.NullTime)
		case accesstoken.FieldID, accesstoken.FieldUserID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the AccessToken fields.
func (at *AccessToken) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case accesstoken.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				at.ID = *value
			}
		case accesstoken.FieldAccessToken:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field access_token", values[i])
			} else if value.Valid {
				at.AccessToken = value.String
			}
		case accesstoken.FieldAccessTokenHash:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field access_token_hash", values[i])
			} else if value.Valid {
				at.AccessTokenHash = value.String
			}
		case accesstoken.FieldAccessTokenPrefix:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field access_token_prefix", values[i])
			} else if value.Valid {
				at.AccessTokenPrefix = value.String
			}
		case accesstoken.FieldAccessTokenLength:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field access_token_length", values[i])
			} else if value.Valid {
				at.AccessTokenLength = int(value.Int64)
			}
		case accesstoken.FieldAccessTokenMaskPrefix:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field access_token_mask_prefix", values[i])
			} else if value.Valid {
				at.AccessTokenMaskPrefix = value.String
			}
		case accesstoken.FieldAccessTokenMaskSuffix:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field access_token_mask_suffix", values[i])
			} else if value.Valid {
				at.AccessTokenMaskSuffix = value.String
			}
		case accesstoken.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				at.Name = value.String
			}
		case accesstoken.FieldUserID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value != nil {
				at.UserID = *value
			}
		case accesstoken.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				at.CreatedAt = value.Time
			}
		default:
			at.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the AccessToken.
// This includes values selected through modifiers, order, etc.
func (at *AccessToken) Value(name string) (ent.Value, error) {
	return at.selectValues.Get(name)
}

// QueryUser queries the "user" edge of the AccessToken entity.
func (at *AccessToken) QueryUser() *UserQuery {
	return NewAccessTokenClient(at.config).QueryUser(at)
}

// Update returns a builder for updating this AccessToken.
// Note that you need to call AccessToken.Unwrap() before calling this method if this AccessToken
// was returned from a transaction, and the transaction was committed or rolled back.
func (at *AccessToken) Update() *AccessTokenUpdateOne {
	return NewAccessTokenClient(at.config).UpdateOne(at)
}

// Unwrap unwraps the AccessToken entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (at *AccessToken) Unwrap() *AccessToken {
	_tx, ok := at.config.driver.(*txDriver)
	if !ok {
		panic("models: AccessToken is not a transactional entity")
	}
	at.config.driver = _tx.drv
	return at
}

// String implements the fmt.Stringer.
func (at *AccessToken) String() string {
	var builder strings.Builder
	builder.WriteString("AccessToken(")
	builder.WriteString(fmt.Sprintf("id=%v, ", at.ID))
	builder.WriteString("access_token=<sensitive>")
	builder.WriteString(", ")
	builder.WriteString("access_token_hash=<sensitive>")
	builder.WriteString(", ")
	builder.WriteString("access_token_prefix=")
	builder.WriteString(at.AccessTokenPrefix)
	builder.WriteString(", ")
	builder.WriteString("access_token_length=")
	builder.WriteString(fmt.Sprintf("%v", at.AccessTokenLength))
	builder.WriteString(", ")
	builder.WriteString("access_token_mask_prefix=")
	builder.WriteString(at.AccessTokenMaskPrefix)
	builder.WriteString(", ")
	builder.WriteString("access_token_mask_suffix=")
	builder.WriteString(at.AccessTokenMaskSuffix)
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(at.Name)
	builder.WriteString(", ")
	builder.WriteString("user_id=")
	builder.WriteString(fmt.Sprintf("%v", at.UserID))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(at.CreatedAt.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// AccessTokens is a parsable slice of AccessToken.
type AccessTokens []*AccessToken
