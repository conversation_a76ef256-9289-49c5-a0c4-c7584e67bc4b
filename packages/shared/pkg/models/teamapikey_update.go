// Code generated by ent, DO NOT EDIT.

package models

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/internal"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/predicate"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/team"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/teamapikey"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/user"
	"github.com/google/uuid"
)

// TeamAPIKeyUpdate is the builder for updating TeamAPIKey entities.
type TeamAPIKeyUpdate struct {
	config
	hooks     []Hook
	mutation  *TeamAPIKeyMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the TeamAPIKeyUpdate builder.
func (taku *TeamAPIKeyUpdate) Where(ps ...predicate.TeamAPIKey) *TeamAPIKeyUpdate {
	taku.mutation.Where(ps...)
	return taku
}

// SetAPIKey sets the "api_key" field.
func (taku *TeamAPIKeyUpdate) SetAPIKey(s string) *TeamAPIKeyUpdate {
	taku.mutation.SetAPIKey(s)
	return taku
}

// SetNillableAPIKey sets the "api_key" field if the given value is not nil.
func (taku *TeamAPIKeyUpdate) SetNillableAPIKey(s *string) *TeamAPIKeyUpdate {
	if s != nil {
		taku.SetAPIKey(*s)
	}
	return taku
}

// SetAPIKeyHash sets the "api_key_hash" field.
func (taku *TeamAPIKeyUpdate) SetAPIKeyHash(s string) *TeamAPIKeyUpdate {
	taku.mutation.SetAPIKeyHash(s)
	return taku
}

// SetNillableAPIKeyHash sets the "api_key_hash" field if the given value is not nil.
func (taku *TeamAPIKeyUpdate) SetNillableAPIKeyHash(s *string) *TeamAPIKeyUpdate {
	if s != nil {
		taku.SetAPIKeyHash(*s)
	}
	return taku
}

// SetUpdatedAt sets the "updated_at" field.
func (taku *TeamAPIKeyUpdate) SetUpdatedAt(t time.Time) *TeamAPIKeyUpdate {
	taku.mutation.SetUpdatedAt(t)
	return taku
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (taku *TeamAPIKeyUpdate) SetNillableUpdatedAt(t *time.Time) *TeamAPIKeyUpdate {
	if t != nil {
		taku.SetUpdatedAt(*t)
	}
	return taku
}

// ClearUpdatedAt clears the value of the "updated_at" field.
func (taku *TeamAPIKeyUpdate) ClearUpdatedAt() *TeamAPIKeyUpdate {
	taku.mutation.ClearUpdatedAt()
	return taku
}

// SetTeamID sets the "team_id" field.
func (taku *TeamAPIKeyUpdate) SetTeamID(u uuid.UUID) *TeamAPIKeyUpdate {
	taku.mutation.SetTeamID(u)
	return taku
}

// SetNillableTeamID sets the "team_id" field if the given value is not nil.
func (taku *TeamAPIKeyUpdate) SetNillableTeamID(u *uuid.UUID) *TeamAPIKeyUpdate {
	if u != nil {
		taku.SetTeamID(*u)
	}
	return taku
}

// SetName sets the "name" field.
func (taku *TeamAPIKeyUpdate) SetName(s string) *TeamAPIKeyUpdate {
	taku.mutation.SetName(s)
	return taku
}

// SetNillableName sets the "name" field if the given value is not nil.
func (taku *TeamAPIKeyUpdate) SetNillableName(s *string) *TeamAPIKeyUpdate {
	if s != nil {
		taku.SetName(*s)
	}
	return taku
}

// SetCreatedBy sets the "created_by" field.
func (taku *TeamAPIKeyUpdate) SetCreatedBy(u uuid.UUID) *TeamAPIKeyUpdate {
	taku.mutation.SetCreatedBy(u)
	return taku
}

// SetNillableCreatedBy sets the "created_by" field if the given value is not nil.
func (taku *TeamAPIKeyUpdate) SetNillableCreatedBy(u *uuid.UUID) *TeamAPIKeyUpdate {
	if u != nil {
		taku.SetCreatedBy(*u)
	}
	return taku
}

// ClearCreatedBy clears the value of the "created_by" field.
func (taku *TeamAPIKeyUpdate) ClearCreatedBy() *TeamAPIKeyUpdate {
	taku.mutation.ClearCreatedBy()
	return taku
}

// SetLastUsed sets the "last_used" field.
func (taku *TeamAPIKeyUpdate) SetLastUsed(t time.Time) *TeamAPIKeyUpdate {
	taku.mutation.SetLastUsed(t)
	return taku
}

// SetNillableLastUsed sets the "last_used" field if the given value is not nil.
func (taku *TeamAPIKeyUpdate) SetNillableLastUsed(t *time.Time) *TeamAPIKeyUpdate {
	if t != nil {
		taku.SetLastUsed(*t)
	}
	return taku
}

// ClearLastUsed clears the value of the "last_used" field.
func (taku *TeamAPIKeyUpdate) ClearLastUsed() *TeamAPIKeyUpdate {
	taku.mutation.ClearLastUsed()
	return taku
}

// SetTeam sets the "team" edge to the Team entity.
func (taku *TeamAPIKeyUpdate) SetTeam(t *Team) *TeamAPIKeyUpdate {
	return taku.SetTeamID(t.ID)
}

// SetCreatorID sets the "creator" edge to the User entity by ID.
func (taku *TeamAPIKeyUpdate) SetCreatorID(id uuid.UUID) *TeamAPIKeyUpdate {
	taku.mutation.SetCreatorID(id)
	return taku
}

// SetNillableCreatorID sets the "creator" edge to the User entity by ID if the given value is not nil.
func (taku *TeamAPIKeyUpdate) SetNillableCreatorID(id *uuid.UUID) *TeamAPIKeyUpdate {
	if id != nil {
		taku = taku.SetCreatorID(*id)
	}
	return taku
}

// SetCreator sets the "creator" edge to the User entity.
func (taku *TeamAPIKeyUpdate) SetCreator(u *User) *TeamAPIKeyUpdate {
	return taku.SetCreatorID(u.ID)
}

// Mutation returns the TeamAPIKeyMutation object of the builder.
func (taku *TeamAPIKeyUpdate) Mutation() *TeamAPIKeyMutation {
	return taku.mutation
}

// ClearTeam clears the "team" edge to the Team entity.
func (taku *TeamAPIKeyUpdate) ClearTeam() *TeamAPIKeyUpdate {
	taku.mutation.ClearTeam()
	return taku
}

// ClearCreator clears the "creator" edge to the User entity.
func (taku *TeamAPIKeyUpdate) ClearCreator() *TeamAPIKeyUpdate {
	taku.mutation.ClearCreator()
	return taku
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (taku *TeamAPIKeyUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, taku.sqlSave, taku.mutation, taku.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (taku *TeamAPIKeyUpdate) SaveX(ctx context.Context) int {
	affected, err := taku.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (taku *TeamAPIKeyUpdate) Exec(ctx context.Context) error {
	_, err := taku.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (taku *TeamAPIKeyUpdate) ExecX(ctx context.Context) {
	if err := taku.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (taku *TeamAPIKeyUpdate) check() error {
	if _, ok := taku.mutation.TeamID(); taku.mutation.TeamCleared() && !ok {
		return errors.New(`models: clearing a required unique edge "TeamAPIKey.team"`)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (taku *TeamAPIKeyUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *TeamAPIKeyUpdate {
	taku.modifiers = append(taku.modifiers, modifiers...)
	return taku
}

func (taku *TeamAPIKeyUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := taku.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(teamapikey.Table, teamapikey.Columns, sqlgraph.NewFieldSpec(teamapikey.FieldID, field.TypeUUID))
	if ps := taku.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := taku.mutation.APIKey(); ok {
		_spec.SetField(teamapikey.FieldAPIKey, field.TypeString, value)
	}
	if value, ok := taku.mutation.APIKeyHash(); ok {
		_spec.SetField(teamapikey.FieldAPIKeyHash, field.TypeString, value)
	}
	if value, ok := taku.mutation.UpdatedAt(); ok {
		_spec.SetField(teamapikey.FieldUpdatedAt, field.TypeTime, value)
	}
	if taku.mutation.UpdatedAtCleared() {
		_spec.ClearField(teamapikey.FieldUpdatedAt, field.TypeTime)
	}
	if value, ok := taku.mutation.Name(); ok {
		_spec.SetField(teamapikey.FieldName, field.TypeString, value)
	}
	if value, ok := taku.mutation.LastUsed(); ok {
		_spec.SetField(teamapikey.FieldLastUsed, field.TypeTime, value)
	}
	if taku.mutation.LastUsedCleared() {
		_spec.ClearField(teamapikey.FieldLastUsed, field.TypeTime)
	}
	if taku.mutation.TeamCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   teamapikey.TeamTable,
			Columns: []string{teamapikey.TeamColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(team.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = taku.schemaConfig.TeamAPIKey
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := taku.mutation.TeamIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   teamapikey.TeamTable,
			Columns: []string{teamapikey.TeamColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(team.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = taku.schemaConfig.TeamAPIKey
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if taku.mutation.CreatorCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   teamapikey.CreatorTable,
			Columns: []string{teamapikey.CreatorColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = taku.schemaConfig.TeamAPIKey
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := taku.mutation.CreatorIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   teamapikey.CreatorTable,
			Columns: []string{teamapikey.CreatorColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = taku.schemaConfig.TeamAPIKey
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.Node.Schema = taku.schemaConfig.TeamAPIKey
	ctx = internal.NewSchemaConfigContext(ctx, taku.schemaConfig)
	_spec.AddModifiers(taku.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, taku.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{teamapikey.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	taku.mutation.done = true
	return n, nil
}

// TeamAPIKeyUpdateOne is the builder for updating a single TeamAPIKey entity.
type TeamAPIKeyUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *TeamAPIKeyMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetAPIKey sets the "api_key" field.
func (takuo *TeamAPIKeyUpdateOne) SetAPIKey(s string) *TeamAPIKeyUpdateOne {
	takuo.mutation.SetAPIKey(s)
	return takuo
}

// SetNillableAPIKey sets the "api_key" field if the given value is not nil.
func (takuo *TeamAPIKeyUpdateOne) SetNillableAPIKey(s *string) *TeamAPIKeyUpdateOne {
	if s != nil {
		takuo.SetAPIKey(*s)
	}
	return takuo
}

// SetAPIKeyHash sets the "api_key_hash" field.
func (takuo *TeamAPIKeyUpdateOne) SetAPIKeyHash(s string) *TeamAPIKeyUpdateOne {
	takuo.mutation.SetAPIKeyHash(s)
	return takuo
}

// SetNillableAPIKeyHash sets the "api_key_hash" field if the given value is not nil.
func (takuo *TeamAPIKeyUpdateOne) SetNillableAPIKeyHash(s *string) *TeamAPIKeyUpdateOne {
	if s != nil {
		takuo.SetAPIKeyHash(*s)
	}
	return takuo
}

// SetUpdatedAt sets the "updated_at" field.
func (takuo *TeamAPIKeyUpdateOne) SetUpdatedAt(t time.Time) *TeamAPIKeyUpdateOne {
	takuo.mutation.SetUpdatedAt(t)
	return takuo
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (takuo *TeamAPIKeyUpdateOne) SetNillableUpdatedAt(t *time.Time) *TeamAPIKeyUpdateOne {
	if t != nil {
		takuo.SetUpdatedAt(*t)
	}
	return takuo
}

// ClearUpdatedAt clears the value of the "updated_at" field.
func (takuo *TeamAPIKeyUpdateOne) ClearUpdatedAt() *TeamAPIKeyUpdateOne {
	takuo.mutation.ClearUpdatedAt()
	return takuo
}

// SetTeamID sets the "team_id" field.
func (takuo *TeamAPIKeyUpdateOne) SetTeamID(u uuid.UUID) *TeamAPIKeyUpdateOne {
	takuo.mutation.SetTeamID(u)
	return takuo
}

// SetNillableTeamID sets the "team_id" field if the given value is not nil.
func (takuo *TeamAPIKeyUpdateOne) SetNillableTeamID(u *uuid.UUID) *TeamAPIKeyUpdateOne {
	if u != nil {
		takuo.SetTeamID(*u)
	}
	return takuo
}

// SetName sets the "name" field.
func (takuo *TeamAPIKeyUpdateOne) SetName(s string) *TeamAPIKeyUpdateOne {
	takuo.mutation.SetName(s)
	return takuo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (takuo *TeamAPIKeyUpdateOne) SetNillableName(s *string) *TeamAPIKeyUpdateOne {
	if s != nil {
		takuo.SetName(*s)
	}
	return takuo
}

// SetCreatedBy sets the "created_by" field.
func (takuo *TeamAPIKeyUpdateOne) SetCreatedBy(u uuid.UUID) *TeamAPIKeyUpdateOne {
	takuo.mutation.SetCreatedBy(u)
	return takuo
}

// SetNillableCreatedBy sets the "created_by" field if the given value is not nil.
func (takuo *TeamAPIKeyUpdateOne) SetNillableCreatedBy(u *uuid.UUID) *TeamAPIKeyUpdateOne {
	if u != nil {
		takuo.SetCreatedBy(*u)
	}
	return takuo
}

// ClearCreatedBy clears the value of the "created_by" field.
func (takuo *TeamAPIKeyUpdateOne) ClearCreatedBy() *TeamAPIKeyUpdateOne {
	takuo.mutation.ClearCreatedBy()
	return takuo
}

// SetLastUsed sets the "last_used" field.
func (takuo *TeamAPIKeyUpdateOne) SetLastUsed(t time.Time) *TeamAPIKeyUpdateOne {
	takuo.mutation.SetLastUsed(t)
	return takuo
}

// SetNillableLastUsed sets the "last_used" field if the given value is not nil.
func (takuo *TeamAPIKeyUpdateOne) SetNillableLastUsed(t *time.Time) *TeamAPIKeyUpdateOne {
	if t != nil {
		takuo.SetLastUsed(*t)
	}
	return takuo
}

// ClearLastUsed clears the value of the "last_used" field.
func (takuo *TeamAPIKeyUpdateOne) ClearLastUsed() *TeamAPIKeyUpdateOne {
	takuo.mutation.ClearLastUsed()
	return takuo
}

// SetTeam sets the "team" edge to the Team entity.
func (takuo *TeamAPIKeyUpdateOne) SetTeam(t *Team) *TeamAPIKeyUpdateOne {
	return takuo.SetTeamID(t.ID)
}

// SetCreatorID sets the "creator" edge to the User entity by ID.
func (takuo *TeamAPIKeyUpdateOne) SetCreatorID(id uuid.UUID) *TeamAPIKeyUpdateOne {
	takuo.mutation.SetCreatorID(id)
	return takuo
}

// SetNillableCreatorID sets the "creator" edge to the User entity by ID if the given value is not nil.
func (takuo *TeamAPIKeyUpdateOne) SetNillableCreatorID(id *uuid.UUID) *TeamAPIKeyUpdateOne {
	if id != nil {
		takuo = takuo.SetCreatorID(*id)
	}
	return takuo
}

// SetCreator sets the "creator" edge to the User entity.
func (takuo *TeamAPIKeyUpdateOne) SetCreator(u *User) *TeamAPIKeyUpdateOne {
	return takuo.SetCreatorID(u.ID)
}

// Mutation returns the TeamAPIKeyMutation object of the builder.
func (takuo *TeamAPIKeyUpdateOne) Mutation() *TeamAPIKeyMutation {
	return takuo.mutation
}

// ClearTeam clears the "team" edge to the Team entity.
func (takuo *TeamAPIKeyUpdateOne) ClearTeam() *TeamAPIKeyUpdateOne {
	takuo.mutation.ClearTeam()
	return takuo
}

// ClearCreator clears the "creator" edge to the User entity.
func (takuo *TeamAPIKeyUpdateOne) ClearCreator() *TeamAPIKeyUpdateOne {
	takuo.mutation.ClearCreator()
	return takuo
}

// Where appends a list predicates to the TeamAPIKeyUpdate builder.
func (takuo *TeamAPIKeyUpdateOne) Where(ps ...predicate.TeamAPIKey) *TeamAPIKeyUpdateOne {
	takuo.mutation.Where(ps...)
	return takuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (takuo *TeamAPIKeyUpdateOne) Select(field string, fields ...string) *TeamAPIKeyUpdateOne {
	takuo.fields = append([]string{field}, fields...)
	return takuo
}

// Save executes the query and returns the updated TeamAPIKey entity.
func (takuo *TeamAPIKeyUpdateOne) Save(ctx context.Context) (*TeamAPIKey, error) {
	return withHooks(ctx, takuo.sqlSave, takuo.mutation, takuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (takuo *TeamAPIKeyUpdateOne) SaveX(ctx context.Context) *TeamAPIKey {
	node, err := takuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (takuo *TeamAPIKeyUpdateOne) Exec(ctx context.Context) error {
	_, err := takuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (takuo *TeamAPIKeyUpdateOne) ExecX(ctx context.Context) {
	if err := takuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (takuo *TeamAPIKeyUpdateOne) check() error {
	if _, ok := takuo.mutation.TeamID(); takuo.mutation.TeamCleared() && !ok {
		return errors.New(`models: clearing a required unique edge "TeamAPIKey.team"`)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (takuo *TeamAPIKeyUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *TeamAPIKeyUpdateOne {
	takuo.modifiers = append(takuo.modifiers, modifiers...)
	return takuo
}

func (takuo *TeamAPIKeyUpdateOne) sqlSave(ctx context.Context) (_node *TeamAPIKey, err error) {
	if err := takuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(teamapikey.Table, teamapikey.Columns, sqlgraph.NewFieldSpec(teamapikey.FieldID, field.TypeUUID))
	id, ok := takuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`models: missing "TeamAPIKey.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := takuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, teamapikey.FieldID)
		for _, f := range fields {
			if !teamapikey.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("models: invalid field %q for query", f)}
			}
			if f != teamapikey.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := takuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := takuo.mutation.APIKey(); ok {
		_spec.SetField(teamapikey.FieldAPIKey, field.TypeString, value)
	}
	if value, ok := takuo.mutation.APIKeyHash(); ok {
		_spec.SetField(teamapikey.FieldAPIKeyHash, field.TypeString, value)
	}
	if value, ok := takuo.mutation.UpdatedAt(); ok {
		_spec.SetField(teamapikey.FieldUpdatedAt, field.TypeTime, value)
	}
	if takuo.mutation.UpdatedAtCleared() {
		_spec.ClearField(teamapikey.FieldUpdatedAt, field.TypeTime)
	}
	if value, ok := takuo.mutation.Name(); ok {
		_spec.SetField(teamapikey.FieldName, field.TypeString, value)
	}
	if value, ok := takuo.mutation.LastUsed(); ok {
		_spec.SetField(teamapikey.FieldLastUsed, field.TypeTime, value)
	}
	if takuo.mutation.LastUsedCleared() {
		_spec.ClearField(teamapikey.FieldLastUsed, field.TypeTime)
	}
	if takuo.mutation.TeamCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   teamapikey.TeamTable,
			Columns: []string{teamapikey.TeamColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(team.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = takuo.schemaConfig.TeamAPIKey
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := takuo.mutation.TeamIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   teamapikey.TeamTable,
			Columns: []string{teamapikey.TeamColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(team.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = takuo.schemaConfig.TeamAPIKey
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if takuo.mutation.CreatorCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   teamapikey.CreatorTable,
			Columns: []string{teamapikey.CreatorColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = takuo.schemaConfig.TeamAPIKey
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := takuo.mutation.CreatorIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   teamapikey.CreatorTable,
			Columns: []string{teamapikey.CreatorColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = takuo.schemaConfig.TeamAPIKey
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.Node.Schema = takuo.schemaConfig.TeamAPIKey
	ctx = internal.NewSchemaConfigContext(ctx, takuo.schemaConfig)
	_spec.AddModifiers(takuo.modifiers...)
	_node = &TeamAPIKey{config: takuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, takuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{teamapikey.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	takuo.mutation.done = true
	return _node, nil
}
