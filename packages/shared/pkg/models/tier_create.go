// Code generated by ent, DO NOT EDIT.

package models

import (
	"context"
	"errors"
	"fmt"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/team"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/tier"
	"github.com/google/uuid"
)

// TierCreate is the builder for creating a Tier entity.
type TierCreate struct {
	config
	mutation *TierMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetName sets the "name" field.
func (tc *TierCreate) SetName(s string) *TierCreate {
	tc.mutation.SetName(s)
	return tc
}

// SetDiskMB sets the "disk_mb" field.
func (tc *TierCreate) SetDiskMB(i int64) *TierCreate {
	tc.mutation.SetDiskMB(i)
	return tc
}

// SetConcurrentInstances sets the "concurrent_instances" field.
func (tc *TierCreate) SetConcurrentInstances(i int64) *TierCreate {
	tc.mutation.SetConcurrentInstances(i)
	return tc
}

// SetMaxLengthHours sets the "max_length_hours" field.
func (tc *TierCreate) SetMaxLengthHours(i int64) *TierCreate {
	tc.mutation.SetMaxLengthHours(i)
	return tc
}

// SetID sets the "id" field.
func (tc *TierCreate) SetID(s string) *TierCreate {
	tc.mutation.SetID(s)
	return tc
}

// AddTeamIDs adds the "teams" edge to the Team entity by IDs.
func (tc *TierCreate) AddTeamIDs(ids ...uuid.UUID) *TierCreate {
	tc.mutation.AddTeamIDs(ids...)
	return tc
}

// AddTeams adds the "teams" edges to the Team entity.
func (tc *TierCreate) AddTeams(t ...*Team) *TierCreate {
	ids := make([]uuid.UUID, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return tc.AddTeamIDs(ids...)
}

// Mutation returns the TierMutation object of the builder.
func (tc *TierCreate) Mutation() *TierMutation {
	return tc.mutation
}

// Save creates the Tier in the database.
func (tc *TierCreate) Save(ctx context.Context) (*Tier, error) {
	return withHooks(ctx, tc.sqlSave, tc.mutation, tc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (tc *TierCreate) SaveX(ctx context.Context) *Tier {
	v, err := tc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (tc *TierCreate) Exec(ctx context.Context) error {
	_, err := tc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tc *TierCreate) ExecX(ctx context.Context) {
	if err := tc.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (tc *TierCreate) check() error {
	if _, ok := tc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`models: missing required field "Tier.name"`)}
	}
	if _, ok := tc.mutation.DiskMB(); !ok {
		return &ValidationError{Name: "disk_mb", err: errors.New(`models: missing required field "Tier.disk_mb"`)}
	}
	if _, ok := tc.mutation.ConcurrentInstances(); !ok {
		return &ValidationError{Name: "concurrent_instances", err: errors.New(`models: missing required field "Tier.concurrent_instances"`)}
	}
	if _, ok := tc.mutation.MaxLengthHours(); !ok {
		return &ValidationError{Name: "max_length_hours", err: errors.New(`models: missing required field "Tier.max_length_hours"`)}
	}
	return nil
}

func (tc *TierCreate) sqlSave(ctx context.Context) (*Tier, error) {
	if err := tc.check(); err != nil {
		return nil, err
	}
	_node, _spec := tc.createSpec()
	if err := sqlgraph.CreateNode(ctx, tc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(string); ok {
			_node.ID = id
		} else {
			return nil, fmt.Errorf("unexpected Tier.ID type: %T", _spec.ID.Value)
		}
	}
	tc.mutation.id = &_node.ID
	tc.mutation.done = true
	return _node, nil
}

func (tc *TierCreate) createSpec() (*Tier, *sqlgraph.CreateSpec) {
	var (
		_node = &Tier{config: tc.config}
		_spec = sqlgraph.NewCreateSpec(tier.Table, sqlgraph.NewFieldSpec(tier.FieldID, field.TypeString))
	)
	_spec.Schema = tc.schemaConfig.Tier
	_spec.OnConflict = tc.conflict
	if id, ok := tc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := tc.mutation.Name(); ok {
		_spec.SetField(tier.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := tc.mutation.DiskMB(); ok {
		_spec.SetField(tier.FieldDiskMB, field.TypeInt64, value)
		_node.DiskMB = value
	}
	if value, ok := tc.mutation.ConcurrentInstances(); ok {
		_spec.SetField(tier.FieldConcurrentInstances, field.TypeInt64, value)
		_node.ConcurrentInstances = value
	}
	if value, ok := tc.mutation.MaxLengthHours(); ok {
		_spec.SetField(tier.FieldMaxLengthHours, field.TypeInt64, value)
		_node.MaxLengthHours = value
	}
	if nodes := tc.mutation.TeamsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   tier.TeamsTable,
			Columns: []string{tier.TeamsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(team.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = tc.schemaConfig.Team
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Tier.Create().
//		SetName(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.TierUpsert) {
//			SetName(v+v).
//		}).
//		Exec(ctx)
func (tc *TierCreate) OnConflict(opts ...sql.ConflictOption) *TierUpsertOne {
	tc.conflict = opts
	return &TierUpsertOne{
		create: tc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Tier.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (tc *TierCreate) OnConflictColumns(columns ...string) *TierUpsertOne {
	tc.conflict = append(tc.conflict, sql.ConflictColumns(columns...))
	return &TierUpsertOne{
		create: tc,
	}
}

type (
	// TierUpsertOne is the builder for "upsert"-ing
	//  one Tier node.
	TierUpsertOne struct {
		create *TierCreate
	}

	// TierUpsert is the "OnConflict" setter.
	TierUpsert struct {
		*sql.UpdateSet
	}
)

// SetName sets the "name" field.
func (u *TierUpsert) SetName(v string) *TierUpsert {
	u.Set(tier.FieldName, v)
	return u
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *TierUpsert) UpdateName() *TierUpsert {
	u.SetExcluded(tier.FieldName)
	return u
}

// SetDiskMB sets the "disk_mb" field.
func (u *TierUpsert) SetDiskMB(v int64) *TierUpsert {
	u.Set(tier.FieldDiskMB, v)
	return u
}

// UpdateDiskMB sets the "disk_mb" field to the value that was provided on create.
func (u *TierUpsert) UpdateDiskMB() *TierUpsert {
	u.SetExcluded(tier.FieldDiskMB)
	return u
}

// AddDiskMB adds v to the "disk_mb" field.
func (u *TierUpsert) AddDiskMB(v int64) *TierUpsert {
	u.Add(tier.FieldDiskMB, v)
	return u
}

// SetConcurrentInstances sets the "concurrent_instances" field.
func (u *TierUpsert) SetConcurrentInstances(v int64) *TierUpsert {
	u.Set(tier.FieldConcurrentInstances, v)
	return u
}

// UpdateConcurrentInstances sets the "concurrent_instances" field to the value that was provided on create.
func (u *TierUpsert) UpdateConcurrentInstances() *TierUpsert {
	u.SetExcluded(tier.FieldConcurrentInstances)
	return u
}

// AddConcurrentInstances adds v to the "concurrent_instances" field.
func (u *TierUpsert) AddConcurrentInstances(v int64) *TierUpsert {
	u.Add(tier.FieldConcurrentInstances, v)
	return u
}

// SetMaxLengthHours sets the "max_length_hours" field.
func (u *TierUpsert) SetMaxLengthHours(v int64) *TierUpsert {
	u.Set(tier.FieldMaxLengthHours, v)
	return u
}

// UpdateMaxLengthHours sets the "max_length_hours" field to the value that was provided on create.
func (u *TierUpsert) UpdateMaxLengthHours() *TierUpsert {
	u.SetExcluded(tier.FieldMaxLengthHours)
	return u
}

// AddMaxLengthHours adds v to the "max_length_hours" field.
func (u *TierUpsert) AddMaxLengthHours(v int64) *TierUpsert {
	u.Add(tier.FieldMaxLengthHours, v)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.Tier.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(tier.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *TierUpsertOne) UpdateNewValues() *TierUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(tier.FieldID)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Tier.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *TierUpsertOne) Ignore() *TierUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *TierUpsertOne) DoNothing() *TierUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the TierCreate.OnConflict
// documentation for more info.
func (u *TierUpsertOne) Update(set func(*TierUpsert)) *TierUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&TierUpsert{UpdateSet: update})
	}))
	return u
}

// SetName sets the "name" field.
func (u *TierUpsertOne) SetName(v string) *TierUpsertOne {
	return u.Update(func(s *TierUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *TierUpsertOne) UpdateName() *TierUpsertOne {
	return u.Update(func(s *TierUpsert) {
		s.UpdateName()
	})
}

// SetDiskMB sets the "disk_mb" field.
func (u *TierUpsertOne) SetDiskMB(v int64) *TierUpsertOne {
	return u.Update(func(s *TierUpsert) {
		s.SetDiskMB(v)
	})
}

// AddDiskMB adds v to the "disk_mb" field.
func (u *TierUpsertOne) AddDiskMB(v int64) *TierUpsertOne {
	return u.Update(func(s *TierUpsert) {
		s.AddDiskMB(v)
	})
}

// UpdateDiskMB sets the "disk_mb" field to the value that was provided on create.
func (u *TierUpsertOne) UpdateDiskMB() *TierUpsertOne {
	return u.Update(func(s *TierUpsert) {
		s.UpdateDiskMB()
	})
}

// SetConcurrentInstances sets the "concurrent_instances" field.
func (u *TierUpsertOne) SetConcurrentInstances(v int64) *TierUpsertOne {
	return u.Update(func(s *TierUpsert) {
		s.SetConcurrentInstances(v)
	})
}

// AddConcurrentInstances adds v to the "concurrent_instances" field.
func (u *TierUpsertOne) AddConcurrentInstances(v int64) *TierUpsertOne {
	return u.Update(func(s *TierUpsert) {
		s.AddConcurrentInstances(v)
	})
}

// UpdateConcurrentInstances sets the "concurrent_instances" field to the value that was provided on create.
func (u *TierUpsertOne) UpdateConcurrentInstances() *TierUpsertOne {
	return u.Update(func(s *TierUpsert) {
		s.UpdateConcurrentInstances()
	})
}

// SetMaxLengthHours sets the "max_length_hours" field.
func (u *TierUpsertOne) SetMaxLengthHours(v int64) *TierUpsertOne {
	return u.Update(func(s *TierUpsert) {
		s.SetMaxLengthHours(v)
	})
}

// AddMaxLengthHours adds v to the "max_length_hours" field.
func (u *TierUpsertOne) AddMaxLengthHours(v int64) *TierUpsertOne {
	return u.Update(func(s *TierUpsert) {
		s.AddMaxLengthHours(v)
	})
}

// UpdateMaxLengthHours sets the "max_length_hours" field to the value that was provided on create.
func (u *TierUpsertOne) UpdateMaxLengthHours() *TierUpsertOne {
	return u.Update(func(s *TierUpsert) {
		s.UpdateMaxLengthHours()
	})
}

// Exec executes the query.
func (u *TierUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("models: missing options for TierCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *TierUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *TierUpsertOne) ID(ctx context.Context) (id string, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("models: TierUpsertOne.ID is not supported by MySQL driver. Use TierUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *TierUpsertOne) IDX(ctx context.Context) string {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// TierCreateBulk is the builder for creating many Tier entities in bulk.
type TierCreateBulk struct {
	config
	err      error
	builders []*TierCreate
	conflict []sql.ConflictOption
}

// Save creates the Tier entities in the database.
func (tcb *TierCreateBulk) Save(ctx context.Context) ([]*Tier, error) {
	if tcb.err != nil {
		return nil, tcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(tcb.builders))
	nodes := make([]*Tier, len(tcb.builders))
	mutators := make([]Mutator, len(tcb.builders))
	for i := range tcb.builders {
		func(i int, root context.Context) {
			builder := tcb.builders[i]
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*TierMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, tcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = tcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, tcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, tcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (tcb *TierCreateBulk) SaveX(ctx context.Context) []*Tier {
	v, err := tcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (tcb *TierCreateBulk) Exec(ctx context.Context) error {
	_, err := tcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tcb *TierCreateBulk) ExecX(ctx context.Context) {
	if err := tcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Tier.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.TierUpsert) {
//			SetName(v+v).
//		}).
//		Exec(ctx)
func (tcb *TierCreateBulk) OnConflict(opts ...sql.ConflictOption) *TierUpsertBulk {
	tcb.conflict = opts
	return &TierUpsertBulk{
		create: tcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Tier.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (tcb *TierCreateBulk) OnConflictColumns(columns ...string) *TierUpsertBulk {
	tcb.conflict = append(tcb.conflict, sql.ConflictColumns(columns...))
	return &TierUpsertBulk{
		create: tcb,
	}
}

// TierUpsertBulk is the builder for "upsert"-ing
// a bulk of Tier nodes.
type TierUpsertBulk struct {
	create *TierCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.Tier.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(tier.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *TierUpsertBulk) UpdateNewValues() *TierUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(tier.FieldID)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Tier.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *TierUpsertBulk) Ignore() *TierUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *TierUpsertBulk) DoNothing() *TierUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the TierCreateBulk.OnConflict
// documentation for more info.
func (u *TierUpsertBulk) Update(set func(*TierUpsert)) *TierUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&TierUpsert{UpdateSet: update})
	}))
	return u
}

// SetName sets the "name" field.
func (u *TierUpsertBulk) SetName(v string) *TierUpsertBulk {
	return u.Update(func(s *TierUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *TierUpsertBulk) UpdateName() *TierUpsertBulk {
	return u.Update(func(s *TierUpsert) {
		s.UpdateName()
	})
}

// SetDiskMB sets the "disk_mb" field.
func (u *TierUpsertBulk) SetDiskMB(v int64) *TierUpsertBulk {
	return u.Update(func(s *TierUpsert) {
		s.SetDiskMB(v)
	})
}

// AddDiskMB adds v to the "disk_mb" field.
func (u *TierUpsertBulk) AddDiskMB(v int64) *TierUpsertBulk {
	return u.Update(func(s *TierUpsert) {
		s.AddDiskMB(v)
	})
}

// UpdateDiskMB sets the "disk_mb" field to the value that was provided on create.
func (u *TierUpsertBulk) UpdateDiskMB() *TierUpsertBulk {
	return u.Update(func(s *TierUpsert) {
		s.UpdateDiskMB()
	})
}

// SetConcurrentInstances sets the "concurrent_instances" field.
func (u *TierUpsertBulk) SetConcurrentInstances(v int64) *TierUpsertBulk {
	return u.Update(func(s *TierUpsert) {
		s.SetConcurrentInstances(v)
	})
}

// AddConcurrentInstances adds v to the "concurrent_instances" field.
func (u *TierUpsertBulk) AddConcurrentInstances(v int64) *TierUpsertBulk {
	return u.Update(func(s *TierUpsert) {
		s.AddConcurrentInstances(v)
	})
}

// UpdateConcurrentInstances sets the "concurrent_instances" field to the value that was provided on create.
func (u *TierUpsertBulk) UpdateConcurrentInstances() *TierUpsertBulk {
	return u.Update(func(s *TierUpsert) {
		s.UpdateConcurrentInstances()
	})
}

// SetMaxLengthHours sets the "max_length_hours" field.
func (u *TierUpsertBulk) SetMaxLengthHours(v int64) *TierUpsertBulk {
	return u.Update(func(s *TierUpsert) {
		s.SetMaxLengthHours(v)
	})
}

// AddMaxLengthHours adds v to the "max_length_hours" field.
func (u *TierUpsertBulk) AddMaxLengthHours(v int64) *TierUpsertBulk {
	return u.Update(func(s *TierUpsert) {
		s.AddMaxLengthHours(v)
	})
}

// UpdateMaxLengthHours sets the "max_length_hours" field to the value that was provided on create.
func (u *TierUpsertBulk) UpdateMaxLengthHours() *TierUpsertBulk {
	return u.Update(func(s *TierUpsert) {
		s.UpdateMaxLengthHours()
	})
}

// Exec executes the query.
func (u *TierUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("models: OnConflict was set for builder %d. Set it on the TierCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("models: missing options for TierCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *TierUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
