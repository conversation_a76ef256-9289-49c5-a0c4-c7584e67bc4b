// Code generated by ent, DO NOT EDIT.

package models

import (
	"context"
	"errors"
	"fmt"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/internal"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/predicate"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/team"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/tier"
	"github.com/google/uuid"
)

// TierUpdate is the builder for updating Tier entities.
type TierUpdate struct {
	config
	hooks     []Hook
	mutation  *TierMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the TierUpdate builder.
func (tu *TierUpdate) Where(ps ...predicate.Tier) *TierUpdate {
	tu.mutation.Where(ps...)
	return tu
}

// SetName sets the "name" field.
func (tu *TierUpdate) SetName(s string) *TierUpdate {
	tu.mutation.SetName(s)
	return tu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (tu *TierUpdate) SetNillableName(s *string) *TierUpdate {
	if s != nil {
		tu.SetName(*s)
	}
	return tu
}

// SetDiskMB sets the "disk_mb" field.
func (tu *TierUpdate) SetDiskMB(i int64) *TierUpdate {
	tu.mutation.ResetDiskMB()
	tu.mutation.SetDiskMB(i)
	return tu
}

// SetNillableDiskMB sets the "disk_mb" field if the given value is not nil.
func (tu *TierUpdate) SetNillableDiskMB(i *int64) *TierUpdate {
	if i != nil {
		tu.SetDiskMB(*i)
	}
	return tu
}

// AddDiskMB adds i to the "disk_mb" field.
func (tu *TierUpdate) AddDiskMB(i int64) *TierUpdate {
	tu.mutation.AddDiskMB(i)
	return tu
}

// SetConcurrentInstances sets the "concurrent_instances" field.
func (tu *TierUpdate) SetConcurrentInstances(i int64) *TierUpdate {
	tu.mutation.ResetConcurrentInstances()
	tu.mutation.SetConcurrentInstances(i)
	return tu
}

// SetNillableConcurrentInstances sets the "concurrent_instances" field if the given value is not nil.
func (tu *TierUpdate) SetNillableConcurrentInstances(i *int64) *TierUpdate {
	if i != nil {
		tu.SetConcurrentInstances(*i)
	}
	return tu
}

// AddConcurrentInstances adds i to the "concurrent_instances" field.
func (tu *TierUpdate) AddConcurrentInstances(i int64) *TierUpdate {
	tu.mutation.AddConcurrentInstances(i)
	return tu
}

// SetMaxLengthHours sets the "max_length_hours" field.
func (tu *TierUpdate) SetMaxLengthHours(i int64) *TierUpdate {
	tu.mutation.ResetMaxLengthHours()
	tu.mutation.SetMaxLengthHours(i)
	return tu
}

// SetNillableMaxLengthHours sets the "max_length_hours" field if the given value is not nil.
func (tu *TierUpdate) SetNillableMaxLengthHours(i *int64) *TierUpdate {
	if i != nil {
		tu.SetMaxLengthHours(*i)
	}
	return tu
}

// AddMaxLengthHours adds i to the "max_length_hours" field.
func (tu *TierUpdate) AddMaxLengthHours(i int64) *TierUpdate {
	tu.mutation.AddMaxLengthHours(i)
	return tu
}

// AddTeamIDs adds the "teams" edge to the Team entity by IDs.
func (tu *TierUpdate) AddTeamIDs(ids ...uuid.UUID) *TierUpdate {
	tu.mutation.AddTeamIDs(ids...)
	return tu
}

// AddTeams adds the "teams" edges to the Team entity.
func (tu *TierUpdate) AddTeams(t ...*Team) *TierUpdate {
	ids := make([]uuid.UUID, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return tu.AddTeamIDs(ids...)
}

// Mutation returns the TierMutation object of the builder.
func (tu *TierUpdate) Mutation() *TierMutation {
	return tu.mutation
}

// ClearTeams clears all "teams" edges to the Team entity.
func (tu *TierUpdate) ClearTeams() *TierUpdate {
	tu.mutation.ClearTeams()
	return tu
}

// RemoveTeamIDs removes the "teams" edge to Team entities by IDs.
func (tu *TierUpdate) RemoveTeamIDs(ids ...uuid.UUID) *TierUpdate {
	tu.mutation.RemoveTeamIDs(ids...)
	return tu
}

// RemoveTeams removes "teams" edges to Team entities.
func (tu *TierUpdate) RemoveTeams(t ...*Team) *TierUpdate {
	ids := make([]uuid.UUID, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return tu.RemoveTeamIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (tu *TierUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, tu.sqlSave, tu.mutation, tu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (tu *TierUpdate) SaveX(ctx context.Context) int {
	affected, err := tu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (tu *TierUpdate) Exec(ctx context.Context) error {
	_, err := tu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tu *TierUpdate) ExecX(ctx context.Context) {
	if err := tu.Exec(ctx); err != nil {
		panic(err)
	}
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (tu *TierUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *TierUpdate {
	tu.modifiers = append(tu.modifiers, modifiers...)
	return tu
}

func (tu *TierUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(tier.Table, tier.Columns, sqlgraph.NewFieldSpec(tier.FieldID, field.TypeString))
	if ps := tu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := tu.mutation.Name(); ok {
		_spec.SetField(tier.FieldName, field.TypeString, value)
	}
	if value, ok := tu.mutation.DiskMB(); ok {
		_spec.SetField(tier.FieldDiskMB, field.TypeInt64, value)
	}
	if value, ok := tu.mutation.AddedDiskMB(); ok {
		_spec.AddField(tier.FieldDiskMB, field.TypeInt64, value)
	}
	if value, ok := tu.mutation.ConcurrentInstances(); ok {
		_spec.SetField(tier.FieldConcurrentInstances, field.TypeInt64, value)
	}
	if value, ok := tu.mutation.AddedConcurrentInstances(); ok {
		_spec.AddField(tier.FieldConcurrentInstances, field.TypeInt64, value)
	}
	if value, ok := tu.mutation.MaxLengthHours(); ok {
		_spec.SetField(tier.FieldMaxLengthHours, field.TypeInt64, value)
	}
	if value, ok := tu.mutation.AddedMaxLengthHours(); ok {
		_spec.AddField(tier.FieldMaxLengthHours, field.TypeInt64, value)
	}
	if tu.mutation.TeamsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   tier.TeamsTable,
			Columns: []string{tier.TeamsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(team.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = tu.schemaConfig.Team
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tu.mutation.RemovedTeamsIDs(); len(nodes) > 0 && !tu.mutation.TeamsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   tier.TeamsTable,
			Columns: []string{tier.TeamsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(team.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = tu.schemaConfig.Team
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tu.mutation.TeamsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   tier.TeamsTable,
			Columns: []string{tier.TeamsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(team.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = tu.schemaConfig.Team
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.Node.Schema = tu.schemaConfig.Tier
	ctx = internal.NewSchemaConfigContext(ctx, tu.schemaConfig)
	_spec.AddModifiers(tu.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, tu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{tier.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	tu.mutation.done = true
	return n, nil
}

// TierUpdateOne is the builder for updating a single Tier entity.
type TierUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *TierMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetName sets the "name" field.
func (tuo *TierUpdateOne) SetName(s string) *TierUpdateOne {
	tuo.mutation.SetName(s)
	return tuo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (tuo *TierUpdateOne) SetNillableName(s *string) *TierUpdateOne {
	if s != nil {
		tuo.SetName(*s)
	}
	return tuo
}

// SetDiskMB sets the "disk_mb" field.
func (tuo *TierUpdateOne) SetDiskMB(i int64) *TierUpdateOne {
	tuo.mutation.ResetDiskMB()
	tuo.mutation.SetDiskMB(i)
	return tuo
}

// SetNillableDiskMB sets the "disk_mb" field if the given value is not nil.
func (tuo *TierUpdateOne) SetNillableDiskMB(i *int64) *TierUpdateOne {
	if i != nil {
		tuo.SetDiskMB(*i)
	}
	return tuo
}

// AddDiskMB adds i to the "disk_mb" field.
func (tuo *TierUpdateOne) AddDiskMB(i int64) *TierUpdateOne {
	tuo.mutation.AddDiskMB(i)
	return tuo
}

// SetConcurrentInstances sets the "concurrent_instances" field.
func (tuo *TierUpdateOne) SetConcurrentInstances(i int64) *TierUpdateOne {
	tuo.mutation.ResetConcurrentInstances()
	tuo.mutation.SetConcurrentInstances(i)
	return tuo
}

// SetNillableConcurrentInstances sets the "concurrent_instances" field if the given value is not nil.
func (tuo *TierUpdateOne) SetNillableConcurrentInstances(i *int64) *TierUpdateOne {
	if i != nil {
		tuo.SetConcurrentInstances(*i)
	}
	return tuo
}

// AddConcurrentInstances adds i to the "concurrent_instances" field.
func (tuo *TierUpdateOne) AddConcurrentInstances(i int64) *TierUpdateOne {
	tuo.mutation.AddConcurrentInstances(i)
	return tuo
}

// SetMaxLengthHours sets the "max_length_hours" field.
func (tuo *TierUpdateOne) SetMaxLengthHours(i int64) *TierUpdateOne {
	tuo.mutation.ResetMaxLengthHours()
	tuo.mutation.SetMaxLengthHours(i)
	return tuo
}

// SetNillableMaxLengthHours sets the "max_length_hours" field if the given value is not nil.
func (tuo *TierUpdateOne) SetNillableMaxLengthHours(i *int64) *TierUpdateOne {
	if i != nil {
		tuo.SetMaxLengthHours(*i)
	}
	return tuo
}

// AddMaxLengthHours adds i to the "max_length_hours" field.
func (tuo *TierUpdateOne) AddMaxLengthHours(i int64) *TierUpdateOne {
	tuo.mutation.AddMaxLengthHours(i)
	return tuo
}

// AddTeamIDs adds the "teams" edge to the Team entity by IDs.
func (tuo *TierUpdateOne) AddTeamIDs(ids ...uuid.UUID) *TierUpdateOne {
	tuo.mutation.AddTeamIDs(ids...)
	return tuo
}

// AddTeams adds the "teams" edges to the Team entity.
func (tuo *TierUpdateOne) AddTeams(t ...*Team) *TierUpdateOne {
	ids := make([]uuid.UUID, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return tuo.AddTeamIDs(ids...)
}

// Mutation returns the TierMutation object of the builder.
func (tuo *TierUpdateOne) Mutation() *TierMutation {
	return tuo.mutation
}

// ClearTeams clears all "teams" edges to the Team entity.
func (tuo *TierUpdateOne) ClearTeams() *TierUpdateOne {
	tuo.mutation.ClearTeams()
	return tuo
}

// RemoveTeamIDs removes the "teams" edge to Team entities by IDs.
func (tuo *TierUpdateOne) RemoveTeamIDs(ids ...uuid.UUID) *TierUpdateOne {
	tuo.mutation.RemoveTeamIDs(ids...)
	return tuo
}

// RemoveTeams removes "teams" edges to Team entities.
func (tuo *TierUpdateOne) RemoveTeams(t ...*Team) *TierUpdateOne {
	ids := make([]uuid.UUID, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return tuo.RemoveTeamIDs(ids...)
}

// Where appends a list predicates to the TierUpdate builder.
func (tuo *TierUpdateOne) Where(ps ...predicate.Tier) *TierUpdateOne {
	tuo.mutation.Where(ps...)
	return tuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (tuo *TierUpdateOne) Select(field string, fields ...string) *TierUpdateOne {
	tuo.fields = append([]string{field}, fields...)
	return tuo
}

// Save executes the query and returns the updated Tier entity.
func (tuo *TierUpdateOne) Save(ctx context.Context) (*Tier, error) {
	return withHooks(ctx, tuo.sqlSave, tuo.mutation, tuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (tuo *TierUpdateOne) SaveX(ctx context.Context) *Tier {
	node, err := tuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (tuo *TierUpdateOne) Exec(ctx context.Context) error {
	_, err := tuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tuo *TierUpdateOne) ExecX(ctx context.Context) {
	if err := tuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (tuo *TierUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *TierUpdateOne {
	tuo.modifiers = append(tuo.modifiers, modifiers...)
	return tuo
}

func (tuo *TierUpdateOne) sqlSave(ctx context.Context) (_node *Tier, err error) {
	_spec := sqlgraph.NewUpdateSpec(tier.Table, tier.Columns, sqlgraph.NewFieldSpec(tier.FieldID, field.TypeString))
	id, ok := tuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`models: missing "Tier.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := tuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, tier.FieldID)
		for _, f := range fields {
			if !tier.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("models: invalid field %q for query", f)}
			}
			if f != tier.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := tuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := tuo.mutation.Name(); ok {
		_spec.SetField(tier.FieldName, field.TypeString, value)
	}
	if value, ok := tuo.mutation.DiskMB(); ok {
		_spec.SetField(tier.FieldDiskMB, field.TypeInt64, value)
	}
	if value, ok := tuo.mutation.AddedDiskMB(); ok {
		_spec.AddField(tier.FieldDiskMB, field.TypeInt64, value)
	}
	if value, ok := tuo.mutation.ConcurrentInstances(); ok {
		_spec.SetField(tier.FieldConcurrentInstances, field.TypeInt64, value)
	}
	if value, ok := tuo.mutation.AddedConcurrentInstances(); ok {
		_spec.AddField(tier.FieldConcurrentInstances, field.TypeInt64, value)
	}
	if value, ok := tuo.mutation.MaxLengthHours(); ok {
		_spec.SetField(tier.FieldMaxLengthHours, field.TypeInt64, value)
	}
	if value, ok := tuo.mutation.AddedMaxLengthHours(); ok {
		_spec.AddField(tier.FieldMaxLengthHours, field.TypeInt64, value)
	}
	if tuo.mutation.TeamsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   tier.TeamsTable,
			Columns: []string{tier.TeamsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(team.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = tuo.schemaConfig.Team
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tuo.mutation.RemovedTeamsIDs(); len(nodes) > 0 && !tuo.mutation.TeamsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   tier.TeamsTable,
			Columns: []string{tier.TeamsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(team.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = tuo.schemaConfig.Team
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tuo.mutation.TeamsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   tier.TeamsTable,
			Columns: []string{tier.TeamsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(team.FieldID, field.TypeUUID),
			},
		}
		edge.Schema = tuo.schemaConfig.Team
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.Node.Schema = tuo.schemaConfig.Tier
	ctx = internal.NewSchemaConfigContext(ctx, tuo.schemaConfig)
	_spec.AddModifiers(tuo.modifiers...)
	_node = &Tier{config: tuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, tuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{tier.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	tuo.mutation.done = true
	return _node, nil
}
