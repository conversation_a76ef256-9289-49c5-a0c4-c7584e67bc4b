// Code generated by ent, DO NOT EDIT.

package models

import (
	"context"
	"errors"
	"fmt"
	"log"
	"reflect"

	"github.com/e2b-dev/infra/packages/shared/pkg/models/migrate"
	"github.com/google/uuid"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/accesstoken"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/env"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/envalias"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/envbuild"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/snapshot"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/team"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/teamapikey"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/tier"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/user"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/usersteams"

	"github.com/e2b-dev/infra/packages/shared/pkg/models/internal"
)

// Client is the client that holds all ent builders.
type Client struct {
	config
	// Schema is the client for creating, migrating and dropping schema.
	Schema *migrate.Schema
	// AccessToken is the client for interacting with the AccessToken builders.
	AccessToken *AccessTokenClient
	// Env is the client for interacting with the Env builders.
	Env *EnvClient
	// EnvAlias is the client for interacting with the EnvAlias builders.
	EnvAlias *EnvAliasClient
	// EnvBuild is the client for interacting with the EnvBuild builders.
	EnvBuild *EnvBuildClient
	// Snapshot is the client for interacting with the Snapshot builders.
	Snapshot *SnapshotClient
	// Team is the client for interacting with the Team builders.
	Team *TeamClient
	// TeamAPIKey is the client for interacting with the TeamAPIKey builders.
	TeamAPIKey *TeamAPIKeyClient
	// Tier is the client for interacting with the Tier builders.
	Tier *TierClient
	// User is the client for interacting with the User builders.
	User *UserClient
	// UsersTeams is the client for interacting with the UsersTeams builders.
	UsersTeams *UsersTeamsClient
}

// NewClient creates a new client configured with the given options.
func NewClient(opts ...Option) *Client {
	client := &Client{config: newConfig(opts...)}
	client.init()
	return client
}

func (c *Client) init() {
	c.Schema = migrate.NewSchema(c.driver)
	c.AccessToken = NewAccessTokenClient(c.config)
	c.Env = NewEnvClient(c.config)
	c.EnvAlias = NewEnvAliasClient(c.config)
	c.EnvBuild = NewEnvBuildClient(c.config)
	c.Snapshot = NewSnapshotClient(c.config)
	c.Team = NewTeamClient(c.config)
	c.TeamAPIKey = NewTeamAPIKeyClient(c.config)
	c.Tier = NewTierClient(c.config)
	c.User = NewUserClient(c.config)
	c.UsersTeams = NewUsersTeamsClient(c.config)
}

type (
	// config is the configuration for the client and its builder.
	config struct {
		// driver used for executing database requests.
		driver dialect.Driver
		// debug enable a debug logging.
		debug bool
		// log used for logging on debug mode.
		log func(...any)
		// hooks to execute on mutations.
		hooks *hooks
		// interceptors to execute on queries.
		inters *inters
		// schemaConfig contains alternative names for all tables.
		schemaConfig SchemaConfig
	}
	// Option function to configure the client.
	Option func(*config)
)

// newConfig creates a new config for the client.
func newConfig(opts ...Option) config {
	cfg := config{log: log.Println, hooks: &hooks{}, inters: &inters{}}
	cfg.schemaConfig = DefaultSchemaConfig
	cfg.options(opts...)
	return cfg
}

// options applies the options on the config object.
func (c *config) options(opts ...Option) {
	for _, opt := range opts {
		opt(c)
	}
	if c.debug {
		c.driver = dialect.Debug(c.driver, c.log)
	}
}

// Debug enables debug logging on the ent.Driver.
func Debug() Option {
	return func(c *config) {
		c.debug = true
	}
}

// Log sets the logging function for debug mode.
func Log(fn func(...any)) Option {
	return func(c *config) {
		c.log = fn
	}
}

// Driver configures the client driver.
func Driver(driver dialect.Driver) Option {
	return func(c *config) {
		c.driver = driver
	}
}

// Open opens a database/sql.DB specified by the driver name and
// the data source name, and returns a new client attached to it.
// Optional parameters can be added for configuring the client.
func Open(driverName, dataSourceName string, options ...Option) (*Client, error) {
	switch driverName {
	case dialect.MySQL, dialect.Postgres, dialect.SQLite:
		drv, err := sql.Open(driverName, dataSourceName)
		if err != nil {
			return nil, err
		}
		return NewClient(append(options, Driver(drv))...), nil
	default:
		return nil, fmt.Errorf("unsupported driver: %q", driverName)
	}
}

// ErrTxStarted is returned when trying to start a new transaction from a transactional client.
var ErrTxStarted = errors.New("models: cannot start a transaction within a transaction")

// Tx returns a new transactional client. The provided context
// is used until the transaction is committed or rolled back.
func (c *Client) Tx(ctx context.Context) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, ErrTxStarted
	}
	tx, err := newTx(ctx, c.driver)
	if err != nil {
		return nil, fmt.Errorf("models: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = tx
	return &Tx{
		ctx:         ctx,
		config:      cfg,
		AccessToken: NewAccessTokenClient(cfg),
		Env:         NewEnvClient(cfg),
		EnvAlias:    NewEnvAliasClient(cfg),
		EnvBuild:    NewEnvBuildClient(cfg),
		Snapshot:    NewSnapshotClient(cfg),
		Team:        NewTeamClient(cfg),
		TeamAPIKey:  NewTeamAPIKeyClient(cfg),
		Tier:        NewTierClient(cfg),
		User:        NewUserClient(cfg),
		UsersTeams:  NewUsersTeamsClient(cfg),
	}, nil
}

// BeginTx returns a transactional client with specified options.
func (c *Client) BeginTx(ctx context.Context, opts *sql.TxOptions) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, errors.New("ent: cannot start a transaction within a transaction")
	}
	tx, err := c.driver.(interface {
		BeginTx(context.Context, *sql.TxOptions) (dialect.Tx, error)
	}).BeginTx(ctx, opts)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = &txDriver{tx: tx, drv: c.driver}
	return &Tx{
		ctx:         ctx,
		config:      cfg,
		AccessToken: NewAccessTokenClient(cfg),
		Env:         NewEnvClient(cfg),
		EnvAlias:    NewEnvAliasClient(cfg),
		EnvBuild:    NewEnvBuildClient(cfg),
		Snapshot:    NewSnapshotClient(cfg),
		Team:        NewTeamClient(cfg),
		TeamAPIKey:  NewTeamAPIKeyClient(cfg),
		Tier:        NewTierClient(cfg),
		User:        NewUserClient(cfg),
		UsersTeams:  NewUsersTeamsClient(cfg),
	}, nil
}

// Debug returns a new debug-client. It's used to get verbose logging on specific operations.
//
//	client.Debug().
//		AccessToken.
//		Query().
//		Count(ctx)
func (c *Client) Debug() *Client {
	if c.debug {
		return c
	}
	cfg := c.config
	cfg.driver = dialect.Debug(c.driver, c.log)
	client := &Client{config: cfg}
	client.init()
	return client
}

// Close closes the database connection and prevents new queries from starting.
func (c *Client) Close() error {
	return c.driver.Close()
}

// Use adds the mutation hooks to all the entity clients.
// In order to add hooks to a specific client, call: `client.Node.Use(...)`.
func (c *Client) Use(hooks ...Hook) {
	for _, n := range []interface{ Use(...Hook) }{
		c.AccessToken, c.Env, c.EnvAlias, c.EnvBuild, c.Snapshot, c.Team, c.TeamAPIKey,
		c.Tier, c.User, c.UsersTeams,
	} {
		n.Use(hooks...)
	}
}

// Intercept adds the query interceptors to all the entity clients.
// In order to add interceptors to a specific client, call: `client.Node.Intercept(...)`.
func (c *Client) Intercept(interceptors ...Interceptor) {
	for _, n := range []interface{ Intercept(...Interceptor) }{
		c.AccessToken, c.Env, c.EnvAlias, c.EnvBuild, c.Snapshot, c.Team, c.TeamAPIKey,
		c.Tier, c.User, c.UsersTeams,
	} {
		n.Intercept(interceptors...)
	}
}

// Mutate implements the ent.Mutator interface.
func (c *Client) Mutate(ctx context.Context, m Mutation) (Value, error) {
	switch m := m.(type) {
	case *AccessTokenMutation:
		return c.AccessToken.mutate(ctx, m)
	case *EnvMutation:
		return c.Env.mutate(ctx, m)
	case *EnvAliasMutation:
		return c.EnvAlias.mutate(ctx, m)
	case *EnvBuildMutation:
		return c.EnvBuild.mutate(ctx, m)
	case *SnapshotMutation:
		return c.Snapshot.mutate(ctx, m)
	case *TeamMutation:
		return c.Team.mutate(ctx, m)
	case *TeamAPIKeyMutation:
		return c.TeamAPIKey.mutate(ctx, m)
	case *TierMutation:
		return c.Tier.mutate(ctx, m)
	case *UserMutation:
		return c.User.mutate(ctx, m)
	case *UsersTeamsMutation:
		return c.UsersTeams.mutate(ctx, m)
	default:
		return nil, fmt.Errorf("models: unknown mutation type %T", m)
	}
}

// AccessTokenClient is a client for the AccessToken schema.
type AccessTokenClient struct {
	config
}

// NewAccessTokenClient returns a client for the AccessToken from the given config.
func NewAccessTokenClient(c config) *AccessTokenClient {
	return &AccessTokenClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `accesstoken.Hooks(f(g(h())))`.
func (c *AccessTokenClient) Use(hooks ...Hook) {
	c.hooks.AccessToken = append(c.hooks.AccessToken, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `accesstoken.Intercept(f(g(h())))`.
func (c *AccessTokenClient) Intercept(interceptors ...Interceptor) {
	c.inters.AccessToken = append(c.inters.AccessToken, interceptors...)
}

// Create returns a builder for creating a AccessToken entity.
func (c *AccessTokenClient) Create() *AccessTokenCreate {
	mutation := newAccessTokenMutation(c.config, OpCreate)
	return &AccessTokenCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of AccessToken entities.
func (c *AccessTokenClient) CreateBulk(builders ...*AccessTokenCreate) *AccessTokenCreateBulk {
	return &AccessTokenCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *AccessTokenClient) MapCreateBulk(slice any, setFunc func(*AccessTokenCreate, int)) *AccessTokenCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &AccessTokenCreateBulk{err: fmt.Errorf("calling to AccessTokenClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*AccessTokenCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &AccessTokenCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for AccessToken.
func (c *AccessTokenClient) Update() *AccessTokenUpdate {
	mutation := newAccessTokenMutation(c.config, OpUpdate)
	return &AccessTokenUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *AccessTokenClient) UpdateOne(at *AccessToken) *AccessTokenUpdateOne {
	mutation := newAccessTokenMutation(c.config, OpUpdateOne, withAccessToken(at))
	return &AccessTokenUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *AccessTokenClient) UpdateOneID(id uuid.UUID) *AccessTokenUpdateOne {
	mutation := newAccessTokenMutation(c.config, OpUpdateOne, withAccessTokenID(id))
	return &AccessTokenUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for AccessToken.
func (c *AccessTokenClient) Delete() *AccessTokenDelete {
	mutation := newAccessTokenMutation(c.config, OpDelete)
	return &AccessTokenDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *AccessTokenClient) DeleteOne(at *AccessToken) *AccessTokenDeleteOne {
	return c.DeleteOneID(at.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *AccessTokenClient) DeleteOneID(id uuid.UUID) *AccessTokenDeleteOne {
	builder := c.Delete().Where(accesstoken.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &AccessTokenDeleteOne{builder}
}

// Query returns a query builder for AccessToken.
func (c *AccessTokenClient) Query() *AccessTokenQuery {
	return &AccessTokenQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeAccessToken},
		inters: c.Interceptors(),
	}
}

// Get returns a AccessToken entity by its id.
func (c *AccessTokenClient) Get(ctx context.Context, id uuid.UUID) (*AccessToken, error) {
	return c.Query().Where(accesstoken.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *AccessTokenClient) GetX(ctx context.Context, id uuid.UUID) *AccessToken {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryUser queries the user edge of a AccessToken.
func (c *AccessTokenClient) QueryUser(at *AccessToken) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := at.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(accesstoken.Table, accesstoken.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, accesstoken.UserTable, accesstoken.UserColumn),
		)
		schemaConfig := at.schemaConfig
		step.To.Schema = schemaConfig.User
		step.Edge.Schema = schemaConfig.AccessToken
		fromV = sqlgraph.Neighbors(at.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *AccessTokenClient) Hooks() []Hook {
	return c.hooks.AccessToken
}

// Interceptors returns the client interceptors.
func (c *AccessTokenClient) Interceptors() []Interceptor {
	return c.inters.AccessToken
}

func (c *AccessTokenClient) mutate(ctx context.Context, m *AccessTokenMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&AccessTokenCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&AccessTokenUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&AccessTokenUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&AccessTokenDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("models: unknown AccessToken mutation op: %q", m.Op())
	}
}

// EnvClient is a client for the Env schema.
type EnvClient struct {
	config
}

// NewEnvClient returns a client for the Env from the given config.
func NewEnvClient(c config) *EnvClient {
	return &EnvClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `env.Hooks(f(g(h())))`.
func (c *EnvClient) Use(hooks ...Hook) {
	c.hooks.Env = append(c.hooks.Env, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `env.Intercept(f(g(h())))`.
func (c *EnvClient) Intercept(interceptors ...Interceptor) {
	c.inters.Env = append(c.inters.Env, interceptors...)
}

// Create returns a builder for creating a Env entity.
func (c *EnvClient) Create() *EnvCreate {
	mutation := newEnvMutation(c.config, OpCreate)
	return &EnvCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Env entities.
func (c *EnvClient) CreateBulk(builders ...*EnvCreate) *EnvCreateBulk {
	return &EnvCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *EnvClient) MapCreateBulk(slice any, setFunc func(*EnvCreate, int)) *EnvCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &EnvCreateBulk{err: fmt.Errorf("calling to EnvClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*EnvCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &EnvCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Env.
func (c *EnvClient) Update() *EnvUpdate {
	mutation := newEnvMutation(c.config, OpUpdate)
	return &EnvUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *EnvClient) UpdateOne(e *Env) *EnvUpdateOne {
	mutation := newEnvMutation(c.config, OpUpdateOne, withEnv(e))
	return &EnvUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *EnvClient) UpdateOneID(id string) *EnvUpdateOne {
	mutation := newEnvMutation(c.config, OpUpdateOne, withEnvID(id))
	return &EnvUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Env.
func (c *EnvClient) Delete() *EnvDelete {
	mutation := newEnvMutation(c.config, OpDelete)
	return &EnvDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *EnvClient) DeleteOne(e *Env) *EnvDeleteOne {
	return c.DeleteOneID(e.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *EnvClient) DeleteOneID(id string) *EnvDeleteOne {
	builder := c.Delete().Where(env.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &EnvDeleteOne{builder}
}

// Query returns a query builder for Env.
func (c *EnvClient) Query() *EnvQuery {
	return &EnvQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeEnv},
		inters: c.Interceptors(),
	}
}

// Get returns a Env entity by its id.
func (c *EnvClient) Get(ctx context.Context, id string) (*Env, error) {
	return c.Query().Where(env.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *EnvClient) GetX(ctx context.Context, id string) *Env {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryTeam queries the team edge of a Env.
func (c *EnvClient) QueryTeam(e *Env) *TeamQuery {
	query := (&TeamClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := e.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(env.Table, env.FieldID, id),
			sqlgraph.To(team.Table, team.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, env.TeamTable, env.TeamColumn),
		)
		schemaConfig := e.schemaConfig
		step.To.Schema = schemaConfig.Team
		step.Edge.Schema = schemaConfig.Env
		fromV = sqlgraph.Neighbors(e.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryCreator queries the creator edge of a Env.
func (c *EnvClient) QueryCreator(e *Env) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := e.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(env.Table, env.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, env.CreatorTable, env.CreatorColumn),
		)
		schemaConfig := e.schemaConfig
		step.To.Schema = schemaConfig.User
		step.Edge.Schema = schemaConfig.Env
		fromV = sqlgraph.Neighbors(e.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryEnvAliases queries the env_aliases edge of a Env.
func (c *EnvClient) QueryEnvAliases(e *Env) *EnvAliasQuery {
	query := (&EnvAliasClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := e.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(env.Table, env.FieldID, id),
			sqlgraph.To(envalias.Table, envalias.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, env.EnvAliasesTable, env.EnvAliasesColumn),
		)
		schemaConfig := e.schemaConfig
		step.To.Schema = schemaConfig.EnvAlias
		step.Edge.Schema = schemaConfig.EnvAlias
		fromV = sqlgraph.Neighbors(e.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryBuilds queries the builds edge of a Env.
func (c *EnvClient) QueryBuilds(e *Env) *EnvBuildQuery {
	query := (&EnvBuildClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := e.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(env.Table, env.FieldID, id),
			sqlgraph.To(envbuild.Table, envbuild.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, env.BuildsTable, env.BuildsColumn),
		)
		schemaConfig := e.schemaConfig
		step.To.Schema = schemaConfig.EnvBuild
		step.Edge.Schema = schemaConfig.EnvBuild
		fromV = sqlgraph.Neighbors(e.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QuerySnapshots queries the snapshots edge of a Env.
func (c *EnvClient) QuerySnapshots(e *Env) *SnapshotQuery {
	query := (&SnapshotClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := e.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(env.Table, env.FieldID, id),
			sqlgraph.To(snapshot.Table, snapshot.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, env.SnapshotsTable, env.SnapshotsColumn),
		)
		schemaConfig := e.schemaConfig
		step.To.Schema = schemaConfig.Snapshot
		step.Edge.Schema = schemaConfig.Snapshot
		fromV = sqlgraph.Neighbors(e.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *EnvClient) Hooks() []Hook {
	return c.hooks.Env
}

// Interceptors returns the client interceptors.
func (c *EnvClient) Interceptors() []Interceptor {
	return c.inters.Env
}

func (c *EnvClient) mutate(ctx context.Context, m *EnvMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&EnvCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&EnvUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&EnvUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&EnvDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("models: unknown Env mutation op: %q", m.Op())
	}
}

// EnvAliasClient is a client for the EnvAlias schema.
type EnvAliasClient struct {
	config
}

// NewEnvAliasClient returns a client for the EnvAlias from the given config.
func NewEnvAliasClient(c config) *EnvAliasClient {
	return &EnvAliasClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `envalias.Hooks(f(g(h())))`.
func (c *EnvAliasClient) Use(hooks ...Hook) {
	c.hooks.EnvAlias = append(c.hooks.EnvAlias, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `envalias.Intercept(f(g(h())))`.
func (c *EnvAliasClient) Intercept(interceptors ...Interceptor) {
	c.inters.EnvAlias = append(c.inters.EnvAlias, interceptors...)
}

// Create returns a builder for creating a EnvAlias entity.
func (c *EnvAliasClient) Create() *EnvAliasCreate {
	mutation := newEnvAliasMutation(c.config, OpCreate)
	return &EnvAliasCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of EnvAlias entities.
func (c *EnvAliasClient) CreateBulk(builders ...*EnvAliasCreate) *EnvAliasCreateBulk {
	return &EnvAliasCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *EnvAliasClient) MapCreateBulk(slice any, setFunc func(*EnvAliasCreate, int)) *EnvAliasCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &EnvAliasCreateBulk{err: fmt.Errorf("calling to EnvAliasClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*EnvAliasCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &EnvAliasCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for EnvAlias.
func (c *EnvAliasClient) Update() *EnvAliasUpdate {
	mutation := newEnvAliasMutation(c.config, OpUpdate)
	return &EnvAliasUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *EnvAliasClient) UpdateOne(ea *EnvAlias) *EnvAliasUpdateOne {
	mutation := newEnvAliasMutation(c.config, OpUpdateOne, withEnvAlias(ea))
	return &EnvAliasUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *EnvAliasClient) UpdateOneID(id string) *EnvAliasUpdateOne {
	mutation := newEnvAliasMutation(c.config, OpUpdateOne, withEnvAliasID(id))
	return &EnvAliasUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for EnvAlias.
func (c *EnvAliasClient) Delete() *EnvAliasDelete {
	mutation := newEnvAliasMutation(c.config, OpDelete)
	return &EnvAliasDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *EnvAliasClient) DeleteOne(ea *EnvAlias) *EnvAliasDeleteOne {
	return c.DeleteOneID(ea.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *EnvAliasClient) DeleteOneID(id string) *EnvAliasDeleteOne {
	builder := c.Delete().Where(envalias.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &EnvAliasDeleteOne{builder}
}

// Query returns a query builder for EnvAlias.
func (c *EnvAliasClient) Query() *EnvAliasQuery {
	return &EnvAliasQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeEnvAlias},
		inters: c.Interceptors(),
	}
}

// Get returns a EnvAlias entity by its id.
func (c *EnvAliasClient) Get(ctx context.Context, id string) (*EnvAlias, error) {
	return c.Query().Where(envalias.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *EnvAliasClient) GetX(ctx context.Context, id string) *EnvAlias {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryEnv queries the env edge of a EnvAlias.
func (c *EnvAliasClient) QueryEnv(ea *EnvAlias) *EnvQuery {
	query := (&EnvClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := ea.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(envalias.Table, envalias.FieldID, id),
			sqlgraph.To(env.Table, env.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, envalias.EnvTable, envalias.EnvColumn),
		)
		schemaConfig := ea.schemaConfig
		step.To.Schema = schemaConfig.Env
		step.Edge.Schema = schemaConfig.EnvAlias
		fromV = sqlgraph.Neighbors(ea.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *EnvAliasClient) Hooks() []Hook {
	return c.hooks.EnvAlias
}

// Interceptors returns the client interceptors.
func (c *EnvAliasClient) Interceptors() []Interceptor {
	return c.inters.EnvAlias
}

func (c *EnvAliasClient) mutate(ctx context.Context, m *EnvAliasMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&EnvAliasCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&EnvAliasUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&EnvAliasUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&EnvAliasDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("models: unknown EnvAlias mutation op: %q", m.Op())
	}
}

// EnvBuildClient is a client for the EnvBuild schema.
type EnvBuildClient struct {
	config
}

// NewEnvBuildClient returns a client for the EnvBuild from the given config.
func NewEnvBuildClient(c config) *EnvBuildClient {
	return &EnvBuildClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `envbuild.Hooks(f(g(h())))`.
func (c *EnvBuildClient) Use(hooks ...Hook) {
	c.hooks.EnvBuild = append(c.hooks.EnvBuild, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `envbuild.Intercept(f(g(h())))`.
func (c *EnvBuildClient) Intercept(interceptors ...Interceptor) {
	c.inters.EnvBuild = append(c.inters.EnvBuild, interceptors...)
}

// Create returns a builder for creating a EnvBuild entity.
func (c *EnvBuildClient) Create() *EnvBuildCreate {
	mutation := newEnvBuildMutation(c.config, OpCreate)
	return &EnvBuildCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of EnvBuild entities.
func (c *EnvBuildClient) CreateBulk(builders ...*EnvBuildCreate) *EnvBuildCreateBulk {
	return &EnvBuildCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *EnvBuildClient) MapCreateBulk(slice any, setFunc func(*EnvBuildCreate, int)) *EnvBuildCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &EnvBuildCreateBulk{err: fmt.Errorf("calling to EnvBuildClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*EnvBuildCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &EnvBuildCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for EnvBuild.
func (c *EnvBuildClient) Update() *EnvBuildUpdate {
	mutation := newEnvBuildMutation(c.config, OpUpdate)
	return &EnvBuildUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *EnvBuildClient) UpdateOne(eb *EnvBuild) *EnvBuildUpdateOne {
	mutation := newEnvBuildMutation(c.config, OpUpdateOne, withEnvBuild(eb))
	return &EnvBuildUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *EnvBuildClient) UpdateOneID(id uuid.UUID) *EnvBuildUpdateOne {
	mutation := newEnvBuildMutation(c.config, OpUpdateOne, withEnvBuildID(id))
	return &EnvBuildUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for EnvBuild.
func (c *EnvBuildClient) Delete() *EnvBuildDelete {
	mutation := newEnvBuildMutation(c.config, OpDelete)
	return &EnvBuildDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *EnvBuildClient) DeleteOne(eb *EnvBuild) *EnvBuildDeleteOne {
	return c.DeleteOneID(eb.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *EnvBuildClient) DeleteOneID(id uuid.UUID) *EnvBuildDeleteOne {
	builder := c.Delete().Where(envbuild.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &EnvBuildDeleteOne{builder}
}

// Query returns a query builder for EnvBuild.
func (c *EnvBuildClient) Query() *EnvBuildQuery {
	return &EnvBuildQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeEnvBuild},
		inters: c.Interceptors(),
	}
}

// Get returns a EnvBuild entity by its id.
func (c *EnvBuildClient) Get(ctx context.Context, id uuid.UUID) (*EnvBuild, error) {
	return c.Query().Where(envbuild.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *EnvBuildClient) GetX(ctx context.Context, id uuid.UUID) *EnvBuild {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryEnv queries the env edge of a EnvBuild.
func (c *EnvBuildClient) QueryEnv(eb *EnvBuild) *EnvQuery {
	query := (&EnvClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := eb.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(envbuild.Table, envbuild.FieldID, id),
			sqlgraph.To(env.Table, env.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, envbuild.EnvTable, envbuild.EnvColumn),
		)
		schemaConfig := eb.schemaConfig
		step.To.Schema = schemaConfig.Env
		step.Edge.Schema = schemaConfig.EnvBuild
		fromV = sqlgraph.Neighbors(eb.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *EnvBuildClient) Hooks() []Hook {
	return c.hooks.EnvBuild
}

// Interceptors returns the client interceptors.
func (c *EnvBuildClient) Interceptors() []Interceptor {
	return c.inters.EnvBuild
}

func (c *EnvBuildClient) mutate(ctx context.Context, m *EnvBuildMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&EnvBuildCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&EnvBuildUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&EnvBuildUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&EnvBuildDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("models: unknown EnvBuild mutation op: %q", m.Op())
	}
}

// SnapshotClient is a client for the Snapshot schema.
type SnapshotClient struct {
	config
}

// NewSnapshotClient returns a client for the Snapshot from the given config.
func NewSnapshotClient(c config) *SnapshotClient {
	return &SnapshotClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `snapshot.Hooks(f(g(h())))`.
func (c *SnapshotClient) Use(hooks ...Hook) {
	c.hooks.Snapshot = append(c.hooks.Snapshot, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `snapshot.Intercept(f(g(h())))`.
func (c *SnapshotClient) Intercept(interceptors ...Interceptor) {
	c.inters.Snapshot = append(c.inters.Snapshot, interceptors...)
}

// Create returns a builder for creating a Snapshot entity.
func (c *SnapshotClient) Create() *SnapshotCreate {
	mutation := newSnapshotMutation(c.config, OpCreate)
	return &SnapshotCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Snapshot entities.
func (c *SnapshotClient) CreateBulk(builders ...*SnapshotCreate) *SnapshotCreateBulk {
	return &SnapshotCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *SnapshotClient) MapCreateBulk(slice any, setFunc func(*SnapshotCreate, int)) *SnapshotCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &SnapshotCreateBulk{err: fmt.Errorf("calling to SnapshotClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*SnapshotCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &SnapshotCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Snapshot.
func (c *SnapshotClient) Update() *SnapshotUpdate {
	mutation := newSnapshotMutation(c.config, OpUpdate)
	return &SnapshotUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *SnapshotClient) UpdateOne(s *Snapshot) *SnapshotUpdateOne {
	mutation := newSnapshotMutation(c.config, OpUpdateOne, withSnapshot(s))
	return &SnapshotUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *SnapshotClient) UpdateOneID(id uuid.UUID) *SnapshotUpdateOne {
	mutation := newSnapshotMutation(c.config, OpUpdateOne, withSnapshotID(id))
	return &SnapshotUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Snapshot.
func (c *SnapshotClient) Delete() *SnapshotDelete {
	mutation := newSnapshotMutation(c.config, OpDelete)
	return &SnapshotDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *SnapshotClient) DeleteOne(s *Snapshot) *SnapshotDeleteOne {
	return c.DeleteOneID(s.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *SnapshotClient) DeleteOneID(id uuid.UUID) *SnapshotDeleteOne {
	builder := c.Delete().Where(snapshot.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &SnapshotDeleteOne{builder}
}

// Query returns a query builder for Snapshot.
func (c *SnapshotClient) Query() *SnapshotQuery {
	return &SnapshotQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeSnapshot},
		inters: c.Interceptors(),
	}
}

// Get returns a Snapshot entity by its id.
func (c *SnapshotClient) Get(ctx context.Context, id uuid.UUID) (*Snapshot, error) {
	return c.Query().Where(snapshot.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *SnapshotClient) GetX(ctx context.Context, id uuid.UUID) *Snapshot {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryEnv queries the env edge of a Snapshot.
func (c *SnapshotClient) QueryEnv(s *Snapshot) *EnvQuery {
	query := (&EnvClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := s.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(snapshot.Table, snapshot.FieldID, id),
			sqlgraph.To(env.Table, env.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, snapshot.EnvTable, snapshot.EnvColumn),
		)
		schemaConfig := s.schemaConfig
		step.To.Schema = schemaConfig.Env
		step.Edge.Schema = schemaConfig.Snapshot
		fromV = sqlgraph.Neighbors(s.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *SnapshotClient) Hooks() []Hook {
	return c.hooks.Snapshot
}

// Interceptors returns the client interceptors.
func (c *SnapshotClient) Interceptors() []Interceptor {
	return c.inters.Snapshot
}

func (c *SnapshotClient) mutate(ctx context.Context, m *SnapshotMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&SnapshotCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&SnapshotUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&SnapshotUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&SnapshotDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("models: unknown Snapshot mutation op: %q", m.Op())
	}
}

// TeamClient is a client for the Team schema.
type TeamClient struct {
	config
}

// NewTeamClient returns a client for the Team from the given config.
func NewTeamClient(c config) *TeamClient {
	return &TeamClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `team.Hooks(f(g(h())))`.
func (c *TeamClient) Use(hooks ...Hook) {
	c.hooks.Team = append(c.hooks.Team, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `team.Intercept(f(g(h())))`.
func (c *TeamClient) Intercept(interceptors ...Interceptor) {
	c.inters.Team = append(c.inters.Team, interceptors...)
}

// Create returns a builder for creating a Team entity.
func (c *TeamClient) Create() *TeamCreate {
	mutation := newTeamMutation(c.config, OpCreate)
	return &TeamCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Team entities.
func (c *TeamClient) CreateBulk(builders ...*TeamCreate) *TeamCreateBulk {
	return &TeamCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *TeamClient) MapCreateBulk(slice any, setFunc func(*TeamCreate, int)) *TeamCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &TeamCreateBulk{err: fmt.Errorf("calling to TeamClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*TeamCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &TeamCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Team.
func (c *TeamClient) Update() *TeamUpdate {
	mutation := newTeamMutation(c.config, OpUpdate)
	return &TeamUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *TeamClient) UpdateOne(t *Team) *TeamUpdateOne {
	mutation := newTeamMutation(c.config, OpUpdateOne, withTeam(t))
	return &TeamUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *TeamClient) UpdateOneID(id uuid.UUID) *TeamUpdateOne {
	mutation := newTeamMutation(c.config, OpUpdateOne, withTeamID(id))
	return &TeamUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Team.
func (c *TeamClient) Delete() *TeamDelete {
	mutation := newTeamMutation(c.config, OpDelete)
	return &TeamDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *TeamClient) DeleteOne(t *Team) *TeamDeleteOne {
	return c.DeleteOneID(t.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *TeamClient) DeleteOneID(id uuid.UUID) *TeamDeleteOne {
	builder := c.Delete().Where(team.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &TeamDeleteOne{builder}
}

// Query returns a query builder for Team.
func (c *TeamClient) Query() *TeamQuery {
	return &TeamQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeTeam},
		inters: c.Interceptors(),
	}
}

// Get returns a Team entity by its id.
func (c *TeamClient) Get(ctx context.Context, id uuid.UUID) (*Team, error) {
	return c.Query().Where(team.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *TeamClient) GetX(ctx context.Context, id uuid.UUID) *Team {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryUsers queries the users edge of a Team.
func (c *TeamClient) QueryUsers(t *Team) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(team.Table, team.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, false, team.UsersTable, team.UsersPrimaryKey...),
		)
		schemaConfig := t.schemaConfig
		step.To.Schema = schemaConfig.User
		step.Edge.Schema = schemaConfig.UsersTeams
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryTeamAPIKeys queries the team_api_keys edge of a Team.
func (c *TeamClient) QueryTeamAPIKeys(t *Team) *TeamAPIKeyQuery {
	query := (&TeamAPIKeyClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(team.Table, team.FieldID, id),
			sqlgraph.To(teamapikey.Table, teamapikey.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, team.TeamAPIKeysTable, team.TeamAPIKeysColumn),
		)
		schemaConfig := t.schemaConfig
		step.To.Schema = schemaConfig.TeamAPIKey
		step.Edge.Schema = schemaConfig.TeamAPIKey
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryTeamTier queries the team_tier edge of a Team.
func (c *TeamClient) QueryTeamTier(t *Team) *TierQuery {
	query := (&TierClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(team.Table, team.FieldID, id),
			sqlgraph.To(tier.Table, tier.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, team.TeamTierTable, team.TeamTierColumn),
		)
		schemaConfig := t.schemaConfig
		step.To.Schema = schemaConfig.Tier
		step.Edge.Schema = schemaConfig.Team
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryEnvs queries the envs edge of a Team.
func (c *TeamClient) QueryEnvs(t *Team) *EnvQuery {
	query := (&EnvClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(team.Table, team.FieldID, id),
			sqlgraph.To(env.Table, env.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, team.EnvsTable, team.EnvsColumn),
		)
		schemaConfig := t.schemaConfig
		step.To.Schema = schemaConfig.Env
		step.Edge.Schema = schemaConfig.Env
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryUsersTeams queries the users_teams edge of a Team.
func (c *TeamClient) QueryUsersTeams(t *Team) *UsersTeamsQuery {
	query := (&UsersTeamsClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(team.Table, team.FieldID, id),
			sqlgraph.To(usersteams.Table, usersteams.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, team.UsersTeamsTable, team.UsersTeamsColumn),
		)
		schemaConfig := t.schemaConfig
		step.To.Schema = schemaConfig.UsersTeams
		step.Edge.Schema = schemaConfig.UsersTeams
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *TeamClient) Hooks() []Hook {
	return c.hooks.Team
}

// Interceptors returns the client interceptors.
func (c *TeamClient) Interceptors() []Interceptor {
	return c.inters.Team
}

func (c *TeamClient) mutate(ctx context.Context, m *TeamMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&TeamCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&TeamUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&TeamUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&TeamDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("models: unknown Team mutation op: %q", m.Op())
	}
}

// TeamAPIKeyClient is a client for the TeamAPIKey schema.
type TeamAPIKeyClient struct {
	config
}

// NewTeamAPIKeyClient returns a client for the TeamAPIKey from the given config.
func NewTeamAPIKeyClient(c config) *TeamAPIKeyClient {
	return &TeamAPIKeyClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `teamapikey.Hooks(f(g(h())))`.
func (c *TeamAPIKeyClient) Use(hooks ...Hook) {
	c.hooks.TeamAPIKey = append(c.hooks.TeamAPIKey, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `teamapikey.Intercept(f(g(h())))`.
func (c *TeamAPIKeyClient) Intercept(interceptors ...Interceptor) {
	c.inters.TeamAPIKey = append(c.inters.TeamAPIKey, interceptors...)
}

// Create returns a builder for creating a TeamAPIKey entity.
func (c *TeamAPIKeyClient) Create() *TeamAPIKeyCreate {
	mutation := newTeamAPIKeyMutation(c.config, OpCreate)
	return &TeamAPIKeyCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of TeamAPIKey entities.
func (c *TeamAPIKeyClient) CreateBulk(builders ...*TeamAPIKeyCreate) *TeamAPIKeyCreateBulk {
	return &TeamAPIKeyCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *TeamAPIKeyClient) MapCreateBulk(slice any, setFunc func(*TeamAPIKeyCreate, int)) *TeamAPIKeyCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &TeamAPIKeyCreateBulk{err: fmt.Errorf("calling to TeamAPIKeyClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*TeamAPIKeyCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &TeamAPIKeyCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for TeamAPIKey.
func (c *TeamAPIKeyClient) Update() *TeamAPIKeyUpdate {
	mutation := newTeamAPIKeyMutation(c.config, OpUpdate)
	return &TeamAPIKeyUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *TeamAPIKeyClient) UpdateOne(tak *TeamAPIKey) *TeamAPIKeyUpdateOne {
	mutation := newTeamAPIKeyMutation(c.config, OpUpdateOne, withTeamAPIKey(tak))
	return &TeamAPIKeyUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *TeamAPIKeyClient) UpdateOneID(id uuid.UUID) *TeamAPIKeyUpdateOne {
	mutation := newTeamAPIKeyMutation(c.config, OpUpdateOne, withTeamAPIKeyID(id))
	return &TeamAPIKeyUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for TeamAPIKey.
func (c *TeamAPIKeyClient) Delete() *TeamAPIKeyDelete {
	mutation := newTeamAPIKeyMutation(c.config, OpDelete)
	return &TeamAPIKeyDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *TeamAPIKeyClient) DeleteOne(tak *TeamAPIKey) *TeamAPIKeyDeleteOne {
	return c.DeleteOneID(tak.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *TeamAPIKeyClient) DeleteOneID(id uuid.UUID) *TeamAPIKeyDeleteOne {
	builder := c.Delete().Where(teamapikey.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &TeamAPIKeyDeleteOne{builder}
}

// Query returns a query builder for TeamAPIKey.
func (c *TeamAPIKeyClient) Query() *TeamAPIKeyQuery {
	return &TeamAPIKeyQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeTeamAPIKey},
		inters: c.Interceptors(),
	}
}

// Get returns a TeamAPIKey entity by its id.
func (c *TeamAPIKeyClient) Get(ctx context.Context, id uuid.UUID) (*TeamAPIKey, error) {
	return c.Query().Where(teamapikey.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *TeamAPIKeyClient) GetX(ctx context.Context, id uuid.UUID) *TeamAPIKey {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryTeam queries the team edge of a TeamAPIKey.
func (c *TeamAPIKeyClient) QueryTeam(tak *TeamAPIKey) *TeamQuery {
	query := (&TeamClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := tak.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(teamapikey.Table, teamapikey.FieldID, id),
			sqlgraph.To(team.Table, team.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, teamapikey.TeamTable, teamapikey.TeamColumn),
		)
		schemaConfig := tak.schemaConfig
		step.To.Schema = schemaConfig.Team
		step.Edge.Schema = schemaConfig.TeamAPIKey
		fromV = sqlgraph.Neighbors(tak.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryCreator queries the creator edge of a TeamAPIKey.
func (c *TeamAPIKeyClient) QueryCreator(tak *TeamAPIKey) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := tak.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(teamapikey.Table, teamapikey.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, teamapikey.CreatorTable, teamapikey.CreatorColumn),
		)
		schemaConfig := tak.schemaConfig
		step.To.Schema = schemaConfig.User
		step.Edge.Schema = schemaConfig.TeamAPIKey
		fromV = sqlgraph.Neighbors(tak.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *TeamAPIKeyClient) Hooks() []Hook {
	return c.hooks.TeamAPIKey
}

// Interceptors returns the client interceptors.
func (c *TeamAPIKeyClient) Interceptors() []Interceptor {
	return c.inters.TeamAPIKey
}

func (c *TeamAPIKeyClient) mutate(ctx context.Context, m *TeamAPIKeyMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&TeamAPIKeyCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&TeamAPIKeyUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&TeamAPIKeyUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&TeamAPIKeyDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("models: unknown TeamAPIKey mutation op: %q", m.Op())
	}
}

// TierClient is a client for the Tier schema.
type TierClient struct {
	config
}

// NewTierClient returns a client for the Tier from the given config.
func NewTierClient(c config) *TierClient {
	return &TierClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `tier.Hooks(f(g(h())))`.
func (c *TierClient) Use(hooks ...Hook) {
	c.hooks.Tier = append(c.hooks.Tier, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `tier.Intercept(f(g(h())))`.
func (c *TierClient) Intercept(interceptors ...Interceptor) {
	c.inters.Tier = append(c.inters.Tier, interceptors...)
}

// Create returns a builder for creating a Tier entity.
func (c *TierClient) Create() *TierCreate {
	mutation := newTierMutation(c.config, OpCreate)
	return &TierCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Tier entities.
func (c *TierClient) CreateBulk(builders ...*TierCreate) *TierCreateBulk {
	return &TierCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *TierClient) MapCreateBulk(slice any, setFunc func(*TierCreate, int)) *TierCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &TierCreateBulk{err: fmt.Errorf("calling to TierClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*TierCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &TierCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Tier.
func (c *TierClient) Update() *TierUpdate {
	mutation := newTierMutation(c.config, OpUpdate)
	return &TierUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *TierClient) UpdateOne(t *Tier) *TierUpdateOne {
	mutation := newTierMutation(c.config, OpUpdateOne, withTier(t))
	return &TierUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *TierClient) UpdateOneID(id string) *TierUpdateOne {
	mutation := newTierMutation(c.config, OpUpdateOne, withTierID(id))
	return &TierUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Tier.
func (c *TierClient) Delete() *TierDelete {
	mutation := newTierMutation(c.config, OpDelete)
	return &TierDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *TierClient) DeleteOne(t *Tier) *TierDeleteOne {
	return c.DeleteOneID(t.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *TierClient) DeleteOneID(id string) *TierDeleteOne {
	builder := c.Delete().Where(tier.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &TierDeleteOne{builder}
}

// Query returns a query builder for Tier.
func (c *TierClient) Query() *TierQuery {
	return &TierQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeTier},
		inters: c.Interceptors(),
	}
}

// Get returns a Tier entity by its id.
func (c *TierClient) Get(ctx context.Context, id string) (*Tier, error) {
	return c.Query().Where(tier.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *TierClient) GetX(ctx context.Context, id string) *Tier {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryTeams queries the teams edge of a Tier.
func (c *TierClient) QueryTeams(t *Tier) *TeamQuery {
	query := (&TeamClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(tier.Table, tier.FieldID, id),
			sqlgraph.To(team.Table, team.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, tier.TeamsTable, tier.TeamsColumn),
		)
		schemaConfig := t.schemaConfig
		step.To.Schema = schemaConfig.Team
		step.Edge.Schema = schemaConfig.Team
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *TierClient) Hooks() []Hook {
	return c.hooks.Tier
}

// Interceptors returns the client interceptors.
func (c *TierClient) Interceptors() []Interceptor {
	return c.inters.Tier
}

func (c *TierClient) mutate(ctx context.Context, m *TierMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&TierCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&TierUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&TierUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&TierDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("models: unknown Tier mutation op: %q", m.Op())
	}
}

// UserClient is a client for the User schema.
type UserClient struct {
	config
}

// NewUserClient returns a client for the User from the given config.
func NewUserClient(c config) *UserClient {
	return &UserClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `user.Hooks(f(g(h())))`.
func (c *UserClient) Use(hooks ...Hook) {
	c.hooks.User = append(c.hooks.User, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `user.Intercept(f(g(h())))`.
func (c *UserClient) Intercept(interceptors ...Interceptor) {
	c.inters.User = append(c.inters.User, interceptors...)
}

// Create returns a builder for creating a User entity.
func (c *UserClient) Create() *UserCreate {
	mutation := newUserMutation(c.config, OpCreate)
	return &UserCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of User entities.
func (c *UserClient) CreateBulk(builders ...*UserCreate) *UserCreateBulk {
	return &UserCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *UserClient) MapCreateBulk(slice any, setFunc func(*UserCreate, int)) *UserCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &UserCreateBulk{err: fmt.Errorf("calling to UserClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*UserCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &UserCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for User.
func (c *UserClient) Update() *UserUpdate {
	mutation := newUserMutation(c.config, OpUpdate)
	return &UserUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *UserClient) UpdateOne(u *User) *UserUpdateOne {
	mutation := newUserMutation(c.config, OpUpdateOne, withUser(u))
	return &UserUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *UserClient) UpdateOneID(id uuid.UUID) *UserUpdateOne {
	mutation := newUserMutation(c.config, OpUpdateOne, withUserID(id))
	return &UserUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for User.
func (c *UserClient) Delete() *UserDelete {
	mutation := newUserMutation(c.config, OpDelete)
	return &UserDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *UserClient) DeleteOne(u *User) *UserDeleteOne {
	return c.DeleteOneID(u.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *UserClient) DeleteOneID(id uuid.UUID) *UserDeleteOne {
	builder := c.Delete().Where(user.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &UserDeleteOne{builder}
}

// Query returns a query builder for User.
func (c *UserClient) Query() *UserQuery {
	return &UserQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeUser},
		inters: c.Interceptors(),
	}
}

// Get returns a User entity by its id.
func (c *UserClient) Get(ctx context.Context, id uuid.UUID) (*User, error) {
	return c.Query().Where(user.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *UserClient) GetX(ctx context.Context, id uuid.UUID) *User {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryTeams queries the teams edge of a User.
func (c *UserClient) QueryTeams(u *User) *TeamQuery {
	query := (&TeamClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(team.Table, team.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, true, user.TeamsTable, user.TeamsPrimaryKey...),
		)
		schemaConfig := u.schemaConfig
		step.To.Schema = schemaConfig.Team
		step.Edge.Schema = schemaConfig.UsersTeams
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryCreatedEnvs queries the created_envs edge of a User.
func (c *UserClient) QueryCreatedEnvs(u *User) *EnvQuery {
	query := (&EnvClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(env.Table, env.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, user.CreatedEnvsTable, user.CreatedEnvsColumn),
		)
		schemaConfig := u.schemaConfig
		step.To.Schema = schemaConfig.Env
		step.Edge.Schema = schemaConfig.Env
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryAccessTokens queries the access_tokens edge of a User.
func (c *UserClient) QueryAccessTokens(u *User) *AccessTokenQuery {
	query := (&AccessTokenClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(accesstoken.Table, accesstoken.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, user.AccessTokensTable, user.AccessTokensColumn),
		)
		schemaConfig := u.schemaConfig
		step.To.Schema = schemaConfig.AccessToken
		step.Edge.Schema = schemaConfig.AccessToken
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryCreatedAPIKeys queries the created_api_keys edge of a User.
func (c *UserClient) QueryCreatedAPIKeys(u *User) *TeamAPIKeyQuery {
	query := (&TeamAPIKeyClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(teamapikey.Table, teamapikey.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, user.CreatedAPIKeysTable, user.CreatedAPIKeysColumn),
		)
		schemaConfig := u.schemaConfig
		step.To.Schema = schemaConfig.TeamAPIKey
		step.Edge.Schema = schemaConfig.TeamAPIKey
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryUsersTeams queries the users_teams edge of a User.
func (c *UserClient) QueryUsersTeams(u *User) *UsersTeamsQuery {
	query := (&UsersTeamsClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(usersteams.Table, usersteams.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, true, user.UsersTeamsTable, user.UsersTeamsColumn),
		)
		schemaConfig := u.schemaConfig
		step.To.Schema = schemaConfig.UsersTeams
		step.Edge.Schema = schemaConfig.UsersTeams
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *UserClient) Hooks() []Hook {
	return c.hooks.User
}

// Interceptors returns the client interceptors.
func (c *UserClient) Interceptors() []Interceptor {
	return c.inters.User
}

func (c *UserClient) mutate(ctx context.Context, m *UserMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&UserCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&UserUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&UserUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&UserDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("models: unknown User mutation op: %q", m.Op())
	}
}

// UsersTeamsClient is a client for the UsersTeams schema.
type UsersTeamsClient struct {
	config
}

// NewUsersTeamsClient returns a client for the UsersTeams from the given config.
func NewUsersTeamsClient(c config) *UsersTeamsClient {
	return &UsersTeamsClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `usersteams.Hooks(f(g(h())))`.
func (c *UsersTeamsClient) Use(hooks ...Hook) {
	c.hooks.UsersTeams = append(c.hooks.UsersTeams, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `usersteams.Intercept(f(g(h())))`.
func (c *UsersTeamsClient) Intercept(interceptors ...Interceptor) {
	c.inters.UsersTeams = append(c.inters.UsersTeams, interceptors...)
}

// Create returns a builder for creating a UsersTeams entity.
func (c *UsersTeamsClient) Create() *UsersTeamsCreate {
	mutation := newUsersTeamsMutation(c.config, OpCreate)
	return &UsersTeamsCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of UsersTeams entities.
func (c *UsersTeamsClient) CreateBulk(builders ...*UsersTeamsCreate) *UsersTeamsCreateBulk {
	return &UsersTeamsCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *UsersTeamsClient) MapCreateBulk(slice any, setFunc func(*UsersTeamsCreate, int)) *UsersTeamsCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &UsersTeamsCreateBulk{err: fmt.Errorf("calling to UsersTeamsClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*UsersTeamsCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &UsersTeamsCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for UsersTeams.
func (c *UsersTeamsClient) Update() *UsersTeamsUpdate {
	mutation := newUsersTeamsMutation(c.config, OpUpdate)
	return &UsersTeamsUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *UsersTeamsClient) UpdateOne(ut *UsersTeams) *UsersTeamsUpdateOne {
	mutation := newUsersTeamsMutation(c.config, OpUpdateOne, withUsersTeams(ut))
	return &UsersTeamsUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *UsersTeamsClient) UpdateOneID(id int) *UsersTeamsUpdateOne {
	mutation := newUsersTeamsMutation(c.config, OpUpdateOne, withUsersTeamsID(id))
	return &UsersTeamsUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for UsersTeams.
func (c *UsersTeamsClient) Delete() *UsersTeamsDelete {
	mutation := newUsersTeamsMutation(c.config, OpDelete)
	return &UsersTeamsDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *UsersTeamsClient) DeleteOne(ut *UsersTeams) *UsersTeamsDeleteOne {
	return c.DeleteOneID(ut.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *UsersTeamsClient) DeleteOneID(id int) *UsersTeamsDeleteOne {
	builder := c.Delete().Where(usersteams.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &UsersTeamsDeleteOne{builder}
}

// Query returns a query builder for UsersTeams.
func (c *UsersTeamsClient) Query() *UsersTeamsQuery {
	return &UsersTeamsQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeUsersTeams},
		inters: c.Interceptors(),
	}
}

// Get returns a UsersTeams entity by its id.
func (c *UsersTeamsClient) Get(ctx context.Context, id int) (*UsersTeams, error) {
	return c.Query().Where(usersteams.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *UsersTeamsClient) GetX(ctx context.Context, id int) *UsersTeams {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryUsers queries the users edge of a UsersTeams.
func (c *UsersTeamsClient) QueryUsers(ut *UsersTeams) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := ut.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(usersteams.Table, usersteams.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, usersteams.UsersTable, usersteams.UsersColumn),
		)
		schemaConfig := ut.schemaConfig
		step.To.Schema = schemaConfig.User
		step.Edge.Schema = schemaConfig.UsersTeams
		fromV = sqlgraph.Neighbors(ut.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryTeams queries the teams edge of a UsersTeams.
func (c *UsersTeamsClient) QueryTeams(ut *UsersTeams) *TeamQuery {
	query := (&TeamClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := ut.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(usersteams.Table, usersteams.FieldID, id),
			sqlgraph.To(team.Table, team.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, usersteams.TeamsTable, usersteams.TeamsColumn),
		)
		schemaConfig := ut.schemaConfig
		step.To.Schema = schemaConfig.Team
		step.Edge.Schema = schemaConfig.UsersTeams
		fromV = sqlgraph.Neighbors(ut.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *UsersTeamsClient) Hooks() []Hook {
	return c.hooks.UsersTeams
}

// Interceptors returns the client interceptors.
func (c *UsersTeamsClient) Interceptors() []Interceptor {
	return c.inters.UsersTeams
}

func (c *UsersTeamsClient) mutate(ctx context.Context, m *UsersTeamsMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&UsersTeamsCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&UsersTeamsUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&UsersTeamsUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&UsersTeamsDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("models: unknown UsersTeams mutation op: %q", m.Op())
	}
}

// hooks and interceptors per client, for fast access.
type (
	hooks struct {
		AccessToken, Env, EnvAlias, EnvBuild, Snapshot, Team, TeamAPIKey, Tier, User,
		UsersTeams []ent.Hook
	}
	inters struct {
		AccessToken, Env, EnvAlias, EnvBuild, Snapshot, Team, TeamAPIKey, Tier, User,
		UsersTeams []ent.Interceptor
	}
)

var (
	// DefaultSchemaConfig represents the default schema names for all tables as defined in ent/schema.
	DefaultSchemaConfig = SchemaConfig{
		AccessToken: tableSchemas[1],
		Env:         tableSchemas[1],
		EnvAlias:    tableSchemas[1],
		EnvBuild:    tableSchemas[1],
		Snapshot:    tableSchemas[1],
		Team:        tableSchemas[1],
		TeamAPIKey:  tableSchemas[1],
		Tier:        tableSchemas[1],
		User:        tableSchemas[0],
		UsersTeams:  tableSchemas[1],
	}
	tableSchemas = [...]string{"auth", "public"}
)

// SchemaConfig represents alternative schema names for all tables
// that can be passed at runtime.
type SchemaConfig = internal.SchemaConfig

// AlternateSchemas allows alternate schema names to be
// passed into ent operations.
func AlternateSchema(schemaConfig SchemaConfig) Option {
	return func(c *config) {
		c.schemaConfig = schemaConfig
	}
}
