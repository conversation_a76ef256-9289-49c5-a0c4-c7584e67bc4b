// Code generated by ent, DO NOT EDIT.

package internal

import "context"

// SchemaConfig represents alternative schema names for all tables
// that can be passed at runtime.
type SchemaConfig struct {
	AccessToken string // AccessToken table.
	Env         string // Env table.
	EnvAlias    string // EnvAlias table.
	EnvBuild    string // EnvBuild table.
	Snapshot    string // Snapshot table.
	Team        string // Team table.
	TeamAPIKey  string // TeamAPIKey table.
	Tier        string // Tier table.
	User        string // User table.
	UsersTeams  string // UsersTeams table.
}

type schemaCtxKey struct{}

// SchemaConfigFromContext returns a SchemaConfig stored inside a context, or empty if there isn't one.
func SchemaConfigFromContext(ctx context.Context) SchemaConfig {
	config, _ := ctx.Value(schemaCtxKey{}).(SchemaConfig)
	return config
}

// NewSchemaConfigContext returns a new context with the given SchemaConfig attached.
func NewSchemaConfigContext(parent context.Context, config SchemaConfig) context.Context {
	return context.WithValue(parent, schemaCtxKey{}, config)
}
