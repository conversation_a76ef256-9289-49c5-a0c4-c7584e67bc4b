// Code generated by ent, DO NOT EDIT.

package snapshot

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/internal"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/predicate"
	"github.com/google/uuid"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldLTE(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldEQ(FieldCreatedAt, v))
}

// BaseEnvID applies equality check predicate on the "base_env_id" field. It's identical to BaseEnvIDEQ.
func BaseEnvID(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldEQ(FieldBaseEnvID, v))
}

// EnvID applies equality check predicate on the "env_id" field. It's identical to EnvIDEQ.
func EnvID(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldEQ(FieldEnvID, v))
}

// SandboxID applies equality check predicate on the "sandbox_id" field. It's identical to SandboxIDEQ.
func SandboxID(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldEQ(FieldSandboxID, v))
}

// SandboxStartedAt applies equality check predicate on the "sandbox_started_at" field. It's identical to SandboxStartedAtEQ.
func SandboxStartedAt(v time.Time) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldEQ(FieldSandboxStartedAt, v))
}

// EnvSecure applies equality check predicate on the "env_secure" field. It's identical to EnvSecureEQ.
func EnvSecure(v bool) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldEQ(FieldEnvSecure, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldLTE(FieldCreatedAt, v))
}

// BaseEnvIDEQ applies the EQ predicate on the "base_env_id" field.
func BaseEnvIDEQ(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldEQ(FieldBaseEnvID, v))
}

// BaseEnvIDNEQ applies the NEQ predicate on the "base_env_id" field.
func BaseEnvIDNEQ(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldNEQ(FieldBaseEnvID, v))
}

// BaseEnvIDIn applies the In predicate on the "base_env_id" field.
func BaseEnvIDIn(vs ...string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldIn(FieldBaseEnvID, vs...))
}

// BaseEnvIDNotIn applies the NotIn predicate on the "base_env_id" field.
func BaseEnvIDNotIn(vs ...string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldNotIn(FieldBaseEnvID, vs...))
}

// BaseEnvIDGT applies the GT predicate on the "base_env_id" field.
func BaseEnvIDGT(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldGT(FieldBaseEnvID, v))
}

// BaseEnvIDGTE applies the GTE predicate on the "base_env_id" field.
func BaseEnvIDGTE(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldGTE(FieldBaseEnvID, v))
}

// BaseEnvIDLT applies the LT predicate on the "base_env_id" field.
func BaseEnvIDLT(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldLT(FieldBaseEnvID, v))
}

// BaseEnvIDLTE applies the LTE predicate on the "base_env_id" field.
func BaseEnvIDLTE(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldLTE(FieldBaseEnvID, v))
}

// BaseEnvIDContains applies the Contains predicate on the "base_env_id" field.
func BaseEnvIDContains(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldContains(FieldBaseEnvID, v))
}

// BaseEnvIDHasPrefix applies the HasPrefix predicate on the "base_env_id" field.
func BaseEnvIDHasPrefix(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldHasPrefix(FieldBaseEnvID, v))
}

// BaseEnvIDHasSuffix applies the HasSuffix predicate on the "base_env_id" field.
func BaseEnvIDHasSuffix(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldHasSuffix(FieldBaseEnvID, v))
}

// BaseEnvIDEqualFold applies the EqualFold predicate on the "base_env_id" field.
func BaseEnvIDEqualFold(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldEqualFold(FieldBaseEnvID, v))
}

// BaseEnvIDContainsFold applies the ContainsFold predicate on the "base_env_id" field.
func BaseEnvIDContainsFold(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldContainsFold(FieldBaseEnvID, v))
}

// EnvIDEQ applies the EQ predicate on the "env_id" field.
func EnvIDEQ(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldEQ(FieldEnvID, v))
}

// EnvIDNEQ applies the NEQ predicate on the "env_id" field.
func EnvIDNEQ(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldNEQ(FieldEnvID, v))
}

// EnvIDIn applies the In predicate on the "env_id" field.
func EnvIDIn(vs ...string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldIn(FieldEnvID, vs...))
}

// EnvIDNotIn applies the NotIn predicate on the "env_id" field.
func EnvIDNotIn(vs ...string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldNotIn(FieldEnvID, vs...))
}

// EnvIDGT applies the GT predicate on the "env_id" field.
func EnvIDGT(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldGT(FieldEnvID, v))
}

// EnvIDGTE applies the GTE predicate on the "env_id" field.
func EnvIDGTE(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldGTE(FieldEnvID, v))
}

// EnvIDLT applies the LT predicate on the "env_id" field.
func EnvIDLT(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldLT(FieldEnvID, v))
}

// EnvIDLTE applies the LTE predicate on the "env_id" field.
func EnvIDLTE(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldLTE(FieldEnvID, v))
}

// EnvIDContains applies the Contains predicate on the "env_id" field.
func EnvIDContains(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldContains(FieldEnvID, v))
}

// EnvIDHasPrefix applies the HasPrefix predicate on the "env_id" field.
func EnvIDHasPrefix(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldHasPrefix(FieldEnvID, v))
}

// EnvIDHasSuffix applies the HasSuffix predicate on the "env_id" field.
func EnvIDHasSuffix(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldHasSuffix(FieldEnvID, v))
}

// EnvIDEqualFold applies the EqualFold predicate on the "env_id" field.
func EnvIDEqualFold(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldEqualFold(FieldEnvID, v))
}

// EnvIDContainsFold applies the ContainsFold predicate on the "env_id" field.
func EnvIDContainsFold(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldContainsFold(FieldEnvID, v))
}

// SandboxIDEQ applies the EQ predicate on the "sandbox_id" field.
func SandboxIDEQ(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldEQ(FieldSandboxID, v))
}

// SandboxIDNEQ applies the NEQ predicate on the "sandbox_id" field.
func SandboxIDNEQ(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldNEQ(FieldSandboxID, v))
}

// SandboxIDIn applies the In predicate on the "sandbox_id" field.
func SandboxIDIn(vs ...string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldIn(FieldSandboxID, vs...))
}

// SandboxIDNotIn applies the NotIn predicate on the "sandbox_id" field.
func SandboxIDNotIn(vs ...string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldNotIn(FieldSandboxID, vs...))
}

// SandboxIDGT applies the GT predicate on the "sandbox_id" field.
func SandboxIDGT(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldGT(FieldSandboxID, v))
}

// SandboxIDGTE applies the GTE predicate on the "sandbox_id" field.
func SandboxIDGTE(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldGTE(FieldSandboxID, v))
}

// SandboxIDLT applies the LT predicate on the "sandbox_id" field.
func SandboxIDLT(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldLT(FieldSandboxID, v))
}

// SandboxIDLTE applies the LTE predicate on the "sandbox_id" field.
func SandboxIDLTE(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldLTE(FieldSandboxID, v))
}

// SandboxIDContains applies the Contains predicate on the "sandbox_id" field.
func SandboxIDContains(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldContains(FieldSandboxID, v))
}

// SandboxIDHasPrefix applies the HasPrefix predicate on the "sandbox_id" field.
func SandboxIDHasPrefix(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldHasPrefix(FieldSandboxID, v))
}

// SandboxIDHasSuffix applies the HasSuffix predicate on the "sandbox_id" field.
func SandboxIDHasSuffix(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldHasSuffix(FieldSandboxID, v))
}

// SandboxIDEqualFold applies the EqualFold predicate on the "sandbox_id" field.
func SandboxIDEqualFold(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldEqualFold(FieldSandboxID, v))
}

// SandboxIDContainsFold applies the ContainsFold predicate on the "sandbox_id" field.
func SandboxIDContainsFold(v string) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldContainsFold(FieldSandboxID, v))
}

// SandboxStartedAtEQ applies the EQ predicate on the "sandbox_started_at" field.
func SandboxStartedAtEQ(v time.Time) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldEQ(FieldSandboxStartedAt, v))
}

// SandboxStartedAtNEQ applies the NEQ predicate on the "sandbox_started_at" field.
func SandboxStartedAtNEQ(v time.Time) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldNEQ(FieldSandboxStartedAt, v))
}

// SandboxStartedAtIn applies the In predicate on the "sandbox_started_at" field.
func SandboxStartedAtIn(vs ...time.Time) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldIn(FieldSandboxStartedAt, vs...))
}

// SandboxStartedAtNotIn applies the NotIn predicate on the "sandbox_started_at" field.
func SandboxStartedAtNotIn(vs ...time.Time) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldNotIn(FieldSandboxStartedAt, vs...))
}

// SandboxStartedAtGT applies the GT predicate on the "sandbox_started_at" field.
func SandboxStartedAtGT(v time.Time) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldGT(FieldSandboxStartedAt, v))
}

// SandboxStartedAtGTE applies the GTE predicate on the "sandbox_started_at" field.
func SandboxStartedAtGTE(v time.Time) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldGTE(FieldSandboxStartedAt, v))
}

// SandboxStartedAtLT applies the LT predicate on the "sandbox_started_at" field.
func SandboxStartedAtLT(v time.Time) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldLT(FieldSandboxStartedAt, v))
}

// SandboxStartedAtLTE applies the LTE predicate on the "sandbox_started_at" field.
func SandboxStartedAtLTE(v time.Time) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldLTE(FieldSandboxStartedAt, v))
}

// EnvSecureEQ applies the EQ predicate on the "env_secure" field.
func EnvSecureEQ(v bool) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldEQ(FieldEnvSecure, v))
}

// EnvSecureNEQ applies the NEQ predicate on the "env_secure" field.
func EnvSecureNEQ(v bool) predicate.Snapshot {
	return predicate.Snapshot(sql.FieldNEQ(FieldEnvSecure, v))
}

// HasEnv applies the HasEdge predicate on the "env" edge.
func HasEnv() predicate.Snapshot {
	return predicate.Snapshot(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, EnvTable, EnvColumn),
		)
		schemaConfig := internal.SchemaConfigFromContext(s.Context())
		step.To.Schema = schemaConfig.Env
		step.Edge.Schema = schemaConfig.Snapshot
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasEnvWith applies the HasEdge predicate on the "env" edge with a given conditions (other predicates).
func HasEnvWith(preds ...predicate.Env) predicate.Snapshot {
	return predicate.Snapshot(func(s *sql.Selector) {
		step := newEnvStep()
		schemaConfig := internal.SchemaConfigFromContext(s.Context())
		step.To.Schema = schemaConfig.Env
		step.Edge.Schema = schemaConfig.Snapshot
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Snapshot) predicate.Snapshot {
	return predicate.Snapshot(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Snapshot) predicate.Snapshot {
	return predicate.Snapshot(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Snapshot) predicate.Snapshot {
	return predicate.Snapshot(sql.NotPredicates(p))
}
