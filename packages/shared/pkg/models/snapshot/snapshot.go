// Code generated by ent, DO NOT EDIT.

package snapshot

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the snapshot type in the database.
	Label = "snapshot"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldBaseEnvID holds the string denoting the base_env_id field in the database.
	FieldBaseEnvID = "base_env_id"
	// FieldEnvID holds the string denoting the env_id field in the database.
	FieldEnvID = "env_id"
	// FieldSandboxID holds the string denoting the sandbox_id field in the database.
	FieldSandboxID = "sandbox_id"
	// FieldMetadata holds the string denoting the metadata field in the database.
	FieldMetadata = "metadata"
	// FieldSandboxStartedAt holds the string denoting the sandbox_started_at field in the database.
	FieldSandboxStartedAt = "sandbox_started_at"
	// FieldEnvSecure holds the string denoting the env_secure field in the database.
	FieldEnvSecure = "env_secure"
	// EdgeEnv holds the string denoting the env edge name in mutations.
	EdgeEnv = "env"
	// Table holds the table name of the snapshot in the database.
	Table = "snapshots"
	// EnvTable is the table that holds the env relation/edge.
	EnvTable = "snapshots"
	// EnvInverseTable is the table name for the Env entity.
	// It exists in this package in order to avoid circular dependency with the "env" package.
	EnvInverseTable = "envs"
	// EnvColumn is the table column denoting the env relation/edge.
	EnvColumn = "env_id"
)

// Columns holds all SQL columns for snapshot fields.
var Columns = []string{
	FieldID,
	FieldCreatedAt,
	FieldBaseEnvID,
	FieldEnvID,
	FieldSandboxID,
	FieldMetadata,
	FieldSandboxStartedAt,
	FieldEnvSecure,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultEnvSecure holds the default value on creation for the "env_secure" field.
	DefaultEnvSecure bool
)

// OrderOption defines the ordering options for the Snapshot queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByBaseEnvID orders the results by the base_env_id field.
func ByBaseEnvID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBaseEnvID, opts...).ToFunc()
}

// ByEnvID orders the results by the env_id field.
func ByEnvID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEnvID, opts...).ToFunc()
}

// BySandboxID orders the results by the sandbox_id field.
func BySandboxID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSandboxID, opts...).ToFunc()
}

// BySandboxStartedAt orders the results by the sandbox_started_at field.
func BySandboxStartedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSandboxStartedAt, opts...).ToFunc()
}

// ByEnvSecure orders the results by the env_secure field.
func ByEnvSecure(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEnvSecure, opts...).ToFunc()
}

// ByEnvField orders the results by env field.
func ByEnvField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newEnvStep(), sql.OrderByField(field, opts...))
	}
}
func newEnvStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(EnvInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, EnvTable, EnvColumn),
	)
}
