// Code generated by ent, DO NOT EDIT.

package env

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the env type in the database.
	Label = "env"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldTeamID holds the string denoting the team_id field in the database.
	FieldTeamID = "team_id"
	// FieldCreatedBy holds the string denoting the created_by field in the database.
	FieldCreatedBy = "created_by"
	// FieldPublic holds the string denoting the public field in the database.
	FieldPublic = "public"
	// FieldBuildCount holds the string denoting the build_count field in the database.
	FieldBuildCount = "build_count"
	// FieldSpawnCount holds the string denoting the spawn_count field in the database.
	FieldSpawnCount = "spawn_count"
	// FieldLastSpawnedAt holds the string denoting the last_spawned_at field in the database.
	FieldLastSpawnedAt = "last_spawned_at"
	// EdgeTeam holds the string denoting the team edge name in mutations.
	EdgeTeam = "team"
	// EdgeCreator holds the string denoting the creator edge name in mutations.
	EdgeCreator = "creator"
	// EdgeEnvAliases holds the string denoting the env_aliases edge name in mutations.
	EdgeEnvAliases = "env_aliases"
	// EdgeBuilds holds the string denoting the builds edge name in mutations.
	EdgeBuilds = "builds"
	// EdgeSnapshots holds the string denoting the snapshots edge name in mutations.
	EdgeSnapshots = "snapshots"
	// EnvAliasFieldID holds the string denoting the ID field of the EnvAlias.
	EnvAliasFieldID = "alias"
	// Table holds the table name of the env in the database.
	Table = "envs"
	// TeamTable is the table that holds the team relation/edge.
	TeamTable = "envs"
	// TeamInverseTable is the table name for the Team entity.
	// It exists in this package in order to avoid circular dependency with the "team" package.
	TeamInverseTable = "teams"
	// TeamColumn is the table column denoting the team relation/edge.
	TeamColumn = "team_id"
	// CreatorTable is the table that holds the creator relation/edge.
	CreatorTable = "envs"
	// CreatorInverseTable is the table name for the User entity.
	// It exists in this package in order to avoid circular dependency with the "user" package.
	CreatorInverseTable = "users"
	// CreatorColumn is the table column denoting the creator relation/edge.
	CreatorColumn = "created_by"
	// EnvAliasesTable is the table that holds the env_aliases relation/edge.
	EnvAliasesTable = "env_aliases"
	// EnvAliasesInverseTable is the table name for the EnvAlias entity.
	// It exists in this package in order to avoid circular dependency with the "envalias" package.
	EnvAliasesInverseTable = "env_aliases"
	// EnvAliasesColumn is the table column denoting the env_aliases relation/edge.
	EnvAliasesColumn = "env_id"
	// BuildsTable is the table that holds the builds relation/edge.
	BuildsTable = "env_builds"
	// BuildsInverseTable is the table name for the EnvBuild entity.
	// It exists in this package in order to avoid circular dependency with the "envbuild" package.
	BuildsInverseTable = "env_builds"
	// BuildsColumn is the table column denoting the builds relation/edge.
	BuildsColumn = "env_id"
	// SnapshotsTable is the table that holds the snapshots relation/edge.
	SnapshotsTable = "snapshots"
	// SnapshotsInverseTable is the table name for the Snapshot entity.
	// It exists in this package in order to avoid circular dependency with the "snapshot" package.
	SnapshotsInverseTable = "snapshots"
	// SnapshotsColumn is the table column denoting the snapshots relation/edge.
	SnapshotsColumn = "env_id"
)

// Columns holds all SQL columns for env fields.
var Columns = []string{
	FieldID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldTeamID,
	FieldCreatedBy,
	FieldPublic,
	FieldBuildCount,
	FieldSpawnCount,
	FieldLastSpawnedAt,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// DefaultBuildCount holds the default value on creation for the "build_count" field.
	DefaultBuildCount int32
	// DefaultSpawnCount holds the default value on creation for the "spawn_count" field.
	DefaultSpawnCount int64
)

// OrderOption defines the ordering options for the Env queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByTeamID orders the results by the team_id field.
func ByTeamID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTeamID, opts...).ToFunc()
}

// ByCreatedBy orders the results by the created_by field.
func ByCreatedBy(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedBy, opts...).ToFunc()
}

// ByPublic orders the results by the public field.
func ByPublic(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPublic, opts...).ToFunc()
}

// ByBuildCount orders the results by the build_count field.
func ByBuildCount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBuildCount, opts...).ToFunc()
}

// BySpawnCount orders the results by the spawn_count field.
func BySpawnCount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSpawnCount, opts...).ToFunc()
}

// ByLastSpawnedAt orders the results by the last_spawned_at field.
func ByLastSpawnedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldLastSpawnedAt, opts...).ToFunc()
}

// ByTeamField orders the results by team field.
func ByTeamField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newTeamStep(), sql.OrderByField(field, opts...))
	}
}

// ByCreatorField orders the results by creator field.
func ByCreatorField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newCreatorStep(), sql.OrderByField(field, opts...))
	}
}

// ByEnvAliasesCount orders the results by env_aliases count.
func ByEnvAliasesCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newEnvAliasesStep(), opts...)
	}
}

// ByEnvAliases orders the results by env_aliases terms.
func ByEnvAliases(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newEnvAliasesStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByBuildsCount orders the results by builds count.
func ByBuildsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newBuildsStep(), opts...)
	}
}

// ByBuilds orders the results by builds terms.
func ByBuilds(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newBuildsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// BySnapshotsCount orders the results by snapshots count.
func BySnapshotsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newSnapshotsStep(), opts...)
	}
}

// BySnapshots orders the results by snapshots terms.
func BySnapshots(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newSnapshotsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}
func newTeamStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(TeamInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, TeamTable, TeamColumn),
	)
}
func newCreatorStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(CreatorInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, CreatorTable, CreatorColumn),
	)
}
func newEnvAliasesStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(EnvAliasesInverseTable, EnvAliasFieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, EnvAliasesTable, EnvAliasesColumn),
	)
}
func newBuildsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(BuildsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, BuildsTable, BuildsColumn),
	)
}
func newSnapshotsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(SnapshotsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, SnapshotsTable, SnapshotsColumn),
	)
}
