// Code generated by ent, DO NOT EDIT.

package models

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/internal"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/predicate"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/usersteams"
)

// UsersTeamsDelete is the builder for deleting a UsersTeams entity.
type UsersTeamsDelete struct {
	config
	hooks    []Hook
	mutation *UsersTeamsMutation
}

// Where appends a list predicates to the UsersTeamsDelete builder.
func (utd *UsersTeamsDelete) Where(ps ...predicate.UsersTeams) *UsersTeamsDelete {
	utd.mutation.Where(ps...)
	return utd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (utd *UsersTeamsDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, utd.sqlExec, utd.mutation, utd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (utd *UsersTeamsDelete) ExecX(ctx context.Context) int {
	n, err := utd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (utd *UsersTeamsDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(usersteams.Table, sqlgraph.NewFieldSpec(usersteams.FieldID, field.TypeInt))
	_spec.Node.Schema = utd.schemaConfig.UsersTeams
	ctx = internal.NewSchemaConfigContext(ctx, utd.schemaConfig)
	if ps := utd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, utd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	utd.mutation.done = true
	return affected, err
}

// UsersTeamsDeleteOne is the builder for deleting a single UsersTeams entity.
type UsersTeamsDeleteOne struct {
	utd *UsersTeamsDelete
}

// Where appends a list predicates to the UsersTeamsDelete builder.
func (utdo *UsersTeamsDeleteOne) Where(ps ...predicate.UsersTeams) *UsersTeamsDeleteOne {
	utdo.utd.mutation.Where(ps...)
	return utdo
}

// Exec executes the deletion query.
func (utdo *UsersTeamsDeleteOne) Exec(ctx context.Context) error {
	n, err := utdo.utd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{usersteams.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (utdo *UsersTeamsDeleteOne) ExecX(ctx context.Context) {
	if err := utdo.Exec(ctx); err != nil {
		panic(err)
	}
}
