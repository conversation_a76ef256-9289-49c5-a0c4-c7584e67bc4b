// Code generated by ent, DO NOT EDIT.

package models

import (
	"fmt"
	"strings"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/team"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/user"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/usersteams"
	"github.com/google/uuid"
)

// UsersTeams is the model entity for the UsersTeams schema.
type UsersTeams struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// UserID holds the value of the "user_id" field.
	UserID uuid.UUID `json:"user_id,omitempty"`
	// TeamID holds the value of the "team_id" field.
	TeamID uuid.UUID `json:"team_id,omitempty"`
	// IsDefault holds the value of the "is_default" field.
	IsDefault bool `json:"is_default,omitempty"`
	// <PERSON>s holds the relations/edges for other nodes in the graph.
	// The values are being populated by the UsersTeamsQuery when eager-loading is set.
	Edges        UsersTeamsEdges `json:"edges"`
	selectValues sql.SelectValues
}

// UsersTeamsEdges holds the relations/edges for other nodes in the graph.
type UsersTeamsEdges struct {
	// Users holds the value of the users edge.
	Users *User `json:"users,omitempty"`
	// Teams holds the value of the teams edge.
	Teams *Team `json:"teams,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [2]bool
}

// UsersOrErr returns the Users value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e UsersTeamsEdges) UsersOrErr() (*User, error) {
	if e.loadedTypes[0] {
		if e.Users == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: user.Label}
		}
		return e.Users, nil
	}
	return nil, &NotLoadedError{edge: "users"}
}

// TeamsOrErr returns the Teams value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e UsersTeamsEdges) TeamsOrErr() (*Team, error) {
	if e.loadedTypes[1] {
		if e.Teams == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: team.Label}
		}
		return e.Teams, nil
	}
	return nil, &NotLoadedError{edge: "teams"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*UsersTeams) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case usersteams.FieldIsDefault:
			values[i] = new(sql.NullBool)
		case usersteams.FieldID:
			values[i] = new(sql.NullInt64)
		case usersteams.FieldUserID, usersteams.FieldTeamID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the UsersTeams fields.
func (ut *UsersTeams) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case usersteams.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			ut.ID = int(value.Int64)
		case usersteams.FieldUserID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value != nil {
				ut.UserID = *value
			}
		case usersteams.FieldTeamID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field team_id", values[i])
			} else if value != nil {
				ut.TeamID = *value
			}
		case usersteams.FieldIsDefault:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_default", values[i])
			} else if value.Valid {
				ut.IsDefault = value.Bool
			}
		default:
			ut.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the UsersTeams.
// This includes values selected through modifiers, order, etc.
func (ut *UsersTeams) Value(name string) (ent.Value, error) {
	return ut.selectValues.Get(name)
}

// QueryUsers queries the "users" edge of the UsersTeams entity.
func (ut *UsersTeams) QueryUsers() *UserQuery {
	return NewUsersTeamsClient(ut.config).QueryUsers(ut)
}

// QueryTeams queries the "teams" edge of the UsersTeams entity.
func (ut *UsersTeams) QueryTeams() *TeamQuery {
	return NewUsersTeamsClient(ut.config).QueryTeams(ut)
}

// Update returns a builder for updating this UsersTeams.
// Note that you need to call UsersTeams.Unwrap() before calling this method if this UsersTeams
// was returned from a transaction, and the transaction was committed or rolled back.
func (ut *UsersTeams) Update() *UsersTeamsUpdateOne {
	return NewUsersTeamsClient(ut.config).UpdateOne(ut)
}

// Unwrap unwraps the UsersTeams entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (ut *UsersTeams) Unwrap() *UsersTeams {
	_tx, ok := ut.config.driver.(*txDriver)
	if !ok {
		panic("models: UsersTeams is not a transactional entity")
	}
	ut.config.driver = _tx.drv
	return ut
}

// String implements the fmt.Stringer.
func (ut *UsersTeams) String() string {
	var builder strings.Builder
	builder.WriteString("UsersTeams(")
	builder.WriteString(fmt.Sprintf("id=%v, ", ut.ID))
	builder.WriteString("user_id=")
	builder.WriteString(fmt.Sprintf("%v", ut.UserID))
	builder.WriteString(", ")
	builder.WriteString("team_id=")
	builder.WriteString(fmt.Sprintf("%v", ut.TeamID))
	builder.WriteString(", ")
	builder.WriteString("is_default=")
	builder.WriteString(fmt.Sprintf("%v", ut.IsDefault))
	builder.WriteByte(')')
	return builder.String()
}

// UsersTeamsSlice is a parsable slice of UsersTeams.
type UsersTeamsSlice []*UsersTeams
