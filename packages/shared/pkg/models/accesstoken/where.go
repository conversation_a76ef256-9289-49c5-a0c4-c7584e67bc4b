// Code generated by ent, DO NOT EDIT.

package accesstoken

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/internal"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/predicate"
	"github.com/google/uuid"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldLTE(FieldID, id))
}

// AccessToken applies equality check predicate on the "access_token" field. It's identical to AccessTokenEQ.
func AccessToken(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldEQ(FieldAccessToken, v))
}

// AccessTokenHash applies equality check predicate on the "access_token_hash" field. It's identical to AccessTokenHashEQ.
func AccessTokenHash(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldEQ(FieldAccessTokenHash, v))
}

// AccessTokenPrefix applies equality check predicate on the "access_token_prefix" field. It's identical to AccessTokenPrefixEQ.
func AccessTokenPrefix(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldEQ(FieldAccessTokenPrefix, v))
}

// AccessTokenLength applies equality check predicate on the "access_token_length" field. It's identical to AccessTokenLengthEQ.
func AccessTokenLength(v int) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldEQ(FieldAccessTokenLength, v))
}

// AccessTokenMaskPrefix applies equality check predicate on the "access_token_mask_prefix" field. It's identical to AccessTokenMaskPrefixEQ.
func AccessTokenMaskPrefix(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldEQ(FieldAccessTokenMaskPrefix, v))
}

// AccessTokenMaskSuffix applies equality check predicate on the "access_token_mask_suffix" field. It's identical to AccessTokenMaskSuffixEQ.
func AccessTokenMaskSuffix(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldEQ(FieldAccessTokenMaskSuffix, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldEQ(FieldName, v))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v uuid.UUID) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldEQ(FieldUserID, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldEQ(FieldCreatedAt, v))
}

// AccessTokenEQ applies the EQ predicate on the "access_token" field.
func AccessTokenEQ(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldEQ(FieldAccessToken, v))
}

// AccessTokenNEQ applies the NEQ predicate on the "access_token" field.
func AccessTokenNEQ(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldNEQ(FieldAccessToken, v))
}

// AccessTokenIn applies the In predicate on the "access_token" field.
func AccessTokenIn(vs ...string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldIn(FieldAccessToken, vs...))
}

// AccessTokenNotIn applies the NotIn predicate on the "access_token" field.
func AccessTokenNotIn(vs ...string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldNotIn(FieldAccessToken, vs...))
}

// AccessTokenGT applies the GT predicate on the "access_token" field.
func AccessTokenGT(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldGT(FieldAccessToken, v))
}

// AccessTokenGTE applies the GTE predicate on the "access_token" field.
func AccessTokenGTE(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldGTE(FieldAccessToken, v))
}

// AccessTokenLT applies the LT predicate on the "access_token" field.
func AccessTokenLT(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldLT(FieldAccessToken, v))
}

// AccessTokenLTE applies the LTE predicate on the "access_token" field.
func AccessTokenLTE(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldLTE(FieldAccessToken, v))
}

// AccessTokenContains applies the Contains predicate on the "access_token" field.
func AccessTokenContains(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldContains(FieldAccessToken, v))
}

// AccessTokenHasPrefix applies the HasPrefix predicate on the "access_token" field.
func AccessTokenHasPrefix(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldHasPrefix(FieldAccessToken, v))
}

// AccessTokenHasSuffix applies the HasSuffix predicate on the "access_token" field.
func AccessTokenHasSuffix(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldHasSuffix(FieldAccessToken, v))
}

// AccessTokenEqualFold applies the EqualFold predicate on the "access_token" field.
func AccessTokenEqualFold(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldEqualFold(FieldAccessToken, v))
}

// AccessTokenContainsFold applies the ContainsFold predicate on the "access_token" field.
func AccessTokenContainsFold(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldContainsFold(FieldAccessToken, v))
}

// AccessTokenHashEQ applies the EQ predicate on the "access_token_hash" field.
func AccessTokenHashEQ(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldEQ(FieldAccessTokenHash, v))
}

// AccessTokenHashNEQ applies the NEQ predicate on the "access_token_hash" field.
func AccessTokenHashNEQ(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldNEQ(FieldAccessTokenHash, v))
}

// AccessTokenHashIn applies the In predicate on the "access_token_hash" field.
func AccessTokenHashIn(vs ...string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldIn(FieldAccessTokenHash, vs...))
}

// AccessTokenHashNotIn applies the NotIn predicate on the "access_token_hash" field.
func AccessTokenHashNotIn(vs ...string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldNotIn(FieldAccessTokenHash, vs...))
}

// AccessTokenHashGT applies the GT predicate on the "access_token_hash" field.
func AccessTokenHashGT(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldGT(FieldAccessTokenHash, v))
}

// AccessTokenHashGTE applies the GTE predicate on the "access_token_hash" field.
func AccessTokenHashGTE(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldGTE(FieldAccessTokenHash, v))
}

// AccessTokenHashLT applies the LT predicate on the "access_token_hash" field.
func AccessTokenHashLT(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldLT(FieldAccessTokenHash, v))
}

// AccessTokenHashLTE applies the LTE predicate on the "access_token_hash" field.
func AccessTokenHashLTE(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldLTE(FieldAccessTokenHash, v))
}

// AccessTokenHashContains applies the Contains predicate on the "access_token_hash" field.
func AccessTokenHashContains(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldContains(FieldAccessTokenHash, v))
}

// AccessTokenHashHasPrefix applies the HasPrefix predicate on the "access_token_hash" field.
func AccessTokenHashHasPrefix(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldHasPrefix(FieldAccessTokenHash, v))
}

// AccessTokenHashHasSuffix applies the HasSuffix predicate on the "access_token_hash" field.
func AccessTokenHashHasSuffix(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldHasSuffix(FieldAccessTokenHash, v))
}

// AccessTokenHashEqualFold applies the EqualFold predicate on the "access_token_hash" field.
func AccessTokenHashEqualFold(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldEqualFold(FieldAccessTokenHash, v))
}

// AccessTokenHashContainsFold applies the ContainsFold predicate on the "access_token_hash" field.
func AccessTokenHashContainsFold(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldContainsFold(FieldAccessTokenHash, v))
}

// AccessTokenPrefixEQ applies the EQ predicate on the "access_token_prefix" field.
func AccessTokenPrefixEQ(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldEQ(FieldAccessTokenPrefix, v))
}

// AccessTokenPrefixNEQ applies the NEQ predicate on the "access_token_prefix" field.
func AccessTokenPrefixNEQ(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldNEQ(FieldAccessTokenPrefix, v))
}

// AccessTokenPrefixIn applies the In predicate on the "access_token_prefix" field.
func AccessTokenPrefixIn(vs ...string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldIn(FieldAccessTokenPrefix, vs...))
}

// AccessTokenPrefixNotIn applies the NotIn predicate on the "access_token_prefix" field.
func AccessTokenPrefixNotIn(vs ...string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldNotIn(FieldAccessTokenPrefix, vs...))
}

// AccessTokenPrefixGT applies the GT predicate on the "access_token_prefix" field.
func AccessTokenPrefixGT(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldGT(FieldAccessTokenPrefix, v))
}

// AccessTokenPrefixGTE applies the GTE predicate on the "access_token_prefix" field.
func AccessTokenPrefixGTE(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldGTE(FieldAccessTokenPrefix, v))
}

// AccessTokenPrefixLT applies the LT predicate on the "access_token_prefix" field.
func AccessTokenPrefixLT(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldLT(FieldAccessTokenPrefix, v))
}

// AccessTokenPrefixLTE applies the LTE predicate on the "access_token_prefix" field.
func AccessTokenPrefixLTE(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldLTE(FieldAccessTokenPrefix, v))
}

// AccessTokenPrefixContains applies the Contains predicate on the "access_token_prefix" field.
func AccessTokenPrefixContains(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldContains(FieldAccessTokenPrefix, v))
}

// AccessTokenPrefixHasPrefix applies the HasPrefix predicate on the "access_token_prefix" field.
func AccessTokenPrefixHasPrefix(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldHasPrefix(FieldAccessTokenPrefix, v))
}

// AccessTokenPrefixHasSuffix applies the HasSuffix predicate on the "access_token_prefix" field.
func AccessTokenPrefixHasSuffix(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldHasSuffix(FieldAccessTokenPrefix, v))
}

// AccessTokenPrefixEqualFold applies the EqualFold predicate on the "access_token_prefix" field.
func AccessTokenPrefixEqualFold(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldEqualFold(FieldAccessTokenPrefix, v))
}

// AccessTokenPrefixContainsFold applies the ContainsFold predicate on the "access_token_prefix" field.
func AccessTokenPrefixContainsFold(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldContainsFold(FieldAccessTokenPrefix, v))
}

// AccessTokenLengthEQ applies the EQ predicate on the "access_token_length" field.
func AccessTokenLengthEQ(v int) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldEQ(FieldAccessTokenLength, v))
}

// AccessTokenLengthNEQ applies the NEQ predicate on the "access_token_length" field.
func AccessTokenLengthNEQ(v int) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldNEQ(FieldAccessTokenLength, v))
}

// AccessTokenLengthIn applies the In predicate on the "access_token_length" field.
func AccessTokenLengthIn(vs ...int) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldIn(FieldAccessTokenLength, vs...))
}

// AccessTokenLengthNotIn applies the NotIn predicate on the "access_token_length" field.
func AccessTokenLengthNotIn(vs ...int) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldNotIn(FieldAccessTokenLength, vs...))
}

// AccessTokenLengthGT applies the GT predicate on the "access_token_length" field.
func AccessTokenLengthGT(v int) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldGT(FieldAccessTokenLength, v))
}

// AccessTokenLengthGTE applies the GTE predicate on the "access_token_length" field.
func AccessTokenLengthGTE(v int) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldGTE(FieldAccessTokenLength, v))
}

// AccessTokenLengthLT applies the LT predicate on the "access_token_length" field.
func AccessTokenLengthLT(v int) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldLT(FieldAccessTokenLength, v))
}

// AccessTokenLengthLTE applies the LTE predicate on the "access_token_length" field.
func AccessTokenLengthLTE(v int) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldLTE(FieldAccessTokenLength, v))
}

// AccessTokenMaskPrefixEQ applies the EQ predicate on the "access_token_mask_prefix" field.
func AccessTokenMaskPrefixEQ(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldEQ(FieldAccessTokenMaskPrefix, v))
}

// AccessTokenMaskPrefixNEQ applies the NEQ predicate on the "access_token_mask_prefix" field.
func AccessTokenMaskPrefixNEQ(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldNEQ(FieldAccessTokenMaskPrefix, v))
}

// AccessTokenMaskPrefixIn applies the In predicate on the "access_token_mask_prefix" field.
func AccessTokenMaskPrefixIn(vs ...string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldIn(FieldAccessTokenMaskPrefix, vs...))
}

// AccessTokenMaskPrefixNotIn applies the NotIn predicate on the "access_token_mask_prefix" field.
func AccessTokenMaskPrefixNotIn(vs ...string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldNotIn(FieldAccessTokenMaskPrefix, vs...))
}

// AccessTokenMaskPrefixGT applies the GT predicate on the "access_token_mask_prefix" field.
func AccessTokenMaskPrefixGT(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldGT(FieldAccessTokenMaskPrefix, v))
}

// AccessTokenMaskPrefixGTE applies the GTE predicate on the "access_token_mask_prefix" field.
func AccessTokenMaskPrefixGTE(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldGTE(FieldAccessTokenMaskPrefix, v))
}

// AccessTokenMaskPrefixLT applies the LT predicate on the "access_token_mask_prefix" field.
func AccessTokenMaskPrefixLT(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldLT(FieldAccessTokenMaskPrefix, v))
}

// AccessTokenMaskPrefixLTE applies the LTE predicate on the "access_token_mask_prefix" field.
func AccessTokenMaskPrefixLTE(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldLTE(FieldAccessTokenMaskPrefix, v))
}

// AccessTokenMaskPrefixContains applies the Contains predicate on the "access_token_mask_prefix" field.
func AccessTokenMaskPrefixContains(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldContains(FieldAccessTokenMaskPrefix, v))
}

// AccessTokenMaskPrefixHasPrefix applies the HasPrefix predicate on the "access_token_mask_prefix" field.
func AccessTokenMaskPrefixHasPrefix(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldHasPrefix(FieldAccessTokenMaskPrefix, v))
}

// AccessTokenMaskPrefixHasSuffix applies the HasSuffix predicate on the "access_token_mask_prefix" field.
func AccessTokenMaskPrefixHasSuffix(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldHasSuffix(FieldAccessTokenMaskPrefix, v))
}

// AccessTokenMaskPrefixEqualFold applies the EqualFold predicate on the "access_token_mask_prefix" field.
func AccessTokenMaskPrefixEqualFold(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldEqualFold(FieldAccessTokenMaskPrefix, v))
}

// AccessTokenMaskPrefixContainsFold applies the ContainsFold predicate on the "access_token_mask_prefix" field.
func AccessTokenMaskPrefixContainsFold(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldContainsFold(FieldAccessTokenMaskPrefix, v))
}

// AccessTokenMaskSuffixEQ applies the EQ predicate on the "access_token_mask_suffix" field.
func AccessTokenMaskSuffixEQ(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldEQ(FieldAccessTokenMaskSuffix, v))
}

// AccessTokenMaskSuffixNEQ applies the NEQ predicate on the "access_token_mask_suffix" field.
func AccessTokenMaskSuffixNEQ(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldNEQ(FieldAccessTokenMaskSuffix, v))
}

// AccessTokenMaskSuffixIn applies the In predicate on the "access_token_mask_suffix" field.
func AccessTokenMaskSuffixIn(vs ...string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldIn(FieldAccessTokenMaskSuffix, vs...))
}

// AccessTokenMaskSuffixNotIn applies the NotIn predicate on the "access_token_mask_suffix" field.
func AccessTokenMaskSuffixNotIn(vs ...string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldNotIn(FieldAccessTokenMaskSuffix, vs...))
}

// AccessTokenMaskSuffixGT applies the GT predicate on the "access_token_mask_suffix" field.
func AccessTokenMaskSuffixGT(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldGT(FieldAccessTokenMaskSuffix, v))
}

// AccessTokenMaskSuffixGTE applies the GTE predicate on the "access_token_mask_suffix" field.
func AccessTokenMaskSuffixGTE(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldGTE(FieldAccessTokenMaskSuffix, v))
}

// AccessTokenMaskSuffixLT applies the LT predicate on the "access_token_mask_suffix" field.
func AccessTokenMaskSuffixLT(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldLT(FieldAccessTokenMaskSuffix, v))
}

// AccessTokenMaskSuffixLTE applies the LTE predicate on the "access_token_mask_suffix" field.
func AccessTokenMaskSuffixLTE(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldLTE(FieldAccessTokenMaskSuffix, v))
}

// AccessTokenMaskSuffixContains applies the Contains predicate on the "access_token_mask_suffix" field.
func AccessTokenMaskSuffixContains(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldContains(FieldAccessTokenMaskSuffix, v))
}

// AccessTokenMaskSuffixHasPrefix applies the HasPrefix predicate on the "access_token_mask_suffix" field.
func AccessTokenMaskSuffixHasPrefix(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldHasPrefix(FieldAccessTokenMaskSuffix, v))
}

// AccessTokenMaskSuffixHasSuffix applies the HasSuffix predicate on the "access_token_mask_suffix" field.
func AccessTokenMaskSuffixHasSuffix(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldHasSuffix(FieldAccessTokenMaskSuffix, v))
}

// AccessTokenMaskSuffixEqualFold applies the EqualFold predicate on the "access_token_mask_suffix" field.
func AccessTokenMaskSuffixEqualFold(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldEqualFold(FieldAccessTokenMaskSuffix, v))
}

// AccessTokenMaskSuffixContainsFold applies the ContainsFold predicate on the "access_token_mask_suffix" field.
func AccessTokenMaskSuffixContainsFold(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldContainsFold(FieldAccessTokenMaskSuffix, v))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldContainsFold(FieldName, v))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v uuid.UUID) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v uuid.UUID) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...uuid.UUID) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...uuid.UUID) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldNotIn(FieldUserID, vs...))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.AccessToken {
	return predicate.AccessToken(sql.FieldLTE(FieldCreatedAt, v))
}

// CreatedAtIsNil applies the IsNil predicate on the "created_at" field.
func CreatedAtIsNil() predicate.AccessToken {
	return predicate.AccessToken(sql.FieldIsNull(FieldCreatedAt))
}

// CreatedAtNotNil applies the NotNil predicate on the "created_at" field.
func CreatedAtNotNil() predicate.AccessToken {
	return predicate.AccessToken(sql.FieldNotNull(FieldCreatedAt))
}

// HasUser applies the HasEdge predicate on the "user" edge.
func HasUser() predicate.AccessToken {
	return predicate.AccessToken(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, UserTable, UserColumn),
		)
		schemaConfig := internal.SchemaConfigFromContext(s.Context())
		step.To.Schema = schemaConfig.User
		step.Edge.Schema = schemaConfig.AccessToken
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasUserWith applies the HasEdge predicate on the "user" edge with a given conditions (other predicates).
func HasUserWith(preds ...predicate.User) predicate.AccessToken {
	return predicate.AccessToken(func(s *sql.Selector) {
		step := newUserStep()
		schemaConfig := internal.SchemaConfigFromContext(s.Context())
		step.To.Schema = schemaConfig.User
		step.Edge.Schema = schemaConfig.AccessToken
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.AccessToken) predicate.AccessToken {
	return predicate.AccessToken(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.AccessToken) predicate.AccessToken {
	return predicate.AccessToken(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.AccessToken) predicate.AccessToken {
	return predicate.AccessToken(sql.NotPredicates(p))
}
