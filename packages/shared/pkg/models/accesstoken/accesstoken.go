// Code generated by ent, DO NOT EDIT.

package accesstoken

import (
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the accesstoken type in the database.
	Label = "access_token"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldAccessToken holds the string denoting the access_token field in the database.
	FieldAccessToken = "access_token"
	// FieldAccessTokenHash holds the string denoting the access_token_hash field in the database.
	FieldAccessTokenHash = "access_token_hash"
	// FieldAccessTokenPrefix holds the string denoting the access_token_prefix field in the database.
	FieldAccessTokenPrefix = "access_token_prefix"
	// FieldAccessTokenLength holds the string denoting the access_token_length field in the database.
	FieldAccessTokenLength = "access_token_length"
	// FieldAccessTokenMaskPrefix holds the string denoting the access_token_mask_prefix field in the database.
	FieldAccessTokenMaskPrefix = "access_token_mask_prefix"
	// FieldAccessTokenMaskSuffix holds the string denoting the access_token_mask_suffix field in the database.
	FieldAccessTokenMaskSuffix = "access_token_mask_suffix"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldUserID holds the string denoting the user_id field in the database.
	FieldUserID = "user_id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// EdgeUser holds the string denoting the user edge name in mutations.
	EdgeUser = "user"
	// Table holds the table name of the accesstoken in the database.
	Table = "access_tokens"
	// UserTable is the table that holds the user relation/edge.
	UserTable = "access_tokens"
	// UserInverseTable is the table name for the User entity.
	// It exists in this package in order to avoid circular dependency with the "user" package.
	UserInverseTable = "users"
	// UserColumn is the table column denoting the user relation/edge.
	UserColumn = "user_id"
)

// Columns holds all SQL columns for accesstoken fields.
var Columns = []string{
	FieldID,
	FieldAccessToken,
	FieldAccessTokenHash,
	FieldAccessTokenPrefix,
	FieldAccessTokenLength,
	FieldAccessTokenMaskPrefix,
	FieldAccessTokenMaskSuffix,
	FieldName,
	FieldUserID,
	FieldCreatedAt,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultName holds the default value on creation for the "name" field.
	DefaultName string
)

// OrderOption defines the ordering options for the AccessToken queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByAccessToken orders the results by the access_token field.
func ByAccessToken(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAccessToken, opts...).ToFunc()
}

// ByAccessTokenHash orders the results by the access_token_hash field.
func ByAccessTokenHash(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAccessTokenHash, opts...).ToFunc()
}

// ByAccessTokenPrefix orders the results by the access_token_prefix field.
func ByAccessTokenPrefix(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAccessTokenPrefix, opts...).ToFunc()
}

// ByAccessTokenLength orders the results by the access_token_length field.
func ByAccessTokenLength(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAccessTokenLength, opts...).ToFunc()
}

// ByAccessTokenMaskPrefix orders the results by the access_token_mask_prefix field.
func ByAccessTokenMaskPrefix(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAccessTokenMaskPrefix, opts...).ToFunc()
}

// ByAccessTokenMaskSuffix orders the results by the access_token_mask_suffix field.
func ByAccessTokenMaskSuffix(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAccessTokenMaskSuffix, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByUserID orders the results by the user_id field.
func ByUserID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUserField orders the results by user field.
func ByUserField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newUserStep(), sql.OrderByField(field, opts...))
	}
}
func newUserStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(UserInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, UserTable, UserColumn),
	)
}
