// Code generated by ent, DO NOT EDIT.

package envbuild

import (
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the envbuild type in the database.
	Label = "env_build"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldFinishedAt holds the string denoting the finished_at field in the database.
	FieldFinishedAt = "finished_at"
	// FieldEnvID holds the string denoting the env_id field in the database.
	FieldEnvID = "env_id"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldDockerfile holds the string denoting the dockerfile field in the database.
	FieldDockerfile = "dockerfile"
	// FieldStartCmd holds the string denoting the start_cmd field in the database.
	FieldStartCmd = "start_cmd"
	// FieldReadyCmd holds the string denoting the ready_cmd field in the database.
	FieldReadyCmd = "ready_cmd"
	// FieldVcpu holds the string denoting the vcpu field in the database.
	FieldVcpu = "vcpu"
	// FieldRAMMB holds the string denoting the ram_mb field in the database.
	FieldRAMMB = "ram_mb"
	// FieldFreeDiskSizeMB holds the string denoting the free_disk_size_mb field in the database.
	FieldFreeDiskSizeMB = "free_disk_size_mb"
	// FieldTotalDiskSizeMB holds the string denoting the total_disk_size_mb field in the database.
	FieldTotalDiskSizeMB = "total_disk_size_mb"
	// FieldKernelVersion holds the string denoting the kernel_version field in the database.
	FieldKernelVersion = "kernel_version"
	// FieldFirecrackerVersion holds the string denoting the firecracker_version field in the database.
	FieldFirecrackerVersion = "firecracker_version"
	// FieldEnvdVersion holds the string denoting the envd_version field in the database.
	FieldEnvdVersion = "envd_version"
	// EdgeEnv holds the string denoting the env edge name in mutations.
	EdgeEnv = "env"
	// Table holds the table name of the envbuild in the database.
	Table = "env_builds"
	// EnvTable is the table that holds the env relation/edge.
	EnvTable = "env_builds"
	// EnvInverseTable is the table name for the Env entity.
	// It exists in this package in order to avoid circular dependency with the "env" package.
	EnvInverseTable = "envs"
	// EnvColumn is the table column denoting the env relation/edge.
	EnvColumn = "env_id"
)

// Columns holds all SQL columns for envbuild fields.
var Columns = []string{
	FieldID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldFinishedAt,
	FieldEnvID,
	FieldStatus,
	FieldDockerfile,
	FieldStartCmd,
	FieldReadyCmd,
	FieldVcpu,
	FieldRAMMB,
	FieldFreeDiskSizeMB,
	FieldTotalDiskSizeMB,
	FieldKernelVersion,
	FieldFirecrackerVersion,
	FieldEnvdVersion,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// DefaultKernelVersion holds the default value on creation for the "kernel_version" field.
	DefaultKernelVersion string
	// DefaultFirecrackerVersion holds the default value on creation for the "firecracker_version" field.
	DefaultFirecrackerVersion string
)

// Status defines the type for the "status" enum field.
type Status string

// StatusWaiting is the default value of the Status enum.
const DefaultStatus = StatusWaiting

// Status values.
const (
	StatusWaiting      Status = "waiting"
	StatusBuilding     Status = "building"
	StatusSnapshotting Status = "snapshotting"
	StatusFailed       Status = "failed"
	StatusSuccess      Status = "success"
	StatusUploaded     Status = "uploaded"
)

func (s Status) String() string {
	return string(s)
}

// StatusValidator is a validator for the "status" field enum values. It is called by the builders before save.
func StatusValidator(s Status) error {
	switch s {
	case StatusWaiting, StatusBuilding, StatusSnapshotting, StatusFailed, StatusSuccess, StatusUploaded:
		return nil
	default:
		return fmt.Errorf("envbuild: invalid enum value for status field: %q", s)
	}
}

// OrderOption defines the ordering options for the EnvBuild queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByFinishedAt orders the results by the finished_at field.
func ByFinishedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFinishedAt, opts...).ToFunc()
}

// ByEnvID orders the results by the env_id field.
func ByEnvID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEnvID, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByDockerfile orders the results by the dockerfile field.
func ByDockerfile(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDockerfile, opts...).ToFunc()
}

// ByStartCmd orders the results by the start_cmd field.
func ByStartCmd(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStartCmd, opts...).ToFunc()
}

// ByReadyCmd orders the results by the ready_cmd field.
func ByReadyCmd(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldReadyCmd, opts...).ToFunc()
}

// ByVcpu orders the results by the vcpu field.
func ByVcpu(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldVcpu, opts...).ToFunc()
}

// ByRAMMB orders the results by the ram_mb field.
func ByRAMMB(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRAMMB, opts...).ToFunc()
}

// ByFreeDiskSizeMB orders the results by the free_disk_size_mb field.
func ByFreeDiskSizeMB(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFreeDiskSizeMB, opts...).ToFunc()
}

// ByTotalDiskSizeMB orders the results by the total_disk_size_mb field.
func ByTotalDiskSizeMB(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTotalDiskSizeMB, opts...).ToFunc()
}

// ByKernelVersion orders the results by the kernel_version field.
func ByKernelVersion(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldKernelVersion, opts...).ToFunc()
}

// ByFirecrackerVersion orders the results by the firecracker_version field.
func ByFirecrackerVersion(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFirecrackerVersion, opts...).ToFunc()
}

// ByEnvdVersion orders the results by the envd_version field.
func ByEnvdVersion(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEnvdVersion, opts...).ToFunc()
}

// ByEnvField orders the results by env field.
func ByEnvField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newEnvStep(), sql.OrderByField(field, opts...))
	}
}
func newEnvStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(EnvInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, EnvTable, EnvColumn),
	)
}
