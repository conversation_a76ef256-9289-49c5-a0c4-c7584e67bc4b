// Code generated by ent, DO NOT EDIT.

package envbuild

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/internal"
	"github.com/e2b-dev/infra/packages/shared/pkg/models/predicate"
	"github.com/google/uuid"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldLTE(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEQ(FieldUpdatedAt, v))
}

// FinishedAt applies equality check predicate on the "finished_at" field. It's identical to FinishedAtEQ.
func FinishedAt(v time.Time) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEQ(FieldFinishedAt, v))
}

// EnvID applies equality check predicate on the "env_id" field. It's identical to EnvIDEQ.
func EnvID(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEQ(FieldEnvID, v))
}

// Dockerfile applies equality check predicate on the "dockerfile" field. It's identical to DockerfileEQ.
func Dockerfile(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEQ(FieldDockerfile, v))
}

// StartCmd applies equality check predicate on the "start_cmd" field. It's identical to StartCmdEQ.
func StartCmd(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEQ(FieldStartCmd, v))
}

// ReadyCmd applies equality check predicate on the "ready_cmd" field. It's identical to ReadyCmdEQ.
func ReadyCmd(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEQ(FieldReadyCmd, v))
}

// Vcpu applies equality check predicate on the "vcpu" field. It's identical to VcpuEQ.
func Vcpu(v int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEQ(FieldVcpu, v))
}

// RAMMB applies equality check predicate on the "ram_mb" field. It's identical to RAMMBEQ.
func RAMMB(v int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEQ(FieldRAMMB, v))
}

// FreeDiskSizeMB applies equality check predicate on the "free_disk_size_mb" field. It's identical to FreeDiskSizeMBEQ.
func FreeDiskSizeMB(v int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEQ(FieldFreeDiskSizeMB, v))
}

// TotalDiskSizeMB applies equality check predicate on the "total_disk_size_mb" field. It's identical to TotalDiskSizeMBEQ.
func TotalDiskSizeMB(v int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEQ(FieldTotalDiskSizeMB, v))
}

// KernelVersion applies equality check predicate on the "kernel_version" field. It's identical to KernelVersionEQ.
func KernelVersion(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEQ(FieldKernelVersion, v))
}

// FirecrackerVersion applies equality check predicate on the "firecracker_version" field. It's identical to FirecrackerVersionEQ.
func FirecrackerVersion(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEQ(FieldFirecrackerVersion, v))
}

// EnvdVersion applies equality check predicate on the "envd_version" field. It's identical to EnvdVersionEQ.
func EnvdVersion(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEQ(FieldEnvdVersion, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldLTE(FieldUpdatedAt, v))
}

// FinishedAtEQ applies the EQ predicate on the "finished_at" field.
func FinishedAtEQ(v time.Time) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEQ(FieldFinishedAt, v))
}

// FinishedAtNEQ applies the NEQ predicate on the "finished_at" field.
func FinishedAtNEQ(v time.Time) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNEQ(FieldFinishedAt, v))
}

// FinishedAtIn applies the In predicate on the "finished_at" field.
func FinishedAtIn(vs ...time.Time) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldIn(FieldFinishedAt, vs...))
}

// FinishedAtNotIn applies the NotIn predicate on the "finished_at" field.
func FinishedAtNotIn(vs ...time.Time) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNotIn(FieldFinishedAt, vs...))
}

// FinishedAtGT applies the GT predicate on the "finished_at" field.
func FinishedAtGT(v time.Time) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldGT(FieldFinishedAt, v))
}

// FinishedAtGTE applies the GTE predicate on the "finished_at" field.
func FinishedAtGTE(v time.Time) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldGTE(FieldFinishedAt, v))
}

// FinishedAtLT applies the LT predicate on the "finished_at" field.
func FinishedAtLT(v time.Time) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldLT(FieldFinishedAt, v))
}

// FinishedAtLTE applies the LTE predicate on the "finished_at" field.
func FinishedAtLTE(v time.Time) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldLTE(FieldFinishedAt, v))
}

// FinishedAtIsNil applies the IsNil predicate on the "finished_at" field.
func FinishedAtIsNil() predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldIsNull(FieldFinishedAt))
}

// FinishedAtNotNil applies the NotNil predicate on the "finished_at" field.
func FinishedAtNotNil() predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNotNull(FieldFinishedAt))
}

// EnvIDEQ applies the EQ predicate on the "env_id" field.
func EnvIDEQ(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEQ(FieldEnvID, v))
}

// EnvIDNEQ applies the NEQ predicate on the "env_id" field.
func EnvIDNEQ(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNEQ(FieldEnvID, v))
}

// EnvIDIn applies the In predicate on the "env_id" field.
func EnvIDIn(vs ...string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldIn(FieldEnvID, vs...))
}

// EnvIDNotIn applies the NotIn predicate on the "env_id" field.
func EnvIDNotIn(vs ...string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNotIn(FieldEnvID, vs...))
}

// EnvIDGT applies the GT predicate on the "env_id" field.
func EnvIDGT(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldGT(FieldEnvID, v))
}

// EnvIDGTE applies the GTE predicate on the "env_id" field.
func EnvIDGTE(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldGTE(FieldEnvID, v))
}

// EnvIDLT applies the LT predicate on the "env_id" field.
func EnvIDLT(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldLT(FieldEnvID, v))
}

// EnvIDLTE applies the LTE predicate on the "env_id" field.
func EnvIDLTE(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldLTE(FieldEnvID, v))
}

// EnvIDContains applies the Contains predicate on the "env_id" field.
func EnvIDContains(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldContains(FieldEnvID, v))
}

// EnvIDHasPrefix applies the HasPrefix predicate on the "env_id" field.
func EnvIDHasPrefix(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldHasPrefix(FieldEnvID, v))
}

// EnvIDHasSuffix applies the HasSuffix predicate on the "env_id" field.
func EnvIDHasSuffix(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldHasSuffix(FieldEnvID, v))
}

// EnvIDIsNil applies the IsNil predicate on the "env_id" field.
func EnvIDIsNil() predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldIsNull(FieldEnvID))
}

// EnvIDNotNil applies the NotNil predicate on the "env_id" field.
func EnvIDNotNil() predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNotNull(FieldEnvID))
}

// EnvIDEqualFold applies the EqualFold predicate on the "env_id" field.
func EnvIDEqualFold(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEqualFold(FieldEnvID, v))
}

// EnvIDContainsFold applies the ContainsFold predicate on the "env_id" field.
func EnvIDContainsFold(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldContainsFold(FieldEnvID, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v Status) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v Status) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...Status) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...Status) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNotIn(FieldStatus, vs...))
}

// DockerfileEQ applies the EQ predicate on the "dockerfile" field.
func DockerfileEQ(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEQ(FieldDockerfile, v))
}

// DockerfileNEQ applies the NEQ predicate on the "dockerfile" field.
func DockerfileNEQ(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNEQ(FieldDockerfile, v))
}

// DockerfileIn applies the In predicate on the "dockerfile" field.
func DockerfileIn(vs ...string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldIn(FieldDockerfile, vs...))
}

// DockerfileNotIn applies the NotIn predicate on the "dockerfile" field.
func DockerfileNotIn(vs ...string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNotIn(FieldDockerfile, vs...))
}

// DockerfileGT applies the GT predicate on the "dockerfile" field.
func DockerfileGT(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldGT(FieldDockerfile, v))
}

// DockerfileGTE applies the GTE predicate on the "dockerfile" field.
func DockerfileGTE(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldGTE(FieldDockerfile, v))
}

// DockerfileLT applies the LT predicate on the "dockerfile" field.
func DockerfileLT(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldLT(FieldDockerfile, v))
}

// DockerfileLTE applies the LTE predicate on the "dockerfile" field.
func DockerfileLTE(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldLTE(FieldDockerfile, v))
}

// DockerfileContains applies the Contains predicate on the "dockerfile" field.
func DockerfileContains(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldContains(FieldDockerfile, v))
}

// DockerfileHasPrefix applies the HasPrefix predicate on the "dockerfile" field.
func DockerfileHasPrefix(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldHasPrefix(FieldDockerfile, v))
}

// DockerfileHasSuffix applies the HasSuffix predicate on the "dockerfile" field.
func DockerfileHasSuffix(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldHasSuffix(FieldDockerfile, v))
}

// DockerfileIsNil applies the IsNil predicate on the "dockerfile" field.
func DockerfileIsNil() predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldIsNull(FieldDockerfile))
}

// DockerfileNotNil applies the NotNil predicate on the "dockerfile" field.
func DockerfileNotNil() predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNotNull(FieldDockerfile))
}

// DockerfileEqualFold applies the EqualFold predicate on the "dockerfile" field.
func DockerfileEqualFold(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEqualFold(FieldDockerfile, v))
}

// DockerfileContainsFold applies the ContainsFold predicate on the "dockerfile" field.
func DockerfileContainsFold(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldContainsFold(FieldDockerfile, v))
}

// StartCmdEQ applies the EQ predicate on the "start_cmd" field.
func StartCmdEQ(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEQ(FieldStartCmd, v))
}

// StartCmdNEQ applies the NEQ predicate on the "start_cmd" field.
func StartCmdNEQ(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNEQ(FieldStartCmd, v))
}

// StartCmdIn applies the In predicate on the "start_cmd" field.
func StartCmdIn(vs ...string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldIn(FieldStartCmd, vs...))
}

// StartCmdNotIn applies the NotIn predicate on the "start_cmd" field.
func StartCmdNotIn(vs ...string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNotIn(FieldStartCmd, vs...))
}

// StartCmdGT applies the GT predicate on the "start_cmd" field.
func StartCmdGT(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldGT(FieldStartCmd, v))
}

// StartCmdGTE applies the GTE predicate on the "start_cmd" field.
func StartCmdGTE(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldGTE(FieldStartCmd, v))
}

// StartCmdLT applies the LT predicate on the "start_cmd" field.
func StartCmdLT(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldLT(FieldStartCmd, v))
}

// StartCmdLTE applies the LTE predicate on the "start_cmd" field.
func StartCmdLTE(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldLTE(FieldStartCmd, v))
}

// StartCmdContains applies the Contains predicate on the "start_cmd" field.
func StartCmdContains(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldContains(FieldStartCmd, v))
}

// StartCmdHasPrefix applies the HasPrefix predicate on the "start_cmd" field.
func StartCmdHasPrefix(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldHasPrefix(FieldStartCmd, v))
}

// StartCmdHasSuffix applies the HasSuffix predicate on the "start_cmd" field.
func StartCmdHasSuffix(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldHasSuffix(FieldStartCmd, v))
}

// StartCmdIsNil applies the IsNil predicate on the "start_cmd" field.
func StartCmdIsNil() predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldIsNull(FieldStartCmd))
}

// StartCmdNotNil applies the NotNil predicate on the "start_cmd" field.
func StartCmdNotNil() predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNotNull(FieldStartCmd))
}

// StartCmdEqualFold applies the EqualFold predicate on the "start_cmd" field.
func StartCmdEqualFold(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEqualFold(FieldStartCmd, v))
}

// StartCmdContainsFold applies the ContainsFold predicate on the "start_cmd" field.
func StartCmdContainsFold(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldContainsFold(FieldStartCmd, v))
}

// ReadyCmdEQ applies the EQ predicate on the "ready_cmd" field.
func ReadyCmdEQ(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEQ(FieldReadyCmd, v))
}

// ReadyCmdNEQ applies the NEQ predicate on the "ready_cmd" field.
func ReadyCmdNEQ(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNEQ(FieldReadyCmd, v))
}

// ReadyCmdIn applies the In predicate on the "ready_cmd" field.
func ReadyCmdIn(vs ...string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldIn(FieldReadyCmd, vs...))
}

// ReadyCmdNotIn applies the NotIn predicate on the "ready_cmd" field.
func ReadyCmdNotIn(vs ...string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNotIn(FieldReadyCmd, vs...))
}

// ReadyCmdGT applies the GT predicate on the "ready_cmd" field.
func ReadyCmdGT(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldGT(FieldReadyCmd, v))
}

// ReadyCmdGTE applies the GTE predicate on the "ready_cmd" field.
func ReadyCmdGTE(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldGTE(FieldReadyCmd, v))
}

// ReadyCmdLT applies the LT predicate on the "ready_cmd" field.
func ReadyCmdLT(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldLT(FieldReadyCmd, v))
}

// ReadyCmdLTE applies the LTE predicate on the "ready_cmd" field.
func ReadyCmdLTE(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldLTE(FieldReadyCmd, v))
}

// ReadyCmdContains applies the Contains predicate on the "ready_cmd" field.
func ReadyCmdContains(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldContains(FieldReadyCmd, v))
}

// ReadyCmdHasPrefix applies the HasPrefix predicate on the "ready_cmd" field.
func ReadyCmdHasPrefix(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldHasPrefix(FieldReadyCmd, v))
}

// ReadyCmdHasSuffix applies the HasSuffix predicate on the "ready_cmd" field.
func ReadyCmdHasSuffix(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldHasSuffix(FieldReadyCmd, v))
}

// ReadyCmdIsNil applies the IsNil predicate on the "ready_cmd" field.
func ReadyCmdIsNil() predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldIsNull(FieldReadyCmd))
}

// ReadyCmdNotNil applies the NotNil predicate on the "ready_cmd" field.
func ReadyCmdNotNil() predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNotNull(FieldReadyCmd))
}

// ReadyCmdEqualFold applies the EqualFold predicate on the "ready_cmd" field.
func ReadyCmdEqualFold(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEqualFold(FieldReadyCmd, v))
}

// ReadyCmdContainsFold applies the ContainsFold predicate on the "ready_cmd" field.
func ReadyCmdContainsFold(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldContainsFold(FieldReadyCmd, v))
}

// VcpuEQ applies the EQ predicate on the "vcpu" field.
func VcpuEQ(v int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEQ(FieldVcpu, v))
}

// VcpuNEQ applies the NEQ predicate on the "vcpu" field.
func VcpuNEQ(v int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNEQ(FieldVcpu, v))
}

// VcpuIn applies the In predicate on the "vcpu" field.
func VcpuIn(vs ...int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldIn(FieldVcpu, vs...))
}

// VcpuNotIn applies the NotIn predicate on the "vcpu" field.
func VcpuNotIn(vs ...int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNotIn(FieldVcpu, vs...))
}

// VcpuGT applies the GT predicate on the "vcpu" field.
func VcpuGT(v int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldGT(FieldVcpu, v))
}

// VcpuGTE applies the GTE predicate on the "vcpu" field.
func VcpuGTE(v int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldGTE(FieldVcpu, v))
}

// VcpuLT applies the LT predicate on the "vcpu" field.
func VcpuLT(v int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldLT(FieldVcpu, v))
}

// VcpuLTE applies the LTE predicate on the "vcpu" field.
func VcpuLTE(v int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldLTE(FieldVcpu, v))
}

// RAMMBEQ applies the EQ predicate on the "ram_mb" field.
func RAMMBEQ(v int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEQ(FieldRAMMB, v))
}

// RAMMBNEQ applies the NEQ predicate on the "ram_mb" field.
func RAMMBNEQ(v int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNEQ(FieldRAMMB, v))
}

// RAMMBIn applies the In predicate on the "ram_mb" field.
func RAMMBIn(vs ...int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldIn(FieldRAMMB, vs...))
}

// RAMMBNotIn applies the NotIn predicate on the "ram_mb" field.
func RAMMBNotIn(vs ...int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNotIn(FieldRAMMB, vs...))
}

// RAMMBGT applies the GT predicate on the "ram_mb" field.
func RAMMBGT(v int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldGT(FieldRAMMB, v))
}

// RAMMBGTE applies the GTE predicate on the "ram_mb" field.
func RAMMBGTE(v int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldGTE(FieldRAMMB, v))
}

// RAMMBLT applies the LT predicate on the "ram_mb" field.
func RAMMBLT(v int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldLT(FieldRAMMB, v))
}

// RAMMBLTE applies the LTE predicate on the "ram_mb" field.
func RAMMBLTE(v int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldLTE(FieldRAMMB, v))
}

// FreeDiskSizeMBEQ applies the EQ predicate on the "free_disk_size_mb" field.
func FreeDiskSizeMBEQ(v int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEQ(FieldFreeDiskSizeMB, v))
}

// FreeDiskSizeMBNEQ applies the NEQ predicate on the "free_disk_size_mb" field.
func FreeDiskSizeMBNEQ(v int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNEQ(FieldFreeDiskSizeMB, v))
}

// FreeDiskSizeMBIn applies the In predicate on the "free_disk_size_mb" field.
func FreeDiskSizeMBIn(vs ...int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldIn(FieldFreeDiskSizeMB, vs...))
}

// FreeDiskSizeMBNotIn applies the NotIn predicate on the "free_disk_size_mb" field.
func FreeDiskSizeMBNotIn(vs ...int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNotIn(FieldFreeDiskSizeMB, vs...))
}

// FreeDiskSizeMBGT applies the GT predicate on the "free_disk_size_mb" field.
func FreeDiskSizeMBGT(v int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldGT(FieldFreeDiskSizeMB, v))
}

// FreeDiskSizeMBGTE applies the GTE predicate on the "free_disk_size_mb" field.
func FreeDiskSizeMBGTE(v int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldGTE(FieldFreeDiskSizeMB, v))
}

// FreeDiskSizeMBLT applies the LT predicate on the "free_disk_size_mb" field.
func FreeDiskSizeMBLT(v int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldLT(FieldFreeDiskSizeMB, v))
}

// FreeDiskSizeMBLTE applies the LTE predicate on the "free_disk_size_mb" field.
func FreeDiskSizeMBLTE(v int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldLTE(FieldFreeDiskSizeMB, v))
}

// TotalDiskSizeMBEQ applies the EQ predicate on the "total_disk_size_mb" field.
func TotalDiskSizeMBEQ(v int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEQ(FieldTotalDiskSizeMB, v))
}

// TotalDiskSizeMBNEQ applies the NEQ predicate on the "total_disk_size_mb" field.
func TotalDiskSizeMBNEQ(v int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNEQ(FieldTotalDiskSizeMB, v))
}

// TotalDiskSizeMBIn applies the In predicate on the "total_disk_size_mb" field.
func TotalDiskSizeMBIn(vs ...int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldIn(FieldTotalDiskSizeMB, vs...))
}

// TotalDiskSizeMBNotIn applies the NotIn predicate on the "total_disk_size_mb" field.
func TotalDiskSizeMBNotIn(vs ...int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNotIn(FieldTotalDiskSizeMB, vs...))
}

// TotalDiskSizeMBGT applies the GT predicate on the "total_disk_size_mb" field.
func TotalDiskSizeMBGT(v int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldGT(FieldTotalDiskSizeMB, v))
}

// TotalDiskSizeMBGTE applies the GTE predicate on the "total_disk_size_mb" field.
func TotalDiskSizeMBGTE(v int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldGTE(FieldTotalDiskSizeMB, v))
}

// TotalDiskSizeMBLT applies the LT predicate on the "total_disk_size_mb" field.
func TotalDiskSizeMBLT(v int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldLT(FieldTotalDiskSizeMB, v))
}

// TotalDiskSizeMBLTE applies the LTE predicate on the "total_disk_size_mb" field.
func TotalDiskSizeMBLTE(v int64) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldLTE(FieldTotalDiskSizeMB, v))
}

// TotalDiskSizeMBIsNil applies the IsNil predicate on the "total_disk_size_mb" field.
func TotalDiskSizeMBIsNil() predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldIsNull(FieldTotalDiskSizeMB))
}

// TotalDiskSizeMBNotNil applies the NotNil predicate on the "total_disk_size_mb" field.
func TotalDiskSizeMBNotNil() predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNotNull(FieldTotalDiskSizeMB))
}

// KernelVersionEQ applies the EQ predicate on the "kernel_version" field.
func KernelVersionEQ(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEQ(FieldKernelVersion, v))
}

// KernelVersionNEQ applies the NEQ predicate on the "kernel_version" field.
func KernelVersionNEQ(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNEQ(FieldKernelVersion, v))
}

// KernelVersionIn applies the In predicate on the "kernel_version" field.
func KernelVersionIn(vs ...string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldIn(FieldKernelVersion, vs...))
}

// KernelVersionNotIn applies the NotIn predicate on the "kernel_version" field.
func KernelVersionNotIn(vs ...string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNotIn(FieldKernelVersion, vs...))
}

// KernelVersionGT applies the GT predicate on the "kernel_version" field.
func KernelVersionGT(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldGT(FieldKernelVersion, v))
}

// KernelVersionGTE applies the GTE predicate on the "kernel_version" field.
func KernelVersionGTE(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldGTE(FieldKernelVersion, v))
}

// KernelVersionLT applies the LT predicate on the "kernel_version" field.
func KernelVersionLT(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldLT(FieldKernelVersion, v))
}

// KernelVersionLTE applies the LTE predicate on the "kernel_version" field.
func KernelVersionLTE(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldLTE(FieldKernelVersion, v))
}

// KernelVersionContains applies the Contains predicate on the "kernel_version" field.
func KernelVersionContains(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldContains(FieldKernelVersion, v))
}

// KernelVersionHasPrefix applies the HasPrefix predicate on the "kernel_version" field.
func KernelVersionHasPrefix(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldHasPrefix(FieldKernelVersion, v))
}

// KernelVersionHasSuffix applies the HasSuffix predicate on the "kernel_version" field.
func KernelVersionHasSuffix(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldHasSuffix(FieldKernelVersion, v))
}

// KernelVersionEqualFold applies the EqualFold predicate on the "kernel_version" field.
func KernelVersionEqualFold(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEqualFold(FieldKernelVersion, v))
}

// KernelVersionContainsFold applies the ContainsFold predicate on the "kernel_version" field.
func KernelVersionContainsFold(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldContainsFold(FieldKernelVersion, v))
}

// FirecrackerVersionEQ applies the EQ predicate on the "firecracker_version" field.
func FirecrackerVersionEQ(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEQ(FieldFirecrackerVersion, v))
}

// FirecrackerVersionNEQ applies the NEQ predicate on the "firecracker_version" field.
func FirecrackerVersionNEQ(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNEQ(FieldFirecrackerVersion, v))
}

// FirecrackerVersionIn applies the In predicate on the "firecracker_version" field.
func FirecrackerVersionIn(vs ...string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldIn(FieldFirecrackerVersion, vs...))
}

// FirecrackerVersionNotIn applies the NotIn predicate on the "firecracker_version" field.
func FirecrackerVersionNotIn(vs ...string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNotIn(FieldFirecrackerVersion, vs...))
}

// FirecrackerVersionGT applies the GT predicate on the "firecracker_version" field.
func FirecrackerVersionGT(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldGT(FieldFirecrackerVersion, v))
}

// FirecrackerVersionGTE applies the GTE predicate on the "firecracker_version" field.
func FirecrackerVersionGTE(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldGTE(FieldFirecrackerVersion, v))
}

// FirecrackerVersionLT applies the LT predicate on the "firecracker_version" field.
func FirecrackerVersionLT(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldLT(FieldFirecrackerVersion, v))
}

// FirecrackerVersionLTE applies the LTE predicate on the "firecracker_version" field.
func FirecrackerVersionLTE(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldLTE(FieldFirecrackerVersion, v))
}

// FirecrackerVersionContains applies the Contains predicate on the "firecracker_version" field.
func FirecrackerVersionContains(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldContains(FieldFirecrackerVersion, v))
}

// FirecrackerVersionHasPrefix applies the HasPrefix predicate on the "firecracker_version" field.
func FirecrackerVersionHasPrefix(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldHasPrefix(FieldFirecrackerVersion, v))
}

// FirecrackerVersionHasSuffix applies the HasSuffix predicate on the "firecracker_version" field.
func FirecrackerVersionHasSuffix(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldHasSuffix(FieldFirecrackerVersion, v))
}

// FirecrackerVersionEqualFold applies the EqualFold predicate on the "firecracker_version" field.
func FirecrackerVersionEqualFold(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEqualFold(FieldFirecrackerVersion, v))
}

// FirecrackerVersionContainsFold applies the ContainsFold predicate on the "firecracker_version" field.
func FirecrackerVersionContainsFold(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldContainsFold(FieldFirecrackerVersion, v))
}

// EnvdVersionEQ applies the EQ predicate on the "envd_version" field.
func EnvdVersionEQ(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEQ(FieldEnvdVersion, v))
}

// EnvdVersionNEQ applies the NEQ predicate on the "envd_version" field.
func EnvdVersionNEQ(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNEQ(FieldEnvdVersion, v))
}

// EnvdVersionIn applies the In predicate on the "envd_version" field.
func EnvdVersionIn(vs ...string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldIn(FieldEnvdVersion, vs...))
}

// EnvdVersionNotIn applies the NotIn predicate on the "envd_version" field.
func EnvdVersionNotIn(vs ...string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNotIn(FieldEnvdVersion, vs...))
}

// EnvdVersionGT applies the GT predicate on the "envd_version" field.
func EnvdVersionGT(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldGT(FieldEnvdVersion, v))
}

// EnvdVersionGTE applies the GTE predicate on the "envd_version" field.
func EnvdVersionGTE(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldGTE(FieldEnvdVersion, v))
}

// EnvdVersionLT applies the LT predicate on the "envd_version" field.
func EnvdVersionLT(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldLT(FieldEnvdVersion, v))
}

// EnvdVersionLTE applies the LTE predicate on the "envd_version" field.
func EnvdVersionLTE(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldLTE(FieldEnvdVersion, v))
}

// EnvdVersionContains applies the Contains predicate on the "envd_version" field.
func EnvdVersionContains(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldContains(FieldEnvdVersion, v))
}

// EnvdVersionHasPrefix applies the HasPrefix predicate on the "envd_version" field.
func EnvdVersionHasPrefix(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldHasPrefix(FieldEnvdVersion, v))
}

// EnvdVersionHasSuffix applies the HasSuffix predicate on the "envd_version" field.
func EnvdVersionHasSuffix(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldHasSuffix(FieldEnvdVersion, v))
}

// EnvdVersionIsNil applies the IsNil predicate on the "envd_version" field.
func EnvdVersionIsNil() predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldIsNull(FieldEnvdVersion))
}

// EnvdVersionNotNil applies the NotNil predicate on the "envd_version" field.
func EnvdVersionNotNil() predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldNotNull(FieldEnvdVersion))
}

// EnvdVersionEqualFold applies the EqualFold predicate on the "envd_version" field.
func EnvdVersionEqualFold(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldEqualFold(FieldEnvdVersion, v))
}

// EnvdVersionContainsFold applies the ContainsFold predicate on the "envd_version" field.
func EnvdVersionContainsFold(v string) predicate.EnvBuild {
	return predicate.EnvBuild(sql.FieldContainsFold(FieldEnvdVersion, v))
}

// HasEnv applies the HasEdge predicate on the "env" edge.
func HasEnv() predicate.EnvBuild {
	return predicate.EnvBuild(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, EnvTable, EnvColumn),
		)
		schemaConfig := internal.SchemaConfigFromContext(s.Context())
		step.To.Schema = schemaConfig.Env
		step.Edge.Schema = schemaConfig.EnvBuild
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasEnvWith applies the HasEdge predicate on the "env" edge with a given conditions (other predicates).
func HasEnvWith(preds ...predicate.Env) predicate.EnvBuild {
	return predicate.EnvBuild(func(s *sql.Selector) {
		step := newEnvStep()
		schemaConfig := internal.SchemaConfigFromContext(s.Context())
		step.To.Schema = schemaConfig.Env
		step.Edge.Schema = schemaConfig.EnvBuild
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.EnvBuild) predicate.EnvBuild {
	return predicate.EnvBuild(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.EnvBuild) predicate.EnvBuild {
	return predicate.EnvBuild(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.EnvBuild) predicate.EnvBuild {
	return predicate.EnvBuild(sql.NotPredicates(p))
}
