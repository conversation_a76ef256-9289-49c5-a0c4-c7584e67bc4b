// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v5.29.3
// source: info.proto

package orchestrator

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// needs to be different from the enumeration in the template manager
type ServiceInfoStatus int32

const (
	ServiceInfoStatus_OrchestratorHealthy   ServiceInfoStatus = 0
	ServiceInfoStatus_OrchestratorDraining  ServiceInfoStatus = 1
	ServiceInfoStatus_OrchestratorUnhealthy ServiceInfoStatus = 2
)

// Enum value maps for ServiceInfoStatus.
var (
	ServiceInfoStatus_name = map[int32]string{
		0: "OrchestratorHealthy",
		1: "OrchestratorDraining",
		2: "OrchestratorUnhealthy",
	}
	ServiceInfoStatus_value = map[string]int32{
		"OrchestratorHealthy":   0,
		"OrchestratorDraining":  1,
		"OrchestratorUnhealthy": 2,
	}
)

func (x ServiceInfoStatus) Enum() *ServiceInfoStatus {
	p := new(ServiceInfoStatus)
	*p = x
	return p
}

func (x ServiceInfoStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServiceInfoStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_info_proto_enumTypes[0].Descriptor()
}

func (ServiceInfoStatus) Type() protoreflect.EnumType {
	return &file_info_proto_enumTypes[0]
}

func (x ServiceInfoStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServiceInfoStatus.Descriptor instead.
func (ServiceInfoStatus) EnumDescriptor() ([]byte, []int) {
	return file_info_proto_rawDescGZIP(), []int{0}
}

type ServiceInfoRole int32

const (
	ServiceInfoRole_TemplateManager ServiceInfoRole = 0
	ServiceInfoRole_Orchestrator    ServiceInfoRole = 1
)

// Enum value maps for ServiceInfoRole.
var (
	ServiceInfoRole_name = map[int32]string{
		0: "TemplateManager",
		1: "Orchestrator",
	}
	ServiceInfoRole_value = map[string]int32{
		"TemplateManager": 0,
		"Orchestrator":    1,
	}
)

func (x ServiceInfoRole) Enum() *ServiceInfoRole {
	p := new(ServiceInfoRole)
	*p = x
	return p
}

func (x ServiceInfoRole) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServiceInfoRole) Descriptor() protoreflect.EnumDescriptor {
	return file_info_proto_enumTypes[1].Descriptor()
}

func (ServiceInfoRole) Type() protoreflect.EnumType {
	return &file_info_proto_enumTypes[1]
}

func (x ServiceInfoRole) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServiceInfoRole.Descriptor instead.
func (ServiceInfoRole) EnumDescriptor() ([]byte, []int) {
	return file_info_proto_rawDescGZIP(), []int{1}
}

type ServiceInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodeId                 string                 `protobuf:"bytes,1,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`
	ServiceId              string                 `protobuf:"bytes,2,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	ServiceVersion         string                 `protobuf:"bytes,3,opt,name=service_version,json=serviceVersion,proto3" json:"service_version,omitempty"`
	ServiceCommit          string                 `protobuf:"bytes,4,opt,name=service_commit,json=serviceCommit,proto3" json:"service_commit,omitempty"`
	ServiceStatus          ServiceInfoStatus      `protobuf:"varint,51,opt,name=service_status,json=serviceStatus,proto3,enum=ServiceInfoStatus" json:"service_status,omitempty"`
	ServiceRoles           []ServiceInfoRole      `protobuf:"varint,52,rep,packed,name=service_roles,json=serviceRoles,proto3,enum=ServiceInfoRole" json:"service_roles,omitempty"`
	ServiceStartup         *timestamppb.Timestamp `protobuf:"bytes,53,opt,name=service_startup,json=serviceStartup,proto3" json:"service_startup,omitempty"`
	MetricVcpuUsed         int64                  `protobuf:"varint,101,opt,name=metric_vcpu_used,json=metricVcpuUsed,proto3" json:"metric_vcpu_used,omitempty"`
	MetricMemoryUsedMb     int64                  `protobuf:"varint,102,opt,name=metric_memory_used_mb,json=metricMemoryUsedMb,proto3" json:"metric_memory_used_mb,omitempty"`
	MetricDiskMb           int64                  `protobuf:"varint,103,opt,name=metric_disk_mb,json=metricDiskMb,proto3" json:"metric_disk_mb,omitempty"`
	MetricSandboxesRunning int64                  `protobuf:"varint,104,opt,name=metric_sandboxes_running,json=metricSandboxesRunning,proto3" json:"metric_sandboxes_running,omitempty"`
}

func (x *ServiceInfoResponse) Reset() {
	*x = ServiceInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_info_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceInfoResponse) ProtoMessage() {}

func (x *ServiceInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_info_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceInfoResponse.ProtoReflect.Descriptor instead.
func (*ServiceInfoResponse) Descriptor() ([]byte, []int) {
	return file_info_proto_rawDescGZIP(), []int{0}
}

func (x *ServiceInfoResponse) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *ServiceInfoResponse) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *ServiceInfoResponse) GetServiceVersion() string {
	if x != nil {
		return x.ServiceVersion
	}
	return ""
}

func (x *ServiceInfoResponse) GetServiceCommit() string {
	if x != nil {
		return x.ServiceCommit
	}
	return ""
}

func (x *ServiceInfoResponse) GetServiceStatus() ServiceInfoStatus {
	if x != nil {
		return x.ServiceStatus
	}
	return ServiceInfoStatus_OrchestratorHealthy
}

func (x *ServiceInfoResponse) GetServiceRoles() []ServiceInfoRole {
	if x != nil {
		return x.ServiceRoles
	}
	return nil
}

func (x *ServiceInfoResponse) GetServiceStartup() *timestamppb.Timestamp {
	if x != nil {
		return x.ServiceStartup
	}
	return nil
}

func (x *ServiceInfoResponse) GetMetricVcpuUsed() int64 {
	if x != nil {
		return x.MetricVcpuUsed
	}
	return 0
}

func (x *ServiceInfoResponse) GetMetricMemoryUsedMb() int64 {
	if x != nil {
		return x.MetricMemoryUsedMb
	}
	return 0
}

func (x *ServiceInfoResponse) GetMetricDiskMb() int64 {
	if x != nil {
		return x.MetricDiskMb
	}
	return 0
}

func (x *ServiceInfoResponse) GetMetricSandboxesRunning() int64 {
	if x != nil {
		return x.MetricSandboxesRunning
	}
	return 0
}

type ServiceStatusChangeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceStatus ServiceInfoStatus `protobuf:"varint,2,opt,name=service_status,json=serviceStatus,proto3,enum=ServiceInfoStatus" json:"service_status,omitempty"`
}

func (x *ServiceStatusChangeRequest) Reset() {
	*x = ServiceStatusChangeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_info_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceStatusChangeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceStatusChangeRequest) ProtoMessage() {}

func (x *ServiceStatusChangeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_info_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceStatusChangeRequest.ProtoReflect.Descriptor instead.
func (*ServiceStatusChangeRequest) Descriptor() ([]byte, []int) {
	return file_info_proto_rawDescGZIP(), []int{1}
}

func (x *ServiceStatusChangeRequest) GetServiceStatus() ServiceInfoStatus {
	if x != nil {
		return x.ServiceStatus
	}
	return ServiceInfoStatus_OrchestratorHealthy
}

var File_info_proto protoreflect.FileDescriptor

var file_info_proto_rawDesc = []byte{
	0x0a, 0x0a, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d,
	0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x91, 0x04, 0x0a, 0x13, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63,
	0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x12, 0x39, 0x0a, 0x0e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x33, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x12, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x35, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x18, 0x34, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x0c,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x12, 0x43, 0x0a, 0x0f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x75, 0x70, 0x18,
	0x35, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x75,
	0x70, 0x12, 0x28, 0x0a, 0x10, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x5f, 0x76, 0x63, 0x70, 0x75,
	0x5f, 0x75, 0x73, 0x65, 0x64, 0x18, 0x65, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x6d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x56, 0x63, 0x70, 0x75, 0x55, 0x73, 0x65, 0x64, 0x12, 0x31, 0x0a, 0x15, 0x6d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x5f, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x5f, 0x75, 0x73, 0x65,
	0x64, 0x5f, 0x6d, 0x62, 0x18, 0x66, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x6d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x55, 0x73, 0x65, 0x64, 0x4d, 0x62, 0x12, 0x24,
	0x0a, 0x0e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x5f, 0x64, 0x69, 0x73, 0x6b, 0x5f, 0x6d, 0x62,
	0x18, 0x67, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x44, 0x69,
	0x73, 0x6b, 0x4d, 0x62, 0x12, 0x38, 0x0a, 0x18, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x5f, 0x73,
	0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x65, 0x73, 0x5f, 0x72, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67,
	0x18, 0x68, 0x20, 0x01, 0x28, 0x03, 0x52, 0x16, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x53, 0x61,
	0x6e, 0x64, 0x62, 0x6f, 0x78, 0x65, 0x73, 0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x22, 0x57,
	0x0a, 0x1a, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x39, 0x0a, 0x0e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2a, 0x61, 0x0a, 0x11, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x17, 0x0a, 0x13,
	0x4f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x48, 0x65, 0x61, 0x6c,
	0x74, 0x68, 0x79, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x4f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x44, 0x72, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x10, 0x01, 0x12,
	0x19, 0x0a, 0x15, 0x4f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x55,
	0x6e, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x79, 0x10, 0x02, 0x2a, 0x38, 0x0a, 0x0f, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x13, 0x0a,
	0x0f, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x4f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x10, 0x01, 0x32, 0x98, 0x01, 0x0a, 0x0b, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x14, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x4c, 0x0a, 0x15, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x12, 0x1b, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x42,
	0x2f, 0x5a, 0x2d, 0x68, 0x74, 0x74, 0x70, 0x73, 0x3a, 0x2f, 0x2f, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x32, 0x62, 0x2d, 0x64, 0x65, 0x76, 0x2f, 0x69, 0x6e,
	0x66, 0x72, 0x61, 0x2f, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_info_proto_rawDescOnce sync.Once
	file_info_proto_rawDescData = file_info_proto_rawDesc
)

func file_info_proto_rawDescGZIP() []byte {
	file_info_proto_rawDescOnce.Do(func() {
		file_info_proto_rawDescData = protoimpl.X.CompressGZIP(file_info_proto_rawDescData)
	})
	return file_info_proto_rawDescData
}

var file_info_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_info_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_info_proto_goTypes = []interface{}{
	(ServiceInfoStatus)(0),             // 0: ServiceInfoStatus
	(ServiceInfoRole)(0),               // 1: ServiceInfoRole
	(*ServiceInfoResponse)(nil),        // 2: ServiceInfoResponse
	(*ServiceStatusChangeRequest)(nil), // 3: ServiceStatusChangeRequest
	(*timestamppb.Timestamp)(nil),      // 4: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),              // 5: google.protobuf.Empty
}
var file_info_proto_depIdxs = []int32{
	0, // 0: ServiceInfoResponse.service_status:type_name -> ServiceInfoStatus
	1, // 1: ServiceInfoResponse.service_roles:type_name -> ServiceInfoRole
	4, // 2: ServiceInfoResponse.service_startup:type_name -> google.protobuf.Timestamp
	0, // 3: ServiceStatusChangeRequest.service_status:type_name -> ServiceInfoStatus
	5, // 4: InfoService.ServiceInfo:input_type -> google.protobuf.Empty
	3, // 5: InfoService.ServiceStatusOverride:input_type -> ServiceStatusChangeRequest
	2, // 6: InfoService.ServiceInfo:output_type -> ServiceInfoResponse
	5, // 7: InfoService.ServiceStatusOverride:output_type -> google.protobuf.Empty
	6, // [6:8] is the sub-list for method output_type
	4, // [4:6] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_info_proto_init() }
func file_info_proto_init() {
	if File_info_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_info_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_info_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceStatusChangeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_info_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_info_proto_goTypes,
		DependencyIndexes: file_info_proto_depIdxs,
		EnumInfos:         file_info_proto_enumTypes,
		MessageInfos:      file_info_proto_msgTypes,
	}.Build()
	File_info_proto = out.File
	file_info_proto_rawDesc = nil
	file_info_proto_goTypes = nil
	file_info_proto_depIdxs = nil
}
