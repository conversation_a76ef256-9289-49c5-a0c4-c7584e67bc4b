// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v5.29.3
// source: template-manager.proto

package template_manager

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TemplateBuildState int32

const (
	TemplateBuildState_Building  TemplateBuildState = 0
	TemplateBuildState_Failed    TemplateBuildState = 1
	TemplateBuildState_Completed TemplateBuildState = 2
)

// Enum value maps for TemplateBuildState.
var (
	TemplateBuildState_name = map[int32]string{
		0: "Building",
		1: "Failed",
		2: "Completed",
	}
	TemplateBuildState_value = map[string]int32{
		"Building":  0,
		"Failed":    1,
		"Completed": 2,
	}
)

func (x TemplateBuildState) Enum() *TemplateBuildState {
	p := new(TemplateBuildState)
	*p = x
	return p
}

func (x TemplateBuildState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TemplateBuildState) Descriptor() protoreflect.EnumDescriptor {
	return file_template_manager_proto_enumTypes[0].Descriptor()
}

func (TemplateBuildState) Type() protoreflect.EnumType {
	return &file_template_manager_proto_enumTypes[0]
}

func (x TemplateBuildState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TemplateBuildState.Descriptor instead.
func (TemplateBuildState) EnumDescriptor() ([]byte, []int) {
	return file_template_manager_proto_rawDescGZIP(), []int{0}
}

type HealthState int32

const (
	HealthState_Healthy  HealthState = 0
	HealthState_Draining HealthState = 1
)

// Enum value maps for HealthState.
var (
	HealthState_name = map[int32]string{
		0: "Healthy",
		1: "Draining",
	}
	HealthState_value = map[string]int32{
		"Healthy":  0,
		"Draining": 1,
	}
)

func (x HealthState) Enum() *HealthState {
	p := new(HealthState)
	*p = x
	return p
}

func (x HealthState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HealthState) Descriptor() protoreflect.EnumDescriptor {
	return file_template_manager_proto_enumTypes[1].Descriptor()
}

func (HealthState) Type() protoreflect.EnumType {
	return &file_template_manager_proto_enumTypes[1]
}

func (x HealthState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HealthState.Descriptor instead.
func (HealthState) EnumDescriptor() ([]byte, []int) {
	return file_template_manager_proto_rawDescGZIP(), []int{1}
}

type TemplateConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemplateID         string `protobuf:"bytes,1,opt,name=templateID,proto3" json:"templateID,omitempty"`
	BuildID            string `protobuf:"bytes,2,opt,name=buildID,proto3" json:"buildID,omitempty"`
	MemoryMB           int32  `protobuf:"varint,3,opt,name=memoryMB,proto3" json:"memoryMB,omitempty"`
	VCpuCount          int32  `protobuf:"varint,4,opt,name=vCpuCount,proto3" json:"vCpuCount,omitempty"`
	DiskSizeMB         int32  `protobuf:"varint,5,opt,name=diskSizeMB,proto3" json:"diskSizeMB,omitempty"`
	KernelVersion      string `protobuf:"bytes,6,opt,name=kernelVersion,proto3" json:"kernelVersion,omitempty"`
	FirecrackerVersion string `protobuf:"bytes,7,opt,name=firecrackerVersion,proto3" json:"firecrackerVersion,omitempty"`
	StartCommand       string `protobuf:"bytes,8,opt,name=startCommand,proto3" json:"startCommand,omitempty"`
	HugePages          bool   `protobuf:"varint,9,opt,name=hugePages,proto3" json:"hugePages,omitempty"`
	ReadyCommand       string `protobuf:"bytes,10,opt,name=readyCommand,proto3" json:"readyCommand,omitempty"`
}

func (x *TemplateConfig) Reset() {
	*x = TemplateConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_template_manager_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TemplateConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemplateConfig) ProtoMessage() {}

func (x *TemplateConfig) ProtoReflect() protoreflect.Message {
	mi := &file_template_manager_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemplateConfig.ProtoReflect.Descriptor instead.
func (*TemplateConfig) Descriptor() ([]byte, []int) {
	return file_template_manager_proto_rawDescGZIP(), []int{0}
}

func (x *TemplateConfig) GetTemplateID() string {
	if x != nil {
		return x.TemplateID
	}
	return ""
}

func (x *TemplateConfig) GetBuildID() string {
	if x != nil {
		return x.BuildID
	}
	return ""
}

func (x *TemplateConfig) GetMemoryMB() int32 {
	if x != nil {
		return x.MemoryMB
	}
	return 0
}

func (x *TemplateConfig) GetVCpuCount() int32 {
	if x != nil {
		return x.VCpuCount
	}
	return 0
}

func (x *TemplateConfig) GetDiskSizeMB() int32 {
	if x != nil {
		return x.DiskSizeMB
	}
	return 0
}

func (x *TemplateConfig) GetKernelVersion() string {
	if x != nil {
		return x.KernelVersion
	}
	return ""
}

func (x *TemplateConfig) GetFirecrackerVersion() string {
	if x != nil {
		return x.FirecrackerVersion
	}
	return ""
}

func (x *TemplateConfig) GetStartCommand() string {
	if x != nil {
		return x.StartCommand
	}
	return ""
}

func (x *TemplateConfig) GetHugePages() bool {
	if x != nil {
		return x.HugePages
	}
	return false
}

func (x *TemplateConfig) GetReadyCommand() string {
	if x != nil {
		return x.ReadyCommand
	}
	return ""
}

type TemplateCreateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Template *TemplateConfig `protobuf:"bytes,1,opt,name=template,proto3" json:"template,omitempty"`
}

func (x *TemplateCreateRequest) Reset() {
	*x = TemplateCreateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_template_manager_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TemplateCreateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemplateCreateRequest) ProtoMessage() {}

func (x *TemplateCreateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_template_manager_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemplateCreateRequest.ProtoReflect.Descriptor instead.
func (*TemplateCreateRequest) Descriptor() ([]byte, []int) {
	return file_template_manager_proto_rawDescGZIP(), []int{1}
}

func (x *TemplateCreateRequest) GetTemplate() *TemplateConfig {
	if x != nil {
		return x.Template
	}
	return nil
}

type TemplateStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemplateID string `protobuf:"bytes,1,opt,name=templateID,proto3" json:"templateID,omitempty"`
	BuildID    string `protobuf:"bytes,2,opt,name=buildID,proto3" json:"buildID,omitempty"`
}

func (x *TemplateStatusRequest) Reset() {
	*x = TemplateStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_template_manager_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TemplateStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemplateStatusRequest) ProtoMessage() {}

func (x *TemplateStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_template_manager_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemplateStatusRequest.ProtoReflect.Descriptor instead.
func (*TemplateStatusRequest) Descriptor() ([]byte, []int) {
	return file_template_manager_proto_rawDescGZIP(), []int{2}
}

func (x *TemplateStatusRequest) GetTemplateID() string {
	if x != nil {
		return x.TemplateID
	}
	return ""
}

func (x *TemplateStatusRequest) GetBuildID() string {
	if x != nil {
		return x.BuildID
	}
	return ""
}

// Data required for deleting a template.
type TemplateBuildDeleteRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BuildID    string `protobuf:"bytes,1,opt,name=buildID,proto3" json:"buildID,omitempty"`
	TemplateID string `protobuf:"bytes,2,opt,name=templateID,proto3" json:"templateID,omitempty"`
}

func (x *TemplateBuildDeleteRequest) Reset() {
	*x = TemplateBuildDeleteRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_template_manager_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TemplateBuildDeleteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemplateBuildDeleteRequest) ProtoMessage() {}

func (x *TemplateBuildDeleteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_template_manager_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemplateBuildDeleteRequest.ProtoReflect.Descriptor instead.
func (*TemplateBuildDeleteRequest) Descriptor() ([]byte, []int) {
	return file_template_manager_proto_rawDescGZIP(), []int{3}
}

func (x *TemplateBuildDeleteRequest) GetBuildID() string {
	if x != nil {
		return x.BuildID
	}
	return ""
}

func (x *TemplateBuildDeleteRequest) GetTemplateID() string {
	if x != nil {
		return x.TemplateID
	}
	return ""
}

type TemplateBuildMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RootfsSizeKey  int32  `protobuf:"varint,1,opt,name=rootfsSizeKey,proto3" json:"rootfsSizeKey,omitempty"`
	EnvdVersionKey string `protobuf:"bytes,2,opt,name=envdVersionKey,proto3" json:"envdVersionKey,omitempty"`
}

func (x *TemplateBuildMetadata) Reset() {
	*x = TemplateBuildMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_template_manager_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TemplateBuildMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemplateBuildMetadata) ProtoMessage() {}

func (x *TemplateBuildMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_template_manager_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemplateBuildMetadata.ProtoReflect.Descriptor instead.
func (*TemplateBuildMetadata) Descriptor() ([]byte, []int) {
	return file_template_manager_proto_rawDescGZIP(), []int{4}
}

func (x *TemplateBuildMetadata) GetRootfsSizeKey() int32 {
	if x != nil {
		return x.RootfsSizeKey
	}
	return 0
}

func (x *TemplateBuildMetadata) GetEnvdVersionKey() string {
	if x != nil {
		return x.EnvdVersionKey
	}
	return ""
}

// Logs from template build
type TemplateBuildStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   TemplateBuildState     `protobuf:"varint,1,opt,name=status,proto3,enum=TemplateBuildState" json:"status,omitempty"`
	Metadata *TemplateBuildMetadata `protobuf:"bytes,2,opt,name=metadata,proto3" json:"metadata,omitempty"`
}

func (x *TemplateBuildStatusResponse) Reset() {
	*x = TemplateBuildStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_template_manager_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TemplateBuildStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemplateBuildStatusResponse) ProtoMessage() {}

func (x *TemplateBuildStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_template_manager_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemplateBuildStatusResponse.ProtoReflect.Descriptor instead.
func (*TemplateBuildStatusResponse) Descriptor() ([]byte, []int) {
	return file_template_manager_proto_rawDescGZIP(), []int{5}
}

func (x *TemplateBuildStatusResponse) GetStatus() TemplateBuildState {
	if x != nil {
		return x.Status
	}
	return TemplateBuildState_Building
}

func (x *TemplateBuildStatusResponse) GetMetadata() *TemplateBuildMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type HealthStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status HealthState `protobuf:"varint,1,opt,name=status,proto3,enum=HealthState" json:"status,omitempty"`
}

func (x *HealthStatusResponse) Reset() {
	*x = HealthStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_template_manager_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HealthStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthStatusResponse) ProtoMessage() {}

func (x *HealthStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_template_manager_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthStatusResponse.ProtoReflect.Descriptor instead.
func (*HealthStatusResponse) Descriptor() ([]byte, []int) {
	return file_template_manager_proto_rawDescGZIP(), []int{6}
}

func (x *HealthStatusResponse) GetStatus() HealthState {
	if x != nil {
		return x.Status
	}
	return HealthState_Healthy
}

var File_template_manager_proto protoreflect.FileDescriptor

var file_template_manager_proto_rawDesc = []byte{
	0x0a, 0x16, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2d, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe0, 0x02, 0x0a, 0x0e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x75, 0x69, 0x6c,
	0x64, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x75, 0x69, 0x6c, 0x64,
	0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x4d, 0x42, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x4d, 0x42, 0x12, 0x1c,
	0x0a, 0x09, 0x76, 0x43, 0x70, 0x75, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x76, 0x43, 0x70, 0x75, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a,
	0x64, 0x69, 0x73, 0x6b, 0x53, 0x69, 0x7a, 0x65, 0x4d, 0x42, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0a, 0x64, 0x69, 0x73, 0x6b, 0x53, 0x69, 0x7a, 0x65, 0x4d, 0x42, 0x12, 0x24, 0x0a, 0x0d,
	0x6b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x6b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x12, 0x66, 0x69, 0x72, 0x65, 0x63, 0x72, 0x61, 0x63, 0x6b, 0x65,
	0x72, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12,
	0x66, 0x69, 0x72, 0x65, 0x63, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x72, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x61,
	0x6e, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x74, 0x61, 0x72, 0x74, 0x43,
	0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x68, 0x75, 0x67, 0x65, 0x50, 0x61,
	0x67, 0x65, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x68, 0x75, 0x67, 0x65, 0x50,
	0x61, 0x67, 0x65, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x61, 0x64, 0x79, 0x43, 0x6f, 0x6d,
	0x6d, 0x61, 0x6e, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x61, 0x64,
	0x79, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x22, 0x44, 0x0a, 0x15, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x2b, 0x0a, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x22, 0x51,
	0x0a, 0x15, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x75, 0x69, 0x6c, 0x64,
	0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x49,
	0x44, 0x22, 0x56, 0x0a, 0x1a, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x42, 0x75, 0x69,
	0x6c, 0x64, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x18, 0x0a, 0x07, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x44, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x44, 0x22, 0x65, 0x0a, 0x15, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x12, 0x24, 0x0a, 0x0d, 0x72, 0x6f, 0x6f, 0x74, 0x66, 0x73, 0x53, 0x69, 0x7a, 0x65,
	0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x72, 0x6f, 0x6f, 0x74, 0x66,
	0x73, 0x53, 0x69, 0x7a, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x26, 0x0a, 0x0e, 0x65, 0x6e, 0x76, 0x64,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x65, 0x6e, 0x76, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4b, 0x65, 0x79,
	0x22, 0x7e, 0x0a, 0x1b, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x42, 0x75, 0x69, 0x6c,
	0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x2b, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x13, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x32, 0x0a, 0x08,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x22, 0x3c, 0x0a, 0x14, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x24, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0c, 0x2e, 0x48, 0x65, 0x61, 0x6c, 0x74,
	0x68, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2a, 0x3d,
	0x0a, 0x12, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x0c, 0x0a, 0x08, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x69, 0x6e, 0x67,
	0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x10, 0x01, 0x12, 0x0d,
	0x0a, 0x09, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x10, 0x02, 0x2a, 0x28, 0x0a,
	0x0b, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0b, 0x0a, 0x07,
	0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x79, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x72, 0x61,
	0x69, 0x6e, 0x69, 0x6e, 0x67, 0x10, 0x01, 0x32, 0xab, 0x02, 0x0a, 0x0f, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x40, 0x0a, 0x0e, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x16, 0x2e,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x4b, 0x0a,
	0x13, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4a, 0x0a, 0x13, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x12, 0x1b, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x42, 0x75, 0x69, 0x6c,
	0x64, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x3d, 0x0a, 0x0c, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x15,
	0x2e, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x33, 0x5a, 0x31, 0x68, 0x74, 0x74, 0x70, 0x73, 0x3a, 0x2f,
	0x2f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x32, 0x62, 0x2d,
	0x64, 0x65, 0x76, 0x2f, 0x69, 0x6e, 0x66, 0x72, 0x61, 0x2f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x2d, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_template_manager_proto_rawDescOnce sync.Once
	file_template_manager_proto_rawDescData = file_template_manager_proto_rawDesc
)

func file_template_manager_proto_rawDescGZIP() []byte {
	file_template_manager_proto_rawDescOnce.Do(func() {
		file_template_manager_proto_rawDescData = protoimpl.X.CompressGZIP(file_template_manager_proto_rawDescData)
	})
	return file_template_manager_proto_rawDescData
}

var file_template_manager_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_template_manager_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_template_manager_proto_goTypes = []interface{}{
	(TemplateBuildState)(0),             // 0: TemplateBuildState
	(HealthState)(0),                    // 1: HealthState
	(*TemplateConfig)(nil),              // 2: TemplateConfig
	(*TemplateCreateRequest)(nil),       // 3: TemplateCreateRequest
	(*TemplateStatusRequest)(nil),       // 4: TemplateStatusRequest
	(*TemplateBuildDeleteRequest)(nil),  // 5: TemplateBuildDeleteRequest
	(*TemplateBuildMetadata)(nil),       // 6: TemplateBuildMetadata
	(*TemplateBuildStatusResponse)(nil), // 7: TemplateBuildStatusResponse
	(*HealthStatusResponse)(nil),        // 8: HealthStatusResponse
	(*emptypb.Empty)(nil),               // 9: google.protobuf.Empty
}
var file_template_manager_proto_depIdxs = []int32{
	2, // 0: TemplateCreateRequest.template:type_name -> TemplateConfig
	0, // 1: TemplateBuildStatusResponse.status:type_name -> TemplateBuildState
	6, // 2: TemplateBuildStatusResponse.metadata:type_name -> TemplateBuildMetadata
	1, // 3: HealthStatusResponse.status:type_name -> HealthState
	3, // 4: TemplateService.TemplateCreate:input_type -> TemplateCreateRequest
	4, // 5: TemplateService.TemplateBuildStatus:input_type -> TemplateStatusRequest
	5, // 6: TemplateService.TemplateBuildDelete:input_type -> TemplateBuildDeleteRequest
	9, // 7: TemplateService.HealthStatus:input_type -> google.protobuf.Empty
	9, // 8: TemplateService.TemplateCreate:output_type -> google.protobuf.Empty
	7, // 9: TemplateService.TemplateBuildStatus:output_type -> TemplateBuildStatusResponse
	9, // 10: TemplateService.TemplateBuildDelete:output_type -> google.protobuf.Empty
	8, // 11: TemplateService.HealthStatus:output_type -> HealthStatusResponse
	8, // [8:12] is the sub-list for method output_type
	4, // [4:8] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_template_manager_proto_init() }
func file_template_manager_proto_init() {
	if File_template_manager_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_template_manager_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TemplateConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_template_manager_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TemplateCreateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_template_manager_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TemplateStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_template_manager_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TemplateBuildDeleteRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_template_manager_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TemplateBuildMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_template_manager_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TemplateBuildStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_template_manager_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HealthStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_template_manager_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_template_manager_proto_goTypes,
		DependencyIndexes: file_template_manager_proto_depIdxs,
		EnumInfos:         file_template_manager_proto_enumTypes,
		MessageInfos:      file_template_manager_proto_msgTypes,
	}.Build()
	File_template_manager_proto = out.File
	file_template_manager_proto_rawDesc = nil
	file_template_manager_proto_goTypes = nil
	file_template_manager_proto_depIdxs = nil
}
