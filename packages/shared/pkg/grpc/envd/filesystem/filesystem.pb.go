// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        (unknown)
// source: filesystem/filesystem.proto

package filesystem

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FileType int32

const (
	FileType_FILE_TYPE_UNSPECIFIED FileType = 0
	FileType_FILE_TYPE_FILE        FileType = 1
	FileType_FILE_TYPE_DIRECTORY   FileType = 2
)

// Enum value maps for FileType.
var (
	FileType_name = map[int32]string{
		0: "FILE_TYPE_UNSPECIFIED",
		1: "FILE_TYPE_FILE",
		2: "FILE_TYPE_DIRECTORY",
	}
	FileType_value = map[string]int32{
		"FILE_TYPE_UNSPECIFIED": 0,
		"FILE_TYPE_FILE":        1,
		"FILE_TYPE_DIRECTORY":   2,
	}
)

func (x FileType) Enum() *FileType {
	p := new(FileType)
	*p = x
	return p
}

func (x FileType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FileType) Descriptor() protoreflect.EnumDescriptor {
	return file_filesystem_filesystem_proto_enumTypes[0].Descriptor()
}

func (FileType) Type() protoreflect.EnumType {
	return &file_filesystem_filesystem_proto_enumTypes[0]
}

func (x FileType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FileType.Descriptor instead.
func (FileType) EnumDescriptor() ([]byte, []int) {
	return file_filesystem_filesystem_proto_rawDescGZIP(), []int{0}
}

type EventType int32

const (
	EventType_EVENT_TYPE_UNSPECIFIED EventType = 0
	EventType_EVENT_TYPE_CREATE      EventType = 1
	EventType_EVENT_TYPE_WRITE       EventType = 2
	EventType_EVENT_TYPE_REMOVE      EventType = 3
	EventType_EVENT_TYPE_RENAME      EventType = 4
	EventType_EVENT_TYPE_CHMOD       EventType = 5
)

// Enum value maps for EventType.
var (
	EventType_name = map[int32]string{
		0: "EVENT_TYPE_UNSPECIFIED",
		1: "EVENT_TYPE_CREATE",
		2: "EVENT_TYPE_WRITE",
		3: "EVENT_TYPE_REMOVE",
		4: "EVENT_TYPE_RENAME",
		5: "EVENT_TYPE_CHMOD",
	}
	EventType_value = map[string]int32{
		"EVENT_TYPE_UNSPECIFIED": 0,
		"EVENT_TYPE_CREATE":      1,
		"EVENT_TYPE_WRITE":       2,
		"EVENT_TYPE_REMOVE":      3,
		"EVENT_TYPE_RENAME":      4,
		"EVENT_TYPE_CHMOD":       5,
	}
)

func (x EventType) Enum() *EventType {
	p := new(EventType)
	*p = x
	return p
}

func (x EventType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EventType) Descriptor() protoreflect.EnumDescriptor {
	return file_filesystem_filesystem_proto_enumTypes[1].Descriptor()
}

func (EventType) Type() protoreflect.EnumType {
	return &file_filesystem_filesystem_proto_enumTypes[1]
}

func (x EventType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EventType.Descriptor instead.
func (EventType) EnumDescriptor() ([]byte, []int) {
	return file_filesystem_filesystem_proto_rawDescGZIP(), []int{1}
}

type MoveRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Source      string `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	Destination string `protobuf:"bytes,2,opt,name=destination,proto3" json:"destination,omitempty"`
}

func (x *MoveRequest) Reset() {
	*x = MoveRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_filesystem_filesystem_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MoveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MoveRequest) ProtoMessage() {}

func (x *MoveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_filesystem_filesystem_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MoveRequest.ProtoReflect.Descriptor instead.
func (*MoveRequest) Descriptor() ([]byte, []int) {
	return file_filesystem_filesystem_proto_rawDescGZIP(), []int{0}
}

func (x *MoveRequest) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *MoveRequest) GetDestination() string {
	if x != nil {
		return x.Destination
	}
	return ""
}

type MoveResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Entry *EntryInfo `protobuf:"bytes,1,opt,name=entry,proto3" json:"entry,omitempty"`
}

func (x *MoveResponse) Reset() {
	*x = MoveResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_filesystem_filesystem_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MoveResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MoveResponse) ProtoMessage() {}

func (x *MoveResponse) ProtoReflect() protoreflect.Message {
	mi := &file_filesystem_filesystem_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MoveResponse.ProtoReflect.Descriptor instead.
func (*MoveResponse) Descriptor() ([]byte, []int) {
	return file_filesystem_filesystem_proto_rawDescGZIP(), []int{1}
}

func (x *MoveResponse) GetEntry() *EntryInfo {
	if x != nil {
		return x.Entry
	}
	return nil
}

type MakeDirRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path string `protobuf:"bytes,1,opt,name=path,proto3" json:"path,omitempty"`
}

func (x *MakeDirRequest) Reset() {
	*x = MakeDirRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_filesystem_filesystem_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MakeDirRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MakeDirRequest) ProtoMessage() {}

func (x *MakeDirRequest) ProtoReflect() protoreflect.Message {
	mi := &file_filesystem_filesystem_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MakeDirRequest.ProtoReflect.Descriptor instead.
func (*MakeDirRequest) Descriptor() ([]byte, []int) {
	return file_filesystem_filesystem_proto_rawDescGZIP(), []int{2}
}

func (x *MakeDirRequest) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

type MakeDirResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Entry *EntryInfo `protobuf:"bytes,1,opt,name=entry,proto3" json:"entry,omitempty"`
}

func (x *MakeDirResponse) Reset() {
	*x = MakeDirResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_filesystem_filesystem_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MakeDirResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MakeDirResponse) ProtoMessage() {}

func (x *MakeDirResponse) ProtoReflect() protoreflect.Message {
	mi := &file_filesystem_filesystem_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MakeDirResponse.ProtoReflect.Descriptor instead.
func (*MakeDirResponse) Descriptor() ([]byte, []int) {
	return file_filesystem_filesystem_proto_rawDescGZIP(), []int{3}
}

func (x *MakeDirResponse) GetEntry() *EntryInfo {
	if x != nil {
		return x.Entry
	}
	return nil
}

type RemoveRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path string `protobuf:"bytes,1,opt,name=path,proto3" json:"path,omitempty"`
}

func (x *RemoveRequest) Reset() {
	*x = RemoveRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_filesystem_filesystem_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveRequest) ProtoMessage() {}

func (x *RemoveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_filesystem_filesystem_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveRequest.ProtoReflect.Descriptor instead.
func (*RemoveRequest) Descriptor() ([]byte, []int) {
	return file_filesystem_filesystem_proto_rawDescGZIP(), []int{4}
}

func (x *RemoveRequest) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

type RemoveResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RemoveResponse) Reset() {
	*x = RemoveResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_filesystem_filesystem_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveResponse) ProtoMessage() {}

func (x *RemoveResponse) ProtoReflect() protoreflect.Message {
	mi := &file_filesystem_filesystem_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveResponse.ProtoReflect.Descriptor instead.
func (*RemoveResponse) Descriptor() ([]byte, []int) {
	return file_filesystem_filesystem_proto_rawDescGZIP(), []int{5}
}

type StatRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path string `protobuf:"bytes,1,opt,name=path,proto3" json:"path,omitempty"`
}

func (x *StatRequest) Reset() {
	*x = StatRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_filesystem_filesystem_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatRequest) ProtoMessage() {}

func (x *StatRequest) ProtoReflect() protoreflect.Message {
	mi := &file_filesystem_filesystem_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatRequest.ProtoReflect.Descriptor instead.
func (*StatRequest) Descriptor() ([]byte, []int) {
	return file_filesystem_filesystem_proto_rawDescGZIP(), []int{6}
}

func (x *StatRequest) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

type StatResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Entry *EntryInfo `protobuf:"bytes,1,opt,name=entry,proto3" json:"entry,omitempty"`
}

func (x *StatResponse) Reset() {
	*x = StatResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_filesystem_filesystem_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatResponse) ProtoMessage() {}

func (x *StatResponse) ProtoReflect() protoreflect.Message {
	mi := &file_filesystem_filesystem_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatResponse.ProtoReflect.Descriptor instead.
func (*StatResponse) Descriptor() ([]byte, []int) {
	return file_filesystem_filesystem_proto_rawDescGZIP(), []int{7}
}

func (x *StatResponse) GetEntry() *EntryInfo {
	if x != nil {
		return x.Entry
	}
	return nil
}

type EntryInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Type FileType `protobuf:"varint,2,opt,name=type,proto3,enum=filesystem.FileType" json:"type,omitempty"`
	Path string   `protobuf:"bytes,3,opt,name=path,proto3" json:"path,omitempty"`
}

func (x *EntryInfo) Reset() {
	*x = EntryInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_filesystem_filesystem_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EntryInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntryInfo) ProtoMessage() {}

func (x *EntryInfo) ProtoReflect() protoreflect.Message {
	mi := &file_filesystem_filesystem_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntryInfo.ProtoReflect.Descriptor instead.
func (*EntryInfo) Descriptor() ([]byte, []int) {
	return file_filesystem_filesystem_proto_rawDescGZIP(), []int{8}
}

func (x *EntryInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EntryInfo) GetType() FileType {
	if x != nil {
		return x.Type
	}
	return FileType_FILE_TYPE_UNSPECIFIED
}

func (x *EntryInfo) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

type ListDirRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path  string `protobuf:"bytes,1,opt,name=path,proto3" json:"path,omitempty"`
	Depth uint32 `protobuf:"varint,2,opt,name=depth,proto3" json:"depth,omitempty"`
}

func (x *ListDirRequest) Reset() {
	*x = ListDirRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_filesystem_filesystem_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDirRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDirRequest) ProtoMessage() {}

func (x *ListDirRequest) ProtoReflect() protoreflect.Message {
	mi := &file_filesystem_filesystem_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDirRequest.ProtoReflect.Descriptor instead.
func (*ListDirRequest) Descriptor() ([]byte, []int) {
	return file_filesystem_filesystem_proto_rawDescGZIP(), []int{9}
}

func (x *ListDirRequest) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *ListDirRequest) GetDepth() uint32 {
	if x != nil {
		return x.Depth
	}
	return 0
}

type ListDirResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Entries []*EntryInfo `protobuf:"bytes,1,rep,name=entries,proto3" json:"entries,omitempty"`
}

func (x *ListDirResponse) Reset() {
	*x = ListDirResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_filesystem_filesystem_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDirResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDirResponse) ProtoMessage() {}

func (x *ListDirResponse) ProtoReflect() protoreflect.Message {
	mi := &file_filesystem_filesystem_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDirResponse.ProtoReflect.Descriptor instead.
func (*ListDirResponse) Descriptor() ([]byte, []int) {
	return file_filesystem_filesystem_proto_rawDescGZIP(), []int{10}
}

func (x *ListDirResponse) GetEntries() []*EntryInfo {
	if x != nil {
		return x.Entries
	}
	return nil
}

type WatchDirRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path      string `protobuf:"bytes,1,opt,name=path,proto3" json:"path,omitempty"`
	Recursive bool   `protobuf:"varint,2,opt,name=recursive,proto3" json:"recursive,omitempty"`
}

func (x *WatchDirRequest) Reset() {
	*x = WatchDirRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_filesystem_filesystem_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WatchDirRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WatchDirRequest) ProtoMessage() {}

func (x *WatchDirRequest) ProtoReflect() protoreflect.Message {
	mi := &file_filesystem_filesystem_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WatchDirRequest.ProtoReflect.Descriptor instead.
func (*WatchDirRequest) Descriptor() ([]byte, []int) {
	return file_filesystem_filesystem_proto_rawDescGZIP(), []int{11}
}

func (x *WatchDirRequest) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *WatchDirRequest) GetRecursive() bool {
	if x != nil {
		return x.Recursive
	}
	return false
}

type FilesystemEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string    `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Type EventType `protobuf:"varint,2,opt,name=type,proto3,enum=filesystem.EventType" json:"type,omitempty"`
}

func (x *FilesystemEvent) Reset() {
	*x = FilesystemEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_filesystem_filesystem_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FilesystemEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilesystemEvent) ProtoMessage() {}

func (x *FilesystemEvent) ProtoReflect() protoreflect.Message {
	mi := &file_filesystem_filesystem_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilesystemEvent.ProtoReflect.Descriptor instead.
func (*FilesystemEvent) Descriptor() ([]byte, []int) {
	return file_filesystem_filesystem_proto_rawDescGZIP(), []int{12}
}

func (x *FilesystemEvent) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *FilesystemEvent) GetType() EventType {
	if x != nil {
		return x.Type
	}
	return EventType_EVENT_TYPE_UNSPECIFIED
}

type WatchDirResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Event:
	//
	//	*WatchDirResponse_Start
	//	*WatchDirResponse_Filesystem
	//	*WatchDirResponse_Keepalive
	Event isWatchDirResponse_Event `protobuf_oneof:"event"`
}

func (x *WatchDirResponse) Reset() {
	*x = WatchDirResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_filesystem_filesystem_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WatchDirResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WatchDirResponse) ProtoMessage() {}

func (x *WatchDirResponse) ProtoReflect() protoreflect.Message {
	mi := &file_filesystem_filesystem_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WatchDirResponse.ProtoReflect.Descriptor instead.
func (*WatchDirResponse) Descriptor() ([]byte, []int) {
	return file_filesystem_filesystem_proto_rawDescGZIP(), []int{13}
}

func (m *WatchDirResponse) GetEvent() isWatchDirResponse_Event {
	if m != nil {
		return m.Event
	}
	return nil
}

func (x *WatchDirResponse) GetStart() *WatchDirResponse_StartEvent {
	if x, ok := x.GetEvent().(*WatchDirResponse_Start); ok {
		return x.Start
	}
	return nil
}

func (x *WatchDirResponse) GetFilesystem() *FilesystemEvent {
	if x, ok := x.GetEvent().(*WatchDirResponse_Filesystem); ok {
		return x.Filesystem
	}
	return nil
}

func (x *WatchDirResponse) GetKeepalive() *WatchDirResponse_KeepAlive {
	if x, ok := x.GetEvent().(*WatchDirResponse_Keepalive); ok {
		return x.Keepalive
	}
	return nil
}

type isWatchDirResponse_Event interface {
	isWatchDirResponse_Event()
}

type WatchDirResponse_Start struct {
	Start *WatchDirResponse_StartEvent `protobuf:"bytes,1,opt,name=start,proto3,oneof"`
}

type WatchDirResponse_Filesystem struct {
	Filesystem *FilesystemEvent `protobuf:"bytes,2,opt,name=filesystem,proto3,oneof"`
}

type WatchDirResponse_Keepalive struct {
	Keepalive *WatchDirResponse_KeepAlive `protobuf:"bytes,3,opt,name=keepalive,proto3,oneof"`
}

func (*WatchDirResponse_Start) isWatchDirResponse_Event() {}

func (*WatchDirResponse_Filesystem) isWatchDirResponse_Event() {}

func (*WatchDirResponse_Keepalive) isWatchDirResponse_Event() {}

type CreateWatcherRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path      string `protobuf:"bytes,1,opt,name=path,proto3" json:"path,omitempty"`
	Recursive bool   `protobuf:"varint,2,opt,name=recursive,proto3" json:"recursive,omitempty"`
}

func (x *CreateWatcherRequest) Reset() {
	*x = CreateWatcherRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_filesystem_filesystem_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateWatcherRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWatcherRequest) ProtoMessage() {}

func (x *CreateWatcherRequest) ProtoReflect() protoreflect.Message {
	mi := &file_filesystem_filesystem_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWatcherRequest.ProtoReflect.Descriptor instead.
func (*CreateWatcherRequest) Descriptor() ([]byte, []int) {
	return file_filesystem_filesystem_proto_rawDescGZIP(), []int{14}
}

func (x *CreateWatcherRequest) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *CreateWatcherRequest) GetRecursive() bool {
	if x != nil {
		return x.Recursive
	}
	return false
}

type CreateWatcherResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WatcherId string `protobuf:"bytes,1,opt,name=watcher_id,json=watcherId,proto3" json:"watcher_id,omitempty"`
}

func (x *CreateWatcherResponse) Reset() {
	*x = CreateWatcherResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_filesystem_filesystem_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateWatcherResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWatcherResponse) ProtoMessage() {}

func (x *CreateWatcherResponse) ProtoReflect() protoreflect.Message {
	mi := &file_filesystem_filesystem_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWatcherResponse.ProtoReflect.Descriptor instead.
func (*CreateWatcherResponse) Descriptor() ([]byte, []int) {
	return file_filesystem_filesystem_proto_rawDescGZIP(), []int{15}
}

func (x *CreateWatcherResponse) GetWatcherId() string {
	if x != nil {
		return x.WatcherId
	}
	return ""
}

type GetWatcherEventsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WatcherId string `protobuf:"bytes,1,opt,name=watcher_id,json=watcherId,proto3" json:"watcher_id,omitempty"`
}

func (x *GetWatcherEventsRequest) Reset() {
	*x = GetWatcherEventsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_filesystem_filesystem_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWatcherEventsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWatcherEventsRequest) ProtoMessage() {}

func (x *GetWatcherEventsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_filesystem_filesystem_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWatcherEventsRequest.ProtoReflect.Descriptor instead.
func (*GetWatcherEventsRequest) Descriptor() ([]byte, []int) {
	return file_filesystem_filesystem_proto_rawDescGZIP(), []int{16}
}

func (x *GetWatcherEventsRequest) GetWatcherId() string {
	if x != nil {
		return x.WatcherId
	}
	return ""
}

type GetWatcherEventsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Events []*FilesystemEvent `protobuf:"bytes,1,rep,name=events,proto3" json:"events,omitempty"`
}

func (x *GetWatcherEventsResponse) Reset() {
	*x = GetWatcherEventsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_filesystem_filesystem_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWatcherEventsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWatcherEventsResponse) ProtoMessage() {}

func (x *GetWatcherEventsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_filesystem_filesystem_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWatcherEventsResponse.ProtoReflect.Descriptor instead.
func (*GetWatcherEventsResponse) Descriptor() ([]byte, []int) {
	return file_filesystem_filesystem_proto_rawDescGZIP(), []int{17}
}

func (x *GetWatcherEventsResponse) GetEvents() []*FilesystemEvent {
	if x != nil {
		return x.Events
	}
	return nil
}

type RemoveWatcherRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WatcherId string `protobuf:"bytes,1,opt,name=watcher_id,json=watcherId,proto3" json:"watcher_id,omitempty"`
}

func (x *RemoveWatcherRequest) Reset() {
	*x = RemoveWatcherRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_filesystem_filesystem_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveWatcherRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveWatcherRequest) ProtoMessage() {}

func (x *RemoveWatcherRequest) ProtoReflect() protoreflect.Message {
	mi := &file_filesystem_filesystem_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveWatcherRequest.ProtoReflect.Descriptor instead.
func (*RemoveWatcherRequest) Descriptor() ([]byte, []int) {
	return file_filesystem_filesystem_proto_rawDescGZIP(), []int{18}
}

func (x *RemoveWatcherRequest) GetWatcherId() string {
	if x != nil {
		return x.WatcherId
	}
	return ""
}

type RemoveWatcherResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RemoveWatcherResponse) Reset() {
	*x = RemoveWatcherResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_filesystem_filesystem_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveWatcherResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveWatcherResponse) ProtoMessage() {}

func (x *RemoveWatcherResponse) ProtoReflect() protoreflect.Message {
	mi := &file_filesystem_filesystem_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveWatcherResponse.ProtoReflect.Descriptor instead.
func (*RemoveWatcherResponse) Descriptor() ([]byte, []int) {
	return file_filesystem_filesystem_proto_rawDescGZIP(), []int{19}
}

type WatchDirResponse_StartEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *WatchDirResponse_StartEvent) Reset() {
	*x = WatchDirResponse_StartEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_filesystem_filesystem_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WatchDirResponse_StartEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WatchDirResponse_StartEvent) ProtoMessage() {}

func (x *WatchDirResponse_StartEvent) ProtoReflect() protoreflect.Message {
	mi := &file_filesystem_filesystem_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WatchDirResponse_StartEvent.ProtoReflect.Descriptor instead.
func (*WatchDirResponse_StartEvent) Descriptor() ([]byte, []int) {
	return file_filesystem_filesystem_proto_rawDescGZIP(), []int{13, 0}
}

type WatchDirResponse_KeepAlive struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *WatchDirResponse_KeepAlive) Reset() {
	*x = WatchDirResponse_KeepAlive{}
	if protoimpl.UnsafeEnabled {
		mi := &file_filesystem_filesystem_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WatchDirResponse_KeepAlive) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WatchDirResponse_KeepAlive) ProtoMessage() {}

func (x *WatchDirResponse_KeepAlive) ProtoReflect() protoreflect.Message {
	mi := &file_filesystem_filesystem_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WatchDirResponse_KeepAlive.ProtoReflect.Descriptor instead.
func (*WatchDirResponse_KeepAlive) Descriptor() ([]byte, []int) {
	return file_filesystem_filesystem_proto_rawDescGZIP(), []int{13, 1}
}

var File_filesystem_filesystem_proto protoreflect.FileDescriptor

var file_filesystem_filesystem_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x2f, 0x66, 0x69, 0x6c,
	0x65, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x66,
	0x69, 0x6c, 0x65, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x22, 0x47, 0x0a, 0x0b, 0x4d, 0x6f, 0x76,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0x3b, 0x0a, 0x0c, 0x4d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x2b, 0x0a, 0x05, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x2e, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x22,
	0x24, 0x0a, 0x0e, 0x4d, 0x61, 0x6b, 0x65, 0x44, 0x69, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x70, 0x61, 0x74, 0x68, 0x22, 0x3e, 0x0a, 0x0f, 0x4d, 0x61, 0x6b, 0x65, 0x44, 0x69, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2b, 0x0a, 0x05, 0x65, 0x6e, 0x74, 0x72,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x79,
	0x73, 0x74, 0x65, 0x6d, 0x2e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05,
	0x65, 0x6e, 0x74, 0x72, 0x79, 0x22, 0x23, 0x0a, 0x0d, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x22, 0x10, 0x0a, 0x0e, 0x52, 0x65,
	0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x21, 0x0a, 0x0b,
	0x53, 0x74, 0x61, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x22,
	0x3b, 0x0a, 0x0c, 0x53, 0x74, 0x61, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x2b, 0x0a, 0x05, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x2e, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x22, 0x5d, 0x0a, 0x09,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x66, 0x69,
	0x6c, 0x65, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x22, 0x3a, 0x0a, 0x0e, 0x4c,
	0x69, 0x73, 0x74, 0x44, 0x69, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74,
	0x68, 0x12, 0x14, 0x0a, 0x05, 0x64, 0x65, 0x70, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x05, 0x64, 0x65, 0x70, 0x74, 0x68, 0x22, 0x42, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x44,
	0x69, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x07, 0x65, 0x6e,
	0x74, 0x72, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x66, 0x69,
	0x6c, 0x65, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x2e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x07, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x22, 0x43, 0x0a, 0x0f, 0x57,
	0x61, 0x74, 0x63, 0x68, 0x44, 0x69, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61,
	0x74, 0x68, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x63, 0x75, 0x72, 0x73, 0x69, 0x76, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x72, 0x65, 0x63, 0x75, 0x72, 0x73, 0x69, 0x76, 0x65,
	0x22, 0x50, 0x0a, 0x0f, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x22, 0xfe, 0x01, 0x0a, 0x10, 0x57, 0x61, 0x74, 0x63, 0x68, 0x44, 0x69, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3f, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x79, 0x73,
	0x74, 0x65, 0x6d, 0x2e, 0x57, 0x61, 0x74, 0x63, 0x68, 0x44, 0x69, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x48,
	0x00, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x3d, 0x0a, 0x0a, 0x66, 0x69, 0x6c, 0x65,
	0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66,
	0x69, 0x6c, 0x65, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x79,
	0x73, 0x74, 0x65, 0x6d, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x0a, 0x66, 0x69, 0x6c,
	0x65, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x12, 0x46, 0x0a, 0x09, 0x6b, 0x65, 0x65, 0x70, 0x61,
	0x6c, 0x69, 0x76, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x66, 0x69, 0x6c,
	0x65, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x2e, 0x57, 0x61, 0x74, 0x63, 0x68, 0x44, 0x69, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4b, 0x65, 0x65, 0x70, 0x41, 0x6c, 0x69,
	0x76, 0x65, 0x48, 0x00, 0x52, 0x09, 0x6b, 0x65, 0x65, 0x70, 0x61, 0x6c, 0x69, 0x76, 0x65, 0x1a,
	0x0c, 0x0a, 0x0a, 0x53, 0x74, 0x61, 0x72, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x1a, 0x0b, 0x0a,
	0x09, 0x4b, 0x65, 0x65, 0x70, 0x41, 0x6c, 0x69, 0x76, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x22, 0x48, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x61, 0x74,
	0x63, 0x68, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12,
	0x1c, 0x0a, 0x09, 0x72, 0x65, 0x63, 0x75, 0x72, 0x73, 0x69, 0x76, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x09, 0x72, 0x65, 0x63, 0x75, 0x72, 0x73, 0x69, 0x76, 0x65, 0x22, 0x36, 0x0a,
	0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x77, 0x61, 0x74, 0x63, 0x68, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x77, 0x61, 0x74, 0x63,
	0x68, 0x65, 0x72, 0x49, 0x64, 0x22, 0x38, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x57, 0x61, 0x74, 0x63,
	0x68, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x77, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x77, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x49, 0x64, 0x22,
	0x4f, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x57, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x33, 0x0a, 0x06, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x69,
	0x6c, 0x65, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x79, 0x73,
	0x74, 0x65, 0x6d, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x06, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73,
	0x22, 0x35, 0x0a, 0x14, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x57, 0x61, 0x74, 0x63, 0x68, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x77, 0x61, 0x74, 0x63,
	0x68, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x77, 0x61,
	0x74, 0x63, 0x68, 0x65, 0x72, 0x49, 0x64, 0x22, 0x17, 0x0a, 0x15, 0x52, 0x65, 0x6d, 0x6f, 0x76,
	0x65, 0x57, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2a, 0x52, 0x0a, 0x08, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15,
	0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x46, 0x49, 0x4c, 0x45, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x46,
	0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x49, 0x52, 0x45, 0x43, 0x54, 0x4f,
	0x52, 0x59, 0x10, 0x02, 0x2a, 0x98, 0x01, 0x0a, 0x09, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x15,
	0x0a, 0x11, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x52, 0x45,
	0x41, 0x54, 0x45, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x57, 0x52, 0x49, 0x54, 0x45, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x45,
	0x56, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x4d, 0x4f, 0x56, 0x45,
	0x10, 0x03, 0x12, 0x15, 0x0a, 0x11, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x52, 0x45, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0x04, 0x12, 0x14, 0x0a, 0x10, 0x45, 0x56, 0x45,
	0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x48, 0x4d, 0x4f, 0x44, 0x10, 0x05, 0x32,
	0x9f, 0x05, 0x0a, 0x0a, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x12, 0x39,
	0x0a, 0x04, 0x53, 0x74, 0x61, 0x74, 0x12, 0x17, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x79, 0x73,
	0x74, 0x65, 0x6d, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x18, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x07, 0x4d, 0x61, 0x6b,
	0x65, 0x44, 0x69, 0x72, 0x12, 0x1a, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x79, 0x73, 0x74, 0x65,
	0x6d, 0x2e, 0x4d, 0x61, 0x6b, 0x65, 0x44, 0x69, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1b, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x2e, 0x4d, 0x61,
	0x6b, 0x65, 0x44, 0x69, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x39, 0x0a,
	0x04, 0x4d, 0x6f, 0x76, 0x65, 0x12, 0x17, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x2e, 0x4d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18,
	0x2e, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x2e, 0x4d, 0x6f, 0x76, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x07, 0x4c, 0x69, 0x73, 0x74,
	0x44, 0x69, 0x72, 0x12, 0x1a, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x69, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1b, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x44, 0x69, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3f, 0x0a, 0x06,
	0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x12, 0x19, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x79, 0x73,
	0x74, 0x65, 0x6d, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1a, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x2e, 0x52,
	0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x47, 0x0a,
	0x08, 0x57, 0x61, 0x74, 0x63, 0x68, 0x44, 0x69, 0x72, 0x12, 0x1b, 0x2e, 0x66, 0x69, 0x6c, 0x65,
	0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x2e, 0x57, 0x61, 0x74, 0x63, 0x68, 0x44, 0x69, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x79, 0x73,
	0x74, 0x65, 0x6d, 0x2e, 0x57, 0x61, 0x74, 0x63, 0x68, 0x44, 0x69, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x30, 0x01, 0x12, 0x54, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x57, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x12, 0x20, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x79,
	0x73, 0x74, 0x65, 0x6d, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x61, 0x74, 0x63, 0x68,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x66, 0x69, 0x6c, 0x65,
	0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x61, 0x74,
	0x63, 0x68, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5d, 0x0a, 0x10,
	0x47, 0x65, 0x74, 0x57, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73,
	0x12, 0x23, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x2e, 0x47, 0x65,
	0x74, 0x57, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x54, 0x0a, 0x0d, 0x52,
	0x65, 0x6d, 0x6f, 0x76, 0x65, 0x57, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x12, 0x20, 0x2e, 0x66,
	0x69, 0x6c, 0x65, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65,
	0x57, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21,
	0x2e, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x2e, 0x52, 0x65, 0x6d, 0x6f,
	0x76, 0x65, 0x57, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x42, 0xac, 0x01, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x79,
	0x73, 0x74, 0x65, 0x6d, 0x42, 0x0f, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x41, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x32, 0x62, 0x2d, 0x64, 0x65, 0x76, 0x2f, 0x69, 0x6e, 0x66, 0x72,
	0x61, 0x2f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x73, 0x2f, 0x73, 0x68, 0x61, 0x72, 0x65,
	0x64, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x65, 0x6e, 0x76, 0x64, 0x2f,
	0x66, 0x69, 0x6c, 0x65, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0xa2, 0x02, 0x03, 0x46, 0x58, 0x58,
	0xaa, 0x02, 0x0a, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0xca, 0x02, 0x0a,
	0x46, 0x69, 0x6c, 0x65, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0xe2, 0x02, 0x16, 0x46, 0x69, 0x6c,
	0x65, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5c, 0x47, 0x50, 0x42, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0xea, 0x02, 0x0a, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_filesystem_filesystem_proto_rawDescOnce sync.Once
	file_filesystem_filesystem_proto_rawDescData = file_filesystem_filesystem_proto_rawDesc
)

func file_filesystem_filesystem_proto_rawDescGZIP() []byte {
	file_filesystem_filesystem_proto_rawDescOnce.Do(func() {
		file_filesystem_filesystem_proto_rawDescData = protoimpl.X.CompressGZIP(file_filesystem_filesystem_proto_rawDescData)
	})
	return file_filesystem_filesystem_proto_rawDescData
}

var file_filesystem_filesystem_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_filesystem_filesystem_proto_msgTypes = make([]protoimpl.MessageInfo, 22)
var file_filesystem_filesystem_proto_goTypes = []interface{}{
	(FileType)(0),                       // 0: filesystem.FileType
	(EventType)(0),                      // 1: filesystem.EventType
	(*MoveRequest)(nil),                 // 2: filesystem.MoveRequest
	(*MoveResponse)(nil),                // 3: filesystem.MoveResponse
	(*MakeDirRequest)(nil),              // 4: filesystem.MakeDirRequest
	(*MakeDirResponse)(nil),             // 5: filesystem.MakeDirResponse
	(*RemoveRequest)(nil),               // 6: filesystem.RemoveRequest
	(*RemoveResponse)(nil),              // 7: filesystem.RemoveResponse
	(*StatRequest)(nil),                 // 8: filesystem.StatRequest
	(*StatResponse)(nil),                // 9: filesystem.StatResponse
	(*EntryInfo)(nil),                   // 10: filesystem.EntryInfo
	(*ListDirRequest)(nil),              // 11: filesystem.ListDirRequest
	(*ListDirResponse)(nil),             // 12: filesystem.ListDirResponse
	(*WatchDirRequest)(nil),             // 13: filesystem.WatchDirRequest
	(*FilesystemEvent)(nil),             // 14: filesystem.FilesystemEvent
	(*WatchDirResponse)(nil),            // 15: filesystem.WatchDirResponse
	(*CreateWatcherRequest)(nil),        // 16: filesystem.CreateWatcherRequest
	(*CreateWatcherResponse)(nil),       // 17: filesystem.CreateWatcherResponse
	(*GetWatcherEventsRequest)(nil),     // 18: filesystem.GetWatcherEventsRequest
	(*GetWatcherEventsResponse)(nil),    // 19: filesystem.GetWatcherEventsResponse
	(*RemoveWatcherRequest)(nil),        // 20: filesystem.RemoveWatcherRequest
	(*RemoveWatcherResponse)(nil),       // 21: filesystem.RemoveWatcherResponse
	(*WatchDirResponse_StartEvent)(nil), // 22: filesystem.WatchDirResponse.StartEvent
	(*WatchDirResponse_KeepAlive)(nil),  // 23: filesystem.WatchDirResponse.KeepAlive
}
var file_filesystem_filesystem_proto_depIdxs = []int32{
	10, // 0: filesystem.MoveResponse.entry:type_name -> filesystem.EntryInfo
	10, // 1: filesystem.MakeDirResponse.entry:type_name -> filesystem.EntryInfo
	10, // 2: filesystem.StatResponse.entry:type_name -> filesystem.EntryInfo
	0,  // 3: filesystem.EntryInfo.type:type_name -> filesystem.FileType
	10, // 4: filesystem.ListDirResponse.entries:type_name -> filesystem.EntryInfo
	1,  // 5: filesystem.FilesystemEvent.type:type_name -> filesystem.EventType
	22, // 6: filesystem.WatchDirResponse.start:type_name -> filesystem.WatchDirResponse.StartEvent
	14, // 7: filesystem.WatchDirResponse.filesystem:type_name -> filesystem.FilesystemEvent
	23, // 8: filesystem.WatchDirResponse.keepalive:type_name -> filesystem.WatchDirResponse.KeepAlive
	14, // 9: filesystem.GetWatcherEventsResponse.events:type_name -> filesystem.FilesystemEvent
	8,  // 10: filesystem.Filesystem.Stat:input_type -> filesystem.StatRequest
	4,  // 11: filesystem.Filesystem.MakeDir:input_type -> filesystem.MakeDirRequest
	2,  // 12: filesystem.Filesystem.Move:input_type -> filesystem.MoveRequest
	11, // 13: filesystem.Filesystem.ListDir:input_type -> filesystem.ListDirRequest
	6,  // 14: filesystem.Filesystem.Remove:input_type -> filesystem.RemoveRequest
	13, // 15: filesystem.Filesystem.WatchDir:input_type -> filesystem.WatchDirRequest
	16, // 16: filesystem.Filesystem.CreateWatcher:input_type -> filesystem.CreateWatcherRequest
	18, // 17: filesystem.Filesystem.GetWatcherEvents:input_type -> filesystem.GetWatcherEventsRequest
	20, // 18: filesystem.Filesystem.RemoveWatcher:input_type -> filesystem.RemoveWatcherRequest
	9,  // 19: filesystem.Filesystem.Stat:output_type -> filesystem.StatResponse
	5,  // 20: filesystem.Filesystem.MakeDir:output_type -> filesystem.MakeDirResponse
	3,  // 21: filesystem.Filesystem.Move:output_type -> filesystem.MoveResponse
	12, // 22: filesystem.Filesystem.ListDir:output_type -> filesystem.ListDirResponse
	7,  // 23: filesystem.Filesystem.Remove:output_type -> filesystem.RemoveResponse
	15, // 24: filesystem.Filesystem.WatchDir:output_type -> filesystem.WatchDirResponse
	17, // 25: filesystem.Filesystem.CreateWatcher:output_type -> filesystem.CreateWatcherResponse
	19, // 26: filesystem.Filesystem.GetWatcherEvents:output_type -> filesystem.GetWatcherEventsResponse
	21, // 27: filesystem.Filesystem.RemoveWatcher:output_type -> filesystem.RemoveWatcherResponse
	19, // [19:28] is the sub-list for method output_type
	10, // [10:19] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_filesystem_filesystem_proto_init() }
func file_filesystem_filesystem_proto_init() {
	if File_filesystem_filesystem_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_filesystem_filesystem_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MoveRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_filesystem_filesystem_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MoveResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_filesystem_filesystem_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MakeDirRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_filesystem_filesystem_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MakeDirResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_filesystem_filesystem_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoveRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_filesystem_filesystem_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoveResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_filesystem_filesystem_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_filesystem_filesystem_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_filesystem_filesystem_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EntryInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_filesystem_filesystem_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDirRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_filesystem_filesystem_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDirResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_filesystem_filesystem_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WatchDirRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_filesystem_filesystem_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FilesystemEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_filesystem_filesystem_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WatchDirResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_filesystem_filesystem_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateWatcherRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_filesystem_filesystem_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateWatcherResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_filesystem_filesystem_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWatcherEventsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_filesystem_filesystem_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWatcherEventsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_filesystem_filesystem_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoveWatcherRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_filesystem_filesystem_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoveWatcherResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_filesystem_filesystem_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WatchDirResponse_StartEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_filesystem_filesystem_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WatchDirResponse_KeepAlive); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_filesystem_filesystem_proto_msgTypes[13].OneofWrappers = []interface{}{
		(*WatchDirResponse_Start)(nil),
		(*WatchDirResponse_Filesystem)(nil),
		(*WatchDirResponse_Keepalive)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_filesystem_filesystem_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   22,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_filesystem_filesystem_proto_goTypes,
		DependencyIndexes: file_filesystem_filesystem_proto_depIdxs,
		EnumInfos:         file_filesystem_filesystem_proto_enumTypes,
		MessageInfos:      file_filesystem_filesystem_proto_msgTypes,
	}.Build()
	File_filesystem_filesystem_proto = out.File
	file_filesystem_filesystem_proto_rawDesc = nil
	file_filesystem_filesystem_proto_goTypes = nil
	file_filesystem_filesystem_proto_depIdxs = nil
}
