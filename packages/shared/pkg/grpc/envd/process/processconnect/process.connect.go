// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: process/process.proto

package processconnect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	process "github.com/e2b-dev/infra/packages/shared/pkg/grpc/envd/process"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// ProcessName is the fully-qualified name of the Process service.
	ProcessName = "process.Process"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// ProcessListProcedure is the fully-qualified name of the Process's List RPC.
	ProcessListProcedure = "/process.Process/List"
	// ProcessConnectProcedure is the fully-qualified name of the Process's Connect RPC.
	ProcessConnectProcedure = "/process.Process/Connect"
	// ProcessStartProcedure is the fully-qualified name of the Process's Start RPC.
	ProcessStartProcedure = "/process.Process/Start"
	// ProcessUpdateProcedure is the fully-qualified name of the Process's Update RPC.
	ProcessUpdateProcedure = "/process.Process/Update"
	// ProcessStreamInputProcedure is the fully-qualified name of the Process's StreamInput RPC.
	ProcessStreamInputProcedure = "/process.Process/StreamInput"
	// ProcessSendInputProcedure is the fully-qualified name of the Process's SendInput RPC.
	ProcessSendInputProcedure = "/process.Process/SendInput"
	// ProcessSendSignalProcedure is the fully-qualified name of the Process's SendSignal RPC.
	ProcessSendSignalProcedure = "/process.Process/SendSignal"
)

// ProcessClient is a client for the process.Process service.
type ProcessClient interface {
	List(context.Context, *connect.Request[process.ListRequest]) (*connect.Response[process.ListResponse], error)
	Connect(context.Context, *connect.Request[process.ConnectRequest]) (*connect.ServerStreamForClient[process.ConnectResponse], error)
	Start(context.Context, *connect.Request[process.StartRequest]) (*connect.ServerStreamForClient[process.StartResponse], error)
	Update(context.Context, *connect.Request[process.UpdateRequest]) (*connect.Response[process.UpdateResponse], error)
	// Client input stream ensures ordering of messages
	StreamInput(context.Context) *connect.ClientStreamForClient[process.StreamInputRequest, process.StreamInputResponse]
	SendInput(context.Context, *connect.Request[process.SendInputRequest]) (*connect.Response[process.SendInputResponse], error)
	SendSignal(context.Context, *connect.Request[process.SendSignalRequest]) (*connect.Response[process.SendSignalResponse], error)
}

// NewProcessClient constructs a client for the process.Process service. By default, it uses the
// Connect protocol with the binary Protobuf Codec, asks for gzipped responses, and sends
// uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewProcessClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) ProcessClient {
	baseURL = strings.TrimRight(baseURL, "/")
	processMethods := process.File_process_process_proto.Services().ByName("Process").Methods()
	return &processClient{
		list: connect.NewClient[process.ListRequest, process.ListResponse](
			httpClient,
			baseURL+ProcessListProcedure,
			connect.WithSchema(processMethods.ByName("List")),
			connect.WithClientOptions(opts...),
		),
		connect: connect.NewClient[process.ConnectRequest, process.ConnectResponse](
			httpClient,
			baseURL+ProcessConnectProcedure,
			connect.WithSchema(processMethods.ByName("Connect")),
			connect.WithClientOptions(opts...),
		),
		start: connect.NewClient[process.StartRequest, process.StartResponse](
			httpClient,
			baseURL+ProcessStartProcedure,
			connect.WithSchema(processMethods.ByName("Start")),
			connect.WithClientOptions(opts...),
		),
		update: connect.NewClient[process.UpdateRequest, process.UpdateResponse](
			httpClient,
			baseURL+ProcessUpdateProcedure,
			connect.WithSchema(processMethods.ByName("Update")),
			connect.WithClientOptions(opts...),
		),
		streamInput: connect.NewClient[process.StreamInputRequest, process.StreamInputResponse](
			httpClient,
			baseURL+ProcessStreamInputProcedure,
			connect.WithSchema(processMethods.ByName("StreamInput")),
			connect.WithClientOptions(opts...),
		),
		sendInput: connect.NewClient[process.SendInputRequest, process.SendInputResponse](
			httpClient,
			baseURL+ProcessSendInputProcedure,
			connect.WithSchema(processMethods.ByName("SendInput")),
			connect.WithClientOptions(opts...),
		),
		sendSignal: connect.NewClient[process.SendSignalRequest, process.SendSignalResponse](
			httpClient,
			baseURL+ProcessSendSignalProcedure,
			connect.WithSchema(processMethods.ByName("SendSignal")),
			connect.WithClientOptions(opts...),
		),
	}
}

// processClient implements ProcessClient.
type processClient struct {
	list        *connect.Client[process.ListRequest, process.ListResponse]
	connect     *connect.Client[process.ConnectRequest, process.ConnectResponse]
	start       *connect.Client[process.StartRequest, process.StartResponse]
	update      *connect.Client[process.UpdateRequest, process.UpdateResponse]
	streamInput *connect.Client[process.StreamInputRequest, process.StreamInputResponse]
	sendInput   *connect.Client[process.SendInputRequest, process.SendInputResponse]
	sendSignal  *connect.Client[process.SendSignalRequest, process.SendSignalResponse]
}

// List calls process.Process.List.
func (c *processClient) List(ctx context.Context, req *connect.Request[process.ListRequest]) (*connect.Response[process.ListResponse], error) {
	return c.list.CallUnary(ctx, req)
}

// Connect calls process.Process.Connect.
func (c *processClient) Connect(ctx context.Context, req *connect.Request[process.ConnectRequest]) (*connect.ServerStreamForClient[process.ConnectResponse], error) {
	return c.connect.CallServerStream(ctx, req)
}

// Start calls process.Process.Start.
func (c *processClient) Start(ctx context.Context, req *connect.Request[process.StartRequest]) (*connect.ServerStreamForClient[process.StartResponse], error) {
	return c.start.CallServerStream(ctx, req)
}

// Update calls process.Process.Update.
func (c *processClient) Update(ctx context.Context, req *connect.Request[process.UpdateRequest]) (*connect.Response[process.UpdateResponse], error) {
	return c.update.CallUnary(ctx, req)
}

// StreamInput calls process.Process.StreamInput.
func (c *processClient) StreamInput(ctx context.Context) *connect.ClientStreamForClient[process.StreamInputRequest, process.StreamInputResponse] {
	return c.streamInput.CallClientStream(ctx)
}

// SendInput calls process.Process.SendInput.
func (c *processClient) SendInput(ctx context.Context, req *connect.Request[process.SendInputRequest]) (*connect.Response[process.SendInputResponse], error) {
	return c.sendInput.CallUnary(ctx, req)
}

// SendSignal calls process.Process.SendSignal.
func (c *processClient) SendSignal(ctx context.Context, req *connect.Request[process.SendSignalRequest]) (*connect.Response[process.SendSignalResponse], error) {
	return c.sendSignal.CallUnary(ctx, req)
}

// ProcessHandler is an implementation of the process.Process service.
type ProcessHandler interface {
	List(context.Context, *connect.Request[process.ListRequest]) (*connect.Response[process.ListResponse], error)
	Connect(context.Context, *connect.Request[process.ConnectRequest], *connect.ServerStream[process.ConnectResponse]) error
	Start(context.Context, *connect.Request[process.StartRequest], *connect.ServerStream[process.StartResponse]) error
	Update(context.Context, *connect.Request[process.UpdateRequest]) (*connect.Response[process.UpdateResponse], error)
	// Client input stream ensures ordering of messages
	StreamInput(context.Context, *connect.ClientStream[process.StreamInputRequest]) (*connect.Response[process.StreamInputResponse], error)
	SendInput(context.Context, *connect.Request[process.SendInputRequest]) (*connect.Response[process.SendInputResponse], error)
	SendSignal(context.Context, *connect.Request[process.SendSignalRequest]) (*connect.Response[process.SendSignalResponse], error)
}

// NewProcessHandler builds an HTTP handler from the service implementation. It returns the path on
// which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewProcessHandler(svc ProcessHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	processMethods := process.File_process_process_proto.Services().ByName("Process").Methods()
	processListHandler := connect.NewUnaryHandler(
		ProcessListProcedure,
		svc.List,
		connect.WithSchema(processMethods.ByName("List")),
		connect.WithHandlerOptions(opts...),
	)
	processConnectHandler := connect.NewServerStreamHandler(
		ProcessConnectProcedure,
		svc.Connect,
		connect.WithSchema(processMethods.ByName("Connect")),
		connect.WithHandlerOptions(opts...),
	)
	processStartHandler := connect.NewServerStreamHandler(
		ProcessStartProcedure,
		svc.Start,
		connect.WithSchema(processMethods.ByName("Start")),
		connect.WithHandlerOptions(opts...),
	)
	processUpdateHandler := connect.NewUnaryHandler(
		ProcessUpdateProcedure,
		svc.Update,
		connect.WithSchema(processMethods.ByName("Update")),
		connect.WithHandlerOptions(opts...),
	)
	processStreamInputHandler := connect.NewClientStreamHandler(
		ProcessStreamInputProcedure,
		svc.StreamInput,
		connect.WithSchema(processMethods.ByName("StreamInput")),
		connect.WithHandlerOptions(opts...),
	)
	processSendInputHandler := connect.NewUnaryHandler(
		ProcessSendInputProcedure,
		svc.SendInput,
		connect.WithSchema(processMethods.ByName("SendInput")),
		connect.WithHandlerOptions(opts...),
	)
	processSendSignalHandler := connect.NewUnaryHandler(
		ProcessSendSignalProcedure,
		svc.SendSignal,
		connect.WithSchema(processMethods.ByName("SendSignal")),
		connect.WithHandlerOptions(opts...),
	)
	return "/process.Process/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case ProcessListProcedure:
			processListHandler.ServeHTTP(w, r)
		case ProcessConnectProcedure:
			processConnectHandler.ServeHTTP(w, r)
		case ProcessStartProcedure:
			processStartHandler.ServeHTTP(w, r)
		case ProcessUpdateProcedure:
			processUpdateHandler.ServeHTTP(w, r)
		case ProcessStreamInputProcedure:
			processStreamInputHandler.ServeHTTP(w, r)
		case ProcessSendInputProcedure:
			processSendInputHandler.ServeHTTP(w, r)
		case ProcessSendSignalProcedure:
			processSendSignalHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedProcessHandler returns CodeUnimplemented from all methods.
type UnimplementedProcessHandler struct{}

func (UnimplementedProcessHandler) List(context.Context, *connect.Request[process.ListRequest]) (*connect.Response[process.ListResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("process.Process.List is not implemented"))
}

func (UnimplementedProcessHandler) Connect(context.Context, *connect.Request[process.ConnectRequest], *connect.ServerStream[process.ConnectResponse]) error {
	return connect.NewError(connect.CodeUnimplemented, errors.New("process.Process.Connect is not implemented"))
}

func (UnimplementedProcessHandler) Start(context.Context, *connect.Request[process.StartRequest], *connect.ServerStream[process.StartResponse]) error {
	return connect.NewError(connect.CodeUnimplemented, errors.New("process.Process.Start is not implemented"))
}

func (UnimplementedProcessHandler) Update(context.Context, *connect.Request[process.UpdateRequest]) (*connect.Response[process.UpdateResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("process.Process.Update is not implemented"))
}

func (UnimplementedProcessHandler) StreamInput(context.Context, *connect.ClientStream[process.StreamInputRequest]) (*connect.Response[process.StreamInputResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("process.Process.StreamInput is not implemented"))
}

func (UnimplementedProcessHandler) SendInput(context.Context, *connect.Request[process.SendInputRequest]) (*connect.Response[process.SendInputResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("process.Process.SendInput is not implemented"))
}

func (UnimplementedProcessHandler) SendSignal(context.Context, *connect.Request[process.SendSignalRequest]) (*connect.Response[process.SendSignalResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("process.Process.SendSignal is not implemented"))
}
