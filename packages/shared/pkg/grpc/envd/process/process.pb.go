// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        (unknown)
// source: process/process.proto

package process

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Signal int32

const (
	Signal_SIGNAL_UNSPECIFIED Signal = 0
	Signal_SIGNAL_SIGTERM     Signal = 15
	Signal_SIGNAL_SIGKILL     Signal = 9
)

// Enum value maps for Signal.
var (
	Signal_name = map[int32]string{
		0:  "SIGNAL_UNSPECIFIED",
		15: "SIGNAL_SIGTERM",
		9:  "SIGNAL_SIGKILL",
	}
	Signal_value = map[string]int32{
		"SIGNAL_UNSPECIFIED": 0,
		"SIGNAL_SIGTERM":     15,
		"SIGNAL_SIGKILL":     9,
	}
)

func (x Signal) Enum() *Signal {
	p := new(Signal)
	*p = x
	return p
}

func (x Signal) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Signal) Descriptor() protoreflect.EnumDescriptor {
	return file_process_process_proto_enumTypes[0].Descriptor()
}

func (Signal) Type() protoreflect.EnumType {
	return &file_process_process_proto_enumTypes[0]
}

func (x Signal) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Signal.Descriptor instead.
func (Signal) EnumDescriptor() ([]byte, []int) {
	return file_process_process_proto_rawDescGZIP(), []int{0}
}

type PTY struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Size *PTY_Size `protobuf:"bytes,1,opt,name=size,proto3" json:"size,omitempty"`
}

func (x *PTY) Reset() {
	*x = PTY{}
	if protoimpl.UnsafeEnabled {
		mi := &file_process_process_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PTY) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PTY) ProtoMessage() {}

func (x *PTY) ProtoReflect() protoreflect.Message {
	mi := &file_process_process_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PTY.ProtoReflect.Descriptor instead.
func (*PTY) Descriptor() ([]byte, []int) {
	return file_process_process_proto_rawDescGZIP(), []int{0}
}

func (x *PTY) GetSize() *PTY_Size {
	if x != nil {
		return x.Size
	}
	return nil
}

type ProcessConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cmd  string            `protobuf:"bytes,1,opt,name=cmd,proto3" json:"cmd,omitempty"`
	Args []string          `protobuf:"bytes,2,rep,name=args,proto3" json:"args,omitempty"`
	Envs map[string]string `protobuf:"bytes,3,rep,name=envs,proto3" json:"envs,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Cwd  *string           `protobuf:"bytes,4,opt,name=cwd,proto3,oneof" json:"cwd,omitempty"`
}

func (x *ProcessConfig) Reset() {
	*x = ProcessConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_process_process_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessConfig) ProtoMessage() {}

func (x *ProcessConfig) ProtoReflect() protoreflect.Message {
	mi := &file_process_process_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessConfig.ProtoReflect.Descriptor instead.
func (*ProcessConfig) Descriptor() ([]byte, []int) {
	return file_process_process_proto_rawDescGZIP(), []int{1}
}

func (x *ProcessConfig) GetCmd() string {
	if x != nil {
		return x.Cmd
	}
	return ""
}

func (x *ProcessConfig) GetArgs() []string {
	if x != nil {
		return x.Args
	}
	return nil
}

func (x *ProcessConfig) GetEnvs() map[string]string {
	if x != nil {
		return x.Envs
	}
	return nil
}

func (x *ProcessConfig) GetCwd() string {
	if x != nil && x.Cwd != nil {
		return *x.Cwd
	}
	return ""
}

type ListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListRequest) Reset() {
	*x = ListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_process_process_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRequest) ProtoMessage() {}

func (x *ListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_process_process_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRequest.ProtoReflect.Descriptor instead.
func (*ListRequest) Descriptor() ([]byte, []int) {
	return file_process_process_proto_rawDescGZIP(), []int{2}
}

type ProcessInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Config *ProcessConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	Pid    uint32         `protobuf:"varint,2,opt,name=pid,proto3" json:"pid,omitempty"`
	Tag    *string        `protobuf:"bytes,3,opt,name=tag,proto3,oneof" json:"tag,omitempty"`
}

func (x *ProcessInfo) Reset() {
	*x = ProcessInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_process_process_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessInfo) ProtoMessage() {}

func (x *ProcessInfo) ProtoReflect() protoreflect.Message {
	mi := &file_process_process_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessInfo.ProtoReflect.Descriptor instead.
func (*ProcessInfo) Descriptor() ([]byte, []int) {
	return file_process_process_proto_rawDescGZIP(), []int{3}
}

func (x *ProcessInfo) GetConfig() *ProcessConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *ProcessInfo) GetPid() uint32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *ProcessInfo) GetTag() string {
	if x != nil && x.Tag != nil {
		return *x.Tag
	}
	return ""
}

type ListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Processes []*ProcessInfo `protobuf:"bytes,1,rep,name=processes,proto3" json:"processes,omitempty"`
}

func (x *ListResponse) Reset() {
	*x = ListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_process_process_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListResponse) ProtoMessage() {}

func (x *ListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_process_process_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListResponse.ProtoReflect.Descriptor instead.
func (*ListResponse) Descriptor() ([]byte, []int) {
	return file_process_process_proto_rawDescGZIP(), []int{4}
}

func (x *ListResponse) GetProcesses() []*ProcessInfo {
	if x != nil {
		return x.Processes
	}
	return nil
}

type StartRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Process *ProcessConfig `protobuf:"bytes,1,opt,name=process,proto3" json:"process,omitempty"`
	Pty     *PTY           `protobuf:"bytes,2,opt,name=pty,proto3,oneof" json:"pty,omitempty"`
	Tag     *string        `protobuf:"bytes,3,opt,name=tag,proto3,oneof" json:"tag,omitempty"`
}

func (x *StartRequest) Reset() {
	*x = StartRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_process_process_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartRequest) ProtoMessage() {}

func (x *StartRequest) ProtoReflect() protoreflect.Message {
	mi := &file_process_process_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartRequest.ProtoReflect.Descriptor instead.
func (*StartRequest) Descriptor() ([]byte, []int) {
	return file_process_process_proto_rawDescGZIP(), []int{5}
}

func (x *StartRequest) GetProcess() *ProcessConfig {
	if x != nil {
		return x.Process
	}
	return nil
}

func (x *StartRequest) GetPty() *PTY {
	if x != nil {
		return x.Pty
	}
	return nil
}

func (x *StartRequest) GetTag() string {
	if x != nil && x.Tag != nil {
		return *x.Tag
	}
	return ""
}

type UpdateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Process *ProcessSelector `protobuf:"bytes,1,opt,name=process,proto3" json:"process,omitempty"`
	Pty     *PTY             `protobuf:"bytes,2,opt,name=pty,proto3,oneof" json:"pty,omitempty"`
}

func (x *UpdateRequest) Reset() {
	*x = UpdateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_process_process_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRequest) ProtoMessage() {}

func (x *UpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_process_process_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRequest.ProtoReflect.Descriptor instead.
func (*UpdateRequest) Descriptor() ([]byte, []int) {
	return file_process_process_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateRequest) GetProcess() *ProcessSelector {
	if x != nil {
		return x.Process
	}
	return nil
}

func (x *UpdateRequest) GetPty() *PTY {
	if x != nil {
		return x.Pty
	}
	return nil
}

type UpdateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateResponse) Reset() {
	*x = UpdateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_process_process_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateResponse) ProtoMessage() {}

func (x *UpdateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_process_process_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateResponse.ProtoReflect.Descriptor instead.
func (*UpdateResponse) Descriptor() ([]byte, []int) {
	return file_process_process_proto_rawDescGZIP(), []int{7}
}

type ProcessEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Event:
	//
	//	*ProcessEvent_Start
	//	*ProcessEvent_Data
	//	*ProcessEvent_End
	//	*ProcessEvent_Keepalive
	Event isProcessEvent_Event `protobuf_oneof:"event"`
}

func (x *ProcessEvent) Reset() {
	*x = ProcessEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_process_process_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessEvent) ProtoMessage() {}

func (x *ProcessEvent) ProtoReflect() protoreflect.Message {
	mi := &file_process_process_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessEvent.ProtoReflect.Descriptor instead.
func (*ProcessEvent) Descriptor() ([]byte, []int) {
	return file_process_process_proto_rawDescGZIP(), []int{8}
}

func (m *ProcessEvent) GetEvent() isProcessEvent_Event {
	if m != nil {
		return m.Event
	}
	return nil
}

func (x *ProcessEvent) GetStart() *ProcessEvent_StartEvent {
	if x, ok := x.GetEvent().(*ProcessEvent_Start); ok {
		return x.Start
	}
	return nil
}

func (x *ProcessEvent) GetData() *ProcessEvent_DataEvent {
	if x, ok := x.GetEvent().(*ProcessEvent_Data); ok {
		return x.Data
	}
	return nil
}

func (x *ProcessEvent) GetEnd() *ProcessEvent_EndEvent {
	if x, ok := x.GetEvent().(*ProcessEvent_End); ok {
		return x.End
	}
	return nil
}

func (x *ProcessEvent) GetKeepalive() *ProcessEvent_KeepAlive {
	if x, ok := x.GetEvent().(*ProcessEvent_Keepalive); ok {
		return x.Keepalive
	}
	return nil
}

type isProcessEvent_Event interface {
	isProcessEvent_Event()
}

type ProcessEvent_Start struct {
	Start *ProcessEvent_StartEvent `protobuf:"bytes,1,opt,name=start,proto3,oneof"`
}

type ProcessEvent_Data struct {
	Data *ProcessEvent_DataEvent `protobuf:"bytes,2,opt,name=data,proto3,oneof"`
}

type ProcessEvent_End struct {
	End *ProcessEvent_EndEvent `protobuf:"bytes,3,opt,name=end,proto3,oneof"`
}

type ProcessEvent_Keepalive struct {
	Keepalive *ProcessEvent_KeepAlive `protobuf:"bytes,4,opt,name=keepalive,proto3,oneof"`
}

func (*ProcessEvent_Start) isProcessEvent_Event() {}

func (*ProcessEvent_Data) isProcessEvent_Event() {}

func (*ProcessEvent_End) isProcessEvent_Event() {}

func (*ProcessEvent_Keepalive) isProcessEvent_Event() {}

type StartResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Event *ProcessEvent `protobuf:"bytes,1,opt,name=event,proto3" json:"event,omitempty"`
}

func (x *StartResponse) Reset() {
	*x = StartResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_process_process_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartResponse) ProtoMessage() {}

func (x *StartResponse) ProtoReflect() protoreflect.Message {
	mi := &file_process_process_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartResponse.ProtoReflect.Descriptor instead.
func (*StartResponse) Descriptor() ([]byte, []int) {
	return file_process_process_proto_rawDescGZIP(), []int{9}
}

func (x *StartResponse) GetEvent() *ProcessEvent {
	if x != nil {
		return x.Event
	}
	return nil
}

type ConnectResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Event *ProcessEvent `protobuf:"bytes,1,opt,name=event,proto3" json:"event,omitempty"`
}

func (x *ConnectResponse) Reset() {
	*x = ConnectResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_process_process_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConnectResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectResponse) ProtoMessage() {}

func (x *ConnectResponse) ProtoReflect() protoreflect.Message {
	mi := &file_process_process_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectResponse.ProtoReflect.Descriptor instead.
func (*ConnectResponse) Descriptor() ([]byte, []int) {
	return file_process_process_proto_rawDescGZIP(), []int{10}
}

func (x *ConnectResponse) GetEvent() *ProcessEvent {
	if x != nil {
		return x.Event
	}
	return nil
}

type SendInputRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Process *ProcessSelector `protobuf:"bytes,1,opt,name=process,proto3" json:"process,omitempty"`
	Input   *ProcessInput    `protobuf:"bytes,2,opt,name=input,proto3" json:"input,omitempty"`
}

func (x *SendInputRequest) Reset() {
	*x = SendInputRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_process_process_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendInputRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendInputRequest) ProtoMessage() {}

func (x *SendInputRequest) ProtoReflect() protoreflect.Message {
	mi := &file_process_process_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendInputRequest.ProtoReflect.Descriptor instead.
func (*SendInputRequest) Descriptor() ([]byte, []int) {
	return file_process_process_proto_rawDescGZIP(), []int{11}
}

func (x *SendInputRequest) GetProcess() *ProcessSelector {
	if x != nil {
		return x.Process
	}
	return nil
}

func (x *SendInputRequest) GetInput() *ProcessInput {
	if x != nil {
		return x.Input
	}
	return nil
}

type SendInputResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SendInputResponse) Reset() {
	*x = SendInputResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_process_process_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendInputResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendInputResponse) ProtoMessage() {}

func (x *SendInputResponse) ProtoReflect() protoreflect.Message {
	mi := &file_process_process_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendInputResponse.ProtoReflect.Descriptor instead.
func (*SendInputResponse) Descriptor() ([]byte, []int) {
	return file_process_process_proto_rawDescGZIP(), []int{12}
}

type ProcessInput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Input:
	//
	//	*ProcessInput_Stdin
	//	*ProcessInput_Pty
	Input isProcessInput_Input `protobuf_oneof:"input"`
}

func (x *ProcessInput) Reset() {
	*x = ProcessInput{}
	if protoimpl.UnsafeEnabled {
		mi := &file_process_process_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessInput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessInput) ProtoMessage() {}

func (x *ProcessInput) ProtoReflect() protoreflect.Message {
	mi := &file_process_process_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessInput.ProtoReflect.Descriptor instead.
func (*ProcessInput) Descriptor() ([]byte, []int) {
	return file_process_process_proto_rawDescGZIP(), []int{13}
}

func (m *ProcessInput) GetInput() isProcessInput_Input {
	if m != nil {
		return m.Input
	}
	return nil
}

func (x *ProcessInput) GetStdin() []byte {
	if x, ok := x.GetInput().(*ProcessInput_Stdin); ok {
		return x.Stdin
	}
	return nil
}

func (x *ProcessInput) GetPty() []byte {
	if x, ok := x.GetInput().(*ProcessInput_Pty); ok {
		return x.Pty
	}
	return nil
}

type isProcessInput_Input interface {
	isProcessInput_Input()
}

type ProcessInput_Stdin struct {
	Stdin []byte `protobuf:"bytes,1,opt,name=stdin,proto3,oneof"`
}

type ProcessInput_Pty struct {
	Pty []byte `protobuf:"bytes,2,opt,name=pty,proto3,oneof"`
}

func (*ProcessInput_Stdin) isProcessInput_Input() {}

func (*ProcessInput_Pty) isProcessInput_Input() {}

type StreamInputRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Event:
	//
	//	*StreamInputRequest_Start
	//	*StreamInputRequest_Data
	//	*StreamInputRequest_Keepalive
	Event isStreamInputRequest_Event `protobuf_oneof:"event"`
}

func (x *StreamInputRequest) Reset() {
	*x = StreamInputRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_process_process_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StreamInputRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamInputRequest) ProtoMessage() {}

func (x *StreamInputRequest) ProtoReflect() protoreflect.Message {
	mi := &file_process_process_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamInputRequest.ProtoReflect.Descriptor instead.
func (*StreamInputRequest) Descriptor() ([]byte, []int) {
	return file_process_process_proto_rawDescGZIP(), []int{14}
}

func (m *StreamInputRequest) GetEvent() isStreamInputRequest_Event {
	if m != nil {
		return m.Event
	}
	return nil
}

func (x *StreamInputRequest) GetStart() *StreamInputRequest_StartEvent {
	if x, ok := x.GetEvent().(*StreamInputRequest_Start); ok {
		return x.Start
	}
	return nil
}

func (x *StreamInputRequest) GetData() *StreamInputRequest_DataEvent {
	if x, ok := x.GetEvent().(*StreamInputRequest_Data); ok {
		return x.Data
	}
	return nil
}

func (x *StreamInputRequest) GetKeepalive() *StreamInputRequest_KeepAlive {
	if x, ok := x.GetEvent().(*StreamInputRequest_Keepalive); ok {
		return x.Keepalive
	}
	return nil
}

type isStreamInputRequest_Event interface {
	isStreamInputRequest_Event()
}

type StreamInputRequest_Start struct {
	Start *StreamInputRequest_StartEvent `protobuf:"bytes,1,opt,name=start,proto3,oneof"`
}

type StreamInputRequest_Data struct {
	Data *StreamInputRequest_DataEvent `protobuf:"bytes,2,opt,name=data,proto3,oneof"`
}

type StreamInputRequest_Keepalive struct {
	Keepalive *StreamInputRequest_KeepAlive `protobuf:"bytes,3,opt,name=keepalive,proto3,oneof"`
}

func (*StreamInputRequest_Start) isStreamInputRequest_Event() {}

func (*StreamInputRequest_Data) isStreamInputRequest_Event() {}

func (*StreamInputRequest_Keepalive) isStreamInputRequest_Event() {}

type StreamInputResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StreamInputResponse) Reset() {
	*x = StreamInputResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_process_process_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StreamInputResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamInputResponse) ProtoMessage() {}

func (x *StreamInputResponse) ProtoReflect() protoreflect.Message {
	mi := &file_process_process_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamInputResponse.ProtoReflect.Descriptor instead.
func (*StreamInputResponse) Descriptor() ([]byte, []int) {
	return file_process_process_proto_rawDescGZIP(), []int{15}
}

type SendSignalRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Process *ProcessSelector `protobuf:"bytes,1,opt,name=process,proto3" json:"process,omitempty"`
	Signal  Signal           `protobuf:"varint,2,opt,name=signal,proto3,enum=process.Signal" json:"signal,omitempty"`
}

func (x *SendSignalRequest) Reset() {
	*x = SendSignalRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_process_process_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendSignalRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendSignalRequest) ProtoMessage() {}

func (x *SendSignalRequest) ProtoReflect() protoreflect.Message {
	mi := &file_process_process_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendSignalRequest.ProtoReflect.Descriptor instead.
func (*SendSignalRequest) Descriptor() ([]byte, []int) {
	return file_process_process_proto_rawDescGZIP(), []int{16}
}

func (x *SendSignalRequest) GetProcess() *ProcessSelector {
	if x != nil {
		return x.Process
	}
	return nil
}

func (x *SendSignalRequest) GetSignal() Signal {
	if x != nil {
		return x.Signal
	}
	return Signal_SIGNAL_UNSPECIFIED
}

type SendSignalResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SendSignalResponse) Reset() {
	*x = SendSignalResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_process_process_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendSignalResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendSignalResponse) ProtoMessage() {}

func (x *SendSignalResponse) ProtoReflect() protoreflect.Message {
	mi := &file_process_process_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendSignalResponse.ProtoReflect.Descriptor instead.
func (*SendSignalResponse) Descriptor() ([]byte, []int) {
	return file_process_process_proto_rawDescGZIP(), []int{17}
}

type ConnectRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Process *ProcessSelector `protobuf:"bytes,1,opt,name=process,proto3" json:"process,omitempty"`
}

func (x *ConnectRequest) Reset() {
	*x = ConnectRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_process_process_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConnectRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectRequest) ProtoMessage() {}

func (x *ConnectRequest) ProtoReflect() protoreflect.Message {
	mi := &file_process_process_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectRequest.ProtoReflect.Descriptor instead.
func (*ConnectRequest) Descriptor() ([]byte, []int) {
	return file_process_process_proto_rawDescGZIP(), []int{18}
}

func (x *ConnectRequest) GetProcess() *ProcessSelector {
	if x != nil {
		return x.Process
	}
	return nil
}

type ProcessSelector struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Selector:
	//
	//	*ProcessSelector_Pid
	//	*ProcessSelector_Tag
	Selector isProcessSelector_Selector `protobuf_oneof:"selector"`
}

func (x *ProcessSelector) Reset() {
	*x = ProcessSelector{}
	if protoimpl.UnsafeEnabled {
		mi := &file_process_process_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessSelector) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessSelector) ProtoMessage() {}

func (x *ProcessSelector) ProtoReflect() protoreflect.Message {
	mi := &file_process_process_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessSelector.ProtoReflect.Descriptor instead.
func (*ProcessSelector) Descriptor() ([]byte, []int) {
	return file_process_process_proto_rawDescGZIP(), []int{19}
}

func (m *ProcessSelector) GetSelector() isProcessSelector_Selector {
	if m != nil {
		return m.Selector
	}
	return nil
}

func (x *ProcessSelector) GetPid() uint32 {
	if x, ok := x.GetSelector().(*ProcessSelector_Pid); ok {
		return x.Pid
	}
	return 0
}

func (x *ProcessSelector) GetTag() string {
	if x, ok := x.GetSelector().(*ProcessSelector_Tag); ok {
		return x.Tag
	}
	return ""
}

type isProcessSelector_Selector interface {
	isProcessSelector_Selector()
}

type ProcessSelector_Pid struct {
	Pid uint32 `protobuf:"varint,1,opt,name=pid,proto3,oneof"`
}

type ProcessSelector_Tag struct {
	Tag string `protobuf:"bytes,2,opt,name=tag,proto3,oneof"`
}

func (*ProcessSelector_Pid) isProcessSelector_Selector() {}

func (*ProcessSelector_Tag) isProcessSelector_Selector() {}

type PTY_Size struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cols uint32 `protobuf:"varint,1,opt,name=cols,proto3" json:"cols,omitempty"`
	Rows uint32 `protobuf:"varint,2,opt,name=rows,proto3" json:"rows,omitempty"`
}

func (x *PTY_Size) Reset() {
	*x = PTY_Size{}
	if protoimpl.UnsafeEnabled {
		mi := &file_process_process_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PTY_Size) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PTY_Size) ProtoMessage() {}

func (x *PTY_Size) ProtoReflect() protoreflect.Message {
	mi := &file_process_process_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PTY_Size.ProtoReflect.Descriptor instead.
func (*PTY_Size) Descriptor() ([]byte, []int) {
	return file_process_process_proto_rawDescGZIP(), []int{0, 0}
}

func (x *PTY_Size) GetCols() uint32 {
	if x != nil {
		return x.Cols
	}
	return 0
}

func (x *PTY_Size) GetRows() uint32 {
	if x != nil {
		return x.Rows
	}
	return 0
}

type ProcessEvent_StartEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pid uint32 `protobuf:"varint,1,opt,name=pid,proto3" json:"pid,omitempty"`
}

func (x *ProcessEvent_StartEvent) Reset() {
	*x = ProcessEvent_StartEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_process_process_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessEvent_StartEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessEvent_StartEvent) ProtoMessage() {}

func (x *ProcessEvent_StartEvent) ProtoReflect() protoreflect.Message {
	mi := &file_process_process_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessEvent_StartEvent.ProtoReflect.Descriptor instead.
func (*ProcessEvent_StartEvent) Descriptor() ([]byte, []int) {
	return file_process_process_proto_rawDescGZIP(), []int{8, 0}
}

func (x *ProcessEvent_StartEvent) GetPid() uint32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

type ProcessEvent_DataEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Output:
	//
	//	*ProcessEvent_DataEvent_Stdout
	//	*ProcessEvent_DataEvent_Stderr
	//	*ProcessEvent_DataEvent_Pty
	Output isProcessEvent_DataEvent_Output `protobuf_oneof:"output"`
}

func (x *ProcessEvent_DataEvent) Reset() {
	*x = ProcessEvent_DataEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_process_process_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessEvent_DataEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessEvent_DataEvent) ProtoMessage() {}

func (x *ProcessEvent_DataEvent) ProtoReflect() protoreflect.Message {
	mi := &file_process_process_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessEvent_DataEvent.ProtoReflect.Descriptor instead.
func (*ProcessEvent_DataEvent) Descriptor() ([]byte, []int) {
	return file_process_process_proto_rawDescGZIP(), []int{8, 1}
}

func (m *ProcessEvent_DataEvent) GetOutput() isProcessEvent_DataEvent_Output {
	if m != nil {
		return m.Output
	}
	return nil
}

func (x *ProcessEvent_DataEvent) GetStdout() []byte {
	if x, ok := x.GetOutput().(*ProcessEvent_DataEvent_Stdout); ok {
		return x.Stdout
	}
	return nil
}

func (x *ProcessEvent_DataEvent) GetStderr() []byte {
	if x, ok := x.GetOutput().(*ProcessEvent_DataEvent_Stderr); ok {
		return x.Stderr
	}
	return nil
}

func (x *ProcessEvent_DataEvent) GetPty() []byte {
	if x, ok := x.GetOutput().(*ProcessEvent_DataEvent_Pty); ok {
		return x.Pty
	}
	return nil
}

type isProcessEvent_DataEvent_Output interface {
	isProcessEvent_DataEvent_Output()
}

type ProcessEvent_DataEvent_Stdout struct {
	Stdout []byte `protobuf:"bytes,1,opt,name=stdout,proto3,oneof"`
}

type ProcessEvent_DataEvent_Stderr struct {
	Stderr []byte `protobuf:"bytes,2,opt,name=stderr,proto3,oneof"`
}

type ProcessEvent_DataEvent_Pty struct {
	Pty []byte `protobuf:"bytes,3,opt,name=pty,proto3,oneof"`
}

func (*ProcessEvent_DataEvent_Stdout) isProcessEvent_DataEvent_Output() {}

func (*ProcessEvent_DataEvent_Stderr) isProcessEvent_DataEvent_Output() {}

func (*ProcessEvent_DataEvent_Pty) isProcessEvent_DataEvent_Output() {}

type ProcessEvent_EndEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExitCode int32   `protobuf:"zigzag32,1,opt,name=exit_code,json=exitCode,proto3" json:"exit_code,omitempty"`
	Exited   bool    `protobuf:"varint,2,opt,name=exited,proto3" json:"exited,omitempty"`
	Status   string  `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	Error    *string `protobuf:"bytes,4,opt,name=error,proto3,oneof" json:"error,omitempty"`
}

func (x *ProcessEvent_EndEvent) Reset() {
	*x = ProcessEvent_EndEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_process_process_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessEvent_EndEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessEvent_EndEvent) ProtoMessage() {}

func (x *ProcessEvent_EndEvent) ProtoReflect() protoreflect.Message {
	mi := &file_process_process_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessEvent_EndEvent.ProtoReflect.Descriptor instead.
func (*ProcessEvent_EndEvent) Descriptor() ([]byte, []int) {
	return file_process_process_proto_rawDescGZIP(), []int{8, 2}
}

func (x *ProcessEvent_EndEvent) GetExitCode() int32 {
	if x != nil {
		return x.ExitCode
	}
	return 0
}

func (x *ProcessEvent_EndEvent) GetExited() bool {
	if x != nil {
		return x.Exited
	}
	return false
}

func (x *ProcessEvent_EndEvent) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *ProcessEvent_EndEvent) GetError() string {
	if x != nil && x.Error != nil {
		return *x.Error
	}
	return ""
}

type ProcessEvent_KeepAlive struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ProcessEvent_KeepAlive) Reset() {
	*x = ProcessEvent_KeepAlive{}
	if protoimpl.UnsafeEnabled {
		mi := &file_process_process_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessEvent_KeepAlive) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessEvent_KeepAlive) ProtoMessage() {}

func (x *ProcessEvent_KeepAlive) ProtoReflect() protoreflect.Message {
	mi := &file_process_process_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessEvent_KeepAlive.ProtoReflect.Descriptor instead.
func (*ProcessEvent_KeepAlive) Descriptor() ([]byte, []int) {
	return file_process_process_proto_rawDescGZIP(), []int{8, 3}
}

type StreamInputRequest_StartEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Process *ProcessSelector `protobuf:"bytes,1,opt,name=process,proto3" json:"process,omitempty"`
}

func (x *StreamInputRequest_StartEvent) Reset() {
	*x = StreamInputRequest_StartEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_process_process_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StreamInputRequest_StartEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamInputRequest_StartEvent) ProtoMessage() {}

func (x *StreamInputRequest_StartEvent) ProtoReflect() protoreflect.Message {
	mi := &file_process_process_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamInputRequest_StartEvent.ProtoReflect.Descriptor instead.
func (*StreamInputRequest_StartEvent) Descriptor() ([]byte, []int) {
	return file_process_process_proto_rawDescGZIP(), []int{14, 0}
}

func (x *StreamInputRequest_StartEvent) GetProcess() *ProcessSelector {
	if x != nil {
		return x.Process
	}
	return nil
}

type StreamInputRequest_DataEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Input *ProcessInput `protobuf:"bytes,2,opt,name=input,proto3" json:"input,omitempty"`
}

func (x *StreamInputRequest_DataEvent) Reset() {
	*x = StreamInputRequest_DataEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_process_process_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StreamInputRequest_DataEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamInputRequest_DataEvent) ProtoMessage() {}

func (x *StreamInputRequest_DataEvent) ProtoReflect() protoreflect.Message {
	mi := &file_process_process_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamInputRequest_DataEvent.ProtoReflect.Descriptor instead.
func (*StreamInputRequest_DataEvent) Descriptor() ([]byte, []int) {
	return file_process_process_proto_rawDescGZIP(), []int{14, 1}
}

func (x *StreamInputRequest_DataEvent) GetInput() *ProcessInput {
	if x != nil {
		return x.Input
	}
	return nil
}

type StreamInputRequest_KeepAlive struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StreamInputRequest_KeepAlive) Reset() {
	*x = StreamInputRequest_KeepAlive{}
	if protoimpl.UnsafeEnabled {
		mi := &file_process_process_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StreamInputRequest_KeepAlive) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamInputRequest_KeepAlive) ProtoMessage() {}

func (x *StreamInputRequest_KeepAlive) ProtoReflect() protoreflect.Message {
	mi := &file_process_process_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamInputRequest_KeepAlive.ProtoReflect.Descriptor instead.
func (*StreamInputRequest_KeepAlive) Descriptor() ([]byte, []int) {
	return file_process_process_proto_rawDescGZIP(), []int{14, 2}
}

var File_process_process_proto protoreflect.FileDescriptor

var file_process_process_proto_rawDesc = []byte{
	0x0a, 0x15, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x22, 0x5c, 0x0a, 0x03, 0x50, 0x54, 0x59, 0x12, 0x25, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x2e,
	0x50, 0x54, 0x59, 0x2e, 0x53, 0x69, 0x7a, 0x65, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x1a, 0x2e,
	0x0a, 0x04, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x6c, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x63, 0x6f, 0x6c, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f,
	0x77, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x72, 0x6f, 0x77, 0x73, 0x22, 0xc3,
	0x01, 0x0a, 0x0d, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x10, 0x0a, 0x03, 0x63, 0x6d, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x63,
	0x6d, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x72, 0x67, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x04, 0x61, 0x72, 0x67, 0x73, 0x12, 0x34, 0x0a, 0x04, 0x65, 0x6e, 0x76, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x45, 0x6e, 0x76,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x65, 0x6e, 0x76, 0x73, 0x12, 0x15, 0x0a, 0x03,
	0x63, 0x77, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x03, 0x63, 0x77, 0x64,
	0x88, 0x01, 0x01, 0x1a, 0x37, 0x0a, 0x09, 0x45, 0x6e, 0x76, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x06, 0x0a, 0x04,
	0x5f, 0x63, 0x77, 0x64, 0x22, 0x0d, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x22, 0x6e, 0x0a, 0x0b, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x2e, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x03, 0x70, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x03, 0x74, 0x61, 0x67, 0x88, 0x01, 0x01, 0x42, 0x06, 0x0a, 0x04, 0x5f,
	0x74, 0x61, 0x67, 0x22, 0x42, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x32, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x73, 0x22, 0x8c, 0x01, 0x0a, 0x0c, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x23, 0x0a, 0x03, 0x70, 0x74,
	0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x2e, 0x50, 0x54, 0x59, 0x48, 0x00, 0x52, 0x03, 0x70, 0x74, 0x79, 0x88, 0x01, 0x01, 0x12,
	0x15, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x03,
	0x74, 0x61, 0x67, 0x88, 0x01, 0x01, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x70, 0x74, 0x79, 0x42, 0x06,
	0x0a, 0x04, 0x5f, 0x74, 0x61, 0x67, 0x22, 0x70, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x32, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x23, 0x0a, 0x03, 0x70,
	0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x2e, 0x50, 0x54, 0x59, 0x48, 0x00, 0x52, 0x03, 0x70, 0x74, 0x79, 0x88, 0x01, 0x01,
	0x42, 0x06, 0x0a, 0x04, 0x5f, 0x70, 0x74, 0x79, 0x22, 0x10, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x87, 0x04, 0x0a, 0x0c, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x38, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x05,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x35, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x32, 0x0a, 0x03,
	0x65, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x2e, 0x45, 0x6e, 0x64, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x03, 0x65, 0x6e, 0x64,
	0x12, 0x3f, 0x0a, 0x09, 0x6b, 0x65, 0x65, 0x70, 0x61, 0x6c, 0x69, 0x76, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x4b, 0x65, 0x65, 0x70, 0x41,
	0x6c, 0x69, 0x76, 0x65, 0x48, 0x00, 0x52, 0x09, 0x6b, 0x65, 0x65, 0x70, 0x61, 0x6c, 0x69, 0x76,
	0x65, 0x1a, 0x1e, 0x0a, 0x0a, 0x53, 0x74, 0x61, 0x72, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12,
	0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x70, 0x69,
	0x64, 0x1a, 0x5d, 0x0a, 0x09, 0x44, 0x61, 0x74, 0x61, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x18,
	0x0a, 0x06, 0x73, 0x74, 0x64, 0x6f, 0x75, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x00,
	0x52, 0x06, 0x73, 0x74, 0x64, 0x6f, 0x75, 0x74, 0x12, 0x18, 0x0a, 0x06, 0x73, 0x74, 0x64, 0x65,
	0x72, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x00, 0x52, 0x06, 0x73, 0x74, 0x64, 0x65,
	0x72, 0x72, 0x12, 0x12, 0x0a, 0x03, 0x70, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x48,
	0x00, 0x52, 0x03, 0x70, 0x74, 0x79, 0x42, 0x08, 0x0a, 0x06, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74,
	0x1a, 0x7c, 0x0a, 0x08, 0x45, 0x6e, 0x64, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09,
	0x65, 0x78, 0x69, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x11, 0x52,
	0x08, 0x65, 0x78, 0x69, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x78, 0x69,
	0x74, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x78, 0x69, 0x74, 0x65,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x19, 0x0a, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x1a, 0x0b,
	0x0a, 0x09, 0x4b, 0x65, 0x65, 0x70, 0x41, 0x6c, 0x69, 0x76, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x22, 0x3c, 0x0a, 0x0d, 0x53, 0x74, 0x61, 0x72, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2b, 0x0a, 0x05, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x05, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x22, 0x3e, 0x0a, 0x0f, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2b, 0x0a, 0x05, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x05, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x22, 0x73, 0x0a, 0x10, 0x53, 0x65, 0x6e, 0x64, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x32, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x2b, 0x0a, 0x05, 0x69, 0x6e,
	0x70, 0x75, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x70, 0x75, 0x74,
	0x52, 0x05, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x22, 0x13, 0x0a, 0x11, 0x53, 0x65, 0x6e, 0x64, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x43, 0x0a, 0x0c,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x16, 0x0a, 0x05,
	0x73, 0x74, 0x64, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x00, 0x52, 0x05, 0x73,
	0x74, 0x64, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x03, 0x70, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0c, 0x48, 0x00, 0x52, 0x03, 0x70, 0x74, 0x79, 0x42, 0x07, 0x0a, 0x05, 0x69, 0x6e, 0x70, 0x75,
	0x74, 0x22, 0xea, 0x02, 0x0a, 0x12, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x70, 0x75,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3e, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x2e, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x48,
	0x00, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x3b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x2e, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x48, 0x00, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x45, 0x0a, 0x09, 0x6b, 0x65, 0x65, 0x70, 0x61, 0x6c, 0x69,
	0x76, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x2e, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4b, 0x65, 0x65, 0x70, 0x41, 0x6c, 0x69, 0x76, 0x65, 0x48,
	0x00, 0x52, 0x09, 0x6b, 0x65, 0x65, 0x70, 0x61, 0x6c, 0x69, 0x76, 0x65, 0x1a, 0x40, 0x0a, 0x0a,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x32, 0x0a, 0x07, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x53, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x1a, 0x38,
	0x0a, 0x09, 0x44, 0x61, 0x74, 0x61, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x2b, 0x0a, 0x05, 0x69,
	0x6e, 0x70, 0x75, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x70, 0x75,
	0x74, 0x52, 0x05, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x1a, 0x0b, 0x0a, 0x09, 0x4b, 0x65, 0x65, 0x70,
	0x41, 0x6c, 0x69, 0x76, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x22, 0x15,
	0x0a, 0x13, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x70, 0x0a, 0x11, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x69, 0x67,
	0x6e, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x32, 0x0a, 0x07, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x53, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x27,
	0x0a, 0x06, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x52,
	0x06, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x22, 0x14, 0x0a, 0x12, 0x53, 0x65, 0x6e, 0x64, 0x53,
	0x69, 0x67, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x44, 0x0a,
	0x0e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x32, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x22, 0x45, 0x0a, 0x0f, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x53, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x12, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x48, 0x00, 0x52, 0x03, 0x70, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x03, 0x74, 0x61,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x03, 0x74, 0x61, 0x67, 0x42, 0x0a,
	0x0a, 0x08, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2a, 0x48, 0x0a, 0x06, 0x53, 0x69,
	0x67, 0x6e, 0x61, 0x6c, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x49, 0x47, 0x4e, 0x41, 0x4c, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e,
	0x53, 0x49, 0x47, 0x4e, 0x41, 0x4c, 0x5f, 0x53, 0x49, 0x47, 0x54, 0x45, 0x52, 0x4d, 0x10, 0x0f,
	0x12, 0x12, 0x0a, 0x0e, 0x53, 0x49, 0x47, 0x4e, 0x41, 0x4c, 0x5f, 0x53, 0x49, 0x47, 0x4b, 0x49,
	0x4c, 0x4c, 0x10, 0x09, 0x32, 0xca, 0x03, 0x0a, 0x07, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x12, 0x33, 0x0a, 0x04, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3e, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x12, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x30, 0x01, 0x12, 0x38, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x15,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x2e,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x30, 0x01, 0x12,
	0x39, 0x0a, 0x06, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x16, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4a, 0x0a, 0x0b, 0x53, 0x74,
	0x72, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x2e, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x2e, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x28, 0x01, 0x12, 0x42, 0x0a, 0x09, 0x53, 0x65, 0x6e, 0x64, 0x49, 0x6e,
	0x70, 0x75, 0x74, 0x12, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x53, 0x65,
	0x6e, 0x64, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a,
	0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x49, 0x6e, 0x70,
	0x75, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x45, 0x0a, 0x0a, 0x53, 0x65,
	0x6e, 0x64, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x12, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x53,
	0x65, 0x6e, 0x64, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x42, 0x97, 0x01, 0x0a, 0x0b, 0x63, 0x6f, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x42, 0x0c, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50,
	0x01, 0x5a, 0x3e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x32,
	0x62, 0x2d, 0x64, 0x65, 0x76, 0x2f, 0x69, 0x6e, 0x66, 0x72, 0x61, 0x2f, 0x70, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x73, 0x2f, 0x73, 0x68, 0x61, 0x72, 0x65, 0x64, 0x2f, 0x70, 0x6b, 0x67, 0x2f,
	0x67, 0x72, 0x70, 0x63, 0x2f, 0x65, 0x6e, 0x76, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0xa2, 0x02, 0x03, 0x50, 0x58, 0x58, 0xaa, 0x02, 0x07, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0xca, 0x02, 0x07, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0xe2, 0x02, 0x13, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x5c, 0x47, 0x50, 0x42, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0xea, 0x02, 0x07, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_process_process_proto_rawDescOnce sync.Once
	file_process_process_proto_rawDescData = file_process_process_proto_rawDesc
)

func file_process_process_proto_rawDescGZIP() []byte {
	file_process_process_proto_rawDescOnce.Do(func() {
		file_process_process_proto_rawDescData = protoimpl.X.CompressGZIP(file_process_process_proto_rawDescData)
	})
	return file_process_process_proto_rawDescData
}

var file_process_process_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_process_process_proto_msgTypes = make([]protoimpl.MessageInfo, 29)
var file_process_process_proto_goTypes = []interface{}{
	(Signal)(0),                           // 0: process.Signal
	(*PTY)(nil),                           // 1: process.PTY
	(*ProcessConfig)(nil),                 // 2: process.ProcessConfig
	(*ListRequest)(nil),                   // 3: process.ListRequest
	(*ProcessInfo)(nil),                   // 4: process.ProcessInfo
	(*ListResponse)(nil),                  // 5: process.ListResponse
	(*StartRequest)(nil),                  // 6: process.StartRequest
	(*UpdateRequest)(nil),                 // 7: process.UpdateRequest
	(*UpdateResponse)(nil),                // 8: process.UpdateResponse
	(*ProcessEvent)(nil),                  // 9: process.ProcessEvent
	(*StartResponse)(nil),                 // 10: process.StartResponse
	(*ConnectResponse)(nil),               // 11: process.ConnectResponse
	(*SendInputRequest)(nil),              // 12: process.SendInputRequest
	(*SendInputResponse)(nil),             // 13: process.SendInputResponse
	(*ProcessInput)(nil),                  // 14: process.ProcessInput
	(*StreamInputRequest)(nil),            // 15: process.StreamInputRequest
	(*StreamInputResponse)(nil),           // 16: process.StreamInputResponse
	(*SendSignalRequest)(nil),             // 17: process.SendSignalRequest
	(*SendSignalResponse)(nil),            // 18: process.SendSignalResponse
	(*ConnectRequest)(nil),                // 19: process.ConnectRequest
	(*ProcessSelector)(nil),               // 20: process.ProcessSelector
	(*PTY_Size)(nil),                      // 21: process.PTY.Size
	nil,                                   // 22: process.ProcessConfig.EnvsEntry
	(*ProcessEvent_StartEvent)(nil),       // 23: process.ProcessEvent.StartEvent
	(*ProcessEvent_DataEvent)(nil),        // 24: process.ProcessEvent.DataEvent
	(*ProcessEvent_EndEvent)(nil),         // 25: process.ProcessEvent.EndEvent
	(*ProcessEvent_KeepAlive)(nil),        // 26: process.ProcessEvent.KeepAlive
	(*StreamInputRequest_StartEvent)(nil), // 27: process.StreamInputRequest.StartEvent
	(*StreamInputRequest_DataEvent)(nil),  // 28: process.StreamInputRequest.DataEvent
	(*StreamInputRequest_KeepAlive)(nil),  // 29: process.StreamInputRequest.KeepAlive
}
var file_process_process_proto_depIdxs = []int32{
	21, // 0: process.PTY.size:type_name -> process.PTY.Size
	22, // 1: process.ProcessConfig.envs:type_name -> process.ProcessConfig.EnvsEntry
	2,  // 2: process.ProcessInfo.config:type_name -> process.ProcessConfig
	4,  // 3: process.ListResponse.processes:type_name -> process.ProcessInfo
	2,  // 4: process.StartRequest.process:type_name -> process.ProcessConfig
	1,  // 5: process.StartRequest.pty:type_name -> process.PTY
	20, // 6: process.UpdateRequest.process:type_name -> process.ProcessSelector
	1,  // 7: process.UpdateRequest.pty:type_name -> process.PTY
	23, // 8: process.ProcessEvent.start:type_name -> process.ProcessEvent.StartEvent
	24, // 9: process.ProcessEvent.data:type_name -> process.ProcessEvent.DataEvent
	25, // 10: process.ProcessEvent.end:type_name -> process.ProcessEvent.EndEvent
	26, // 11: process.ProcessEvent.keepalive:type_name -> process.ProcessEvent.KeepAlive
	9,  // 12: process.StartResponse.event:type_name -> process.ProcessEvent
	9,  // 13: process.ConnectResponse.event:type_name -> process.ProcessEvent
	20, // 14: process.SendInputRequest.process:type_name -> process.ProcessSelector
	14, // 15: process.SendInputRequest.input:type_name -> process.ProcessInput
	27, // 16: process.StreamInputRequest.start:type_name -> process.StreamInputRequest.StartEvent
	28, // 17: process.StreamInputRequest.data:type_name -> process.StreamInputRequest.DataEvent
	29, // 18: process.StreamInputRequest.keepalive:type_name -> process.StreamInputRequest.KeepAlive
	20, // 19: process.SendSignalRequest.process:type_name -> process.ProcessSelector
	0,  // 20: process.SendSignalRequest.signal:type_name -> process.Signal
	20, // 21: process.ConnectRequest.process:type_name -> process.ProcessSelector
	20, // 22: process.StreamInputRequest.StartEvent.process:type_name -> process.ProcessSelector
	14, // 23: process.StreamInputRequest.DataEvent.input:type_name -> process.ProcessInput
	3,  // 24: process.Process.List:input_type -> process.ListRequest
	19, // 25: process.Process.Connect:input_type -> process.ConnectRequest
	6,  // 26: process.Process.Start:input_type -> process.StartRequest
	7,  // 27: process.Process.Update:input_type -> process.UpdateRequest
	15, // 28: process.Process.StreamInput:input_type -> process.StreamInputRequest
	12, // 29: process.Process.SendInput:input_type -> process.SendInputRequest
	17, // 30: process.Process.SendSignal:input_type -> process.SendSignalRequest
	5,  // 31: process.Process.List:output_type -> process.ListResponse
	11, // 32: process.Process.Connect:output_type -> process.ConnectResponse
	10, // 33: process.Process.Start:output_type -> process.StartResponse
	8,  // 34: process.Process.Update:output_type -> process.UpdateResponse
	16, // 35: process.Process.StreamInput:output_type -> process.StreamInputResponse
	13, // 36: process.Process.SendInput:output_type -> process.SendInputResponse
	18, // 37: process.Process.SendSignal:output_type -> process.SendSignalResponse
	31, // [31:38] is the sub-list for method output_type
	24, // [24:31] is the sub-list for method input_type
	24, // [24:24] is the sub-list for extension type_name
	24, // [24:24] is the sub-list for extension extendee
	0,  // [0:24] is the sub-list for field type_name
}

func init() { file_process_process_proto_init() }
func file_process_process_proto_init() {
	if File_process_process_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_process_process_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PTY); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_process_process_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_process_process_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_process_process_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_process_process_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_process_process_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_process_process_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_process_process_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_process_process_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_process_process_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_process_process_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConnectResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_process_process_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendInputRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_process_process_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendInputResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_process_process_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessInput); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_process_process_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StreamInputRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_process_process_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StreamInputResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_process_process_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendSignalRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_process_process_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendSignalResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_process_process_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConnectRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_process_process_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessSelector); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_process_process_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PTY_Size); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_process_process_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessEvent_StartEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_process_process_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessEvent_DataEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_process_process_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessEvent_EndEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_process_process_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessEvent_KeepAlive); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_process_process_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StreamInputRequest_StartEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_process_process_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StreamInputRequest_DataEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_process_process_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StreamInputRequest_KeepAlive); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_process_process_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_process_process_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_process_process_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_process_process_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_process_process_proto_msgTypes[8].OneofWrappers = []interface{}{
		(*ProcessEvent_Start)(nil),
		(*ProcessEvent_Data)(nil),
		(*ProcessEvent_End)(nil),
		(*ProcessEvent_Keepalive)(nil),
	}
	file_process_process_proto_msgTypes[13].OneofWrappers = []interface{}{
		(*ProcessInput_Stdin)(nil),
		(*ProcessInput_Pty)(nil),
	}
	file_process_process_proto_msgTypes[14].OneofWrappers = []interface{}{
		(*StreamInputRequest_Start)(nil),
		(*StreamInputRequest_Data)(nil),
		(*StreamInputRequest_Keepalive)(nil),
	}
	file_process_process_proto_msgTypes[19].OneofWrappers = []interface{}{
		(*ProcessSelector_Pid)(nil),
		(*ProcessSelector_Tag)(nil),
	}
	file_process_process_proto_msgTypes[23].OneofWrappers = []interface{}{
		(*ProcessEvent_DataEvent_Stdout)(nil),
		(*ProcessEvent_DataEvent_Stderr)(nil),
		(*ProcessEvent_DataEvent_Pty)(nil),
	}
	file_process_process_proto_msgTypes[24].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_process_process_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   29,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_process_process_proto_goTypes,
		DependencyIndexes: file_process_process_proto_depIdxs,
		EnumInfos:         file_process_process_proto_enumTypes,
		MessageInfos:      file_process_process_proto_msgTypes,
	}.Build()
	File_process_process_proto = out.File
	file_process_process_proto_rawDesc = nil
	file_process_process_proto_goTypes = nil
	file_process_process_proto_depIdxs = nil
}
