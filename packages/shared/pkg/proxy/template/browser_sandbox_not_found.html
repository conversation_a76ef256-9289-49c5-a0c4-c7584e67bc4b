<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title>Sandbox Not Found</title>
    <style>:root{--brand:#ff8800;--error:#dc2626;--error-light:#fef2f2;--text:#1a1a1a;--background:#ffffff;--border:#e5e7eb;--details-bg:#f9fafb;--code-text:#374151;--muted-text:#6b7280}@media (prefers-color-scheme:dark){:root{--error:#ef4444;--error-light:#2a0f0f;--text:#e5e7eb;--background:#121212;--border:#2f2f2f;--details-bg:#1c1c1c;--code-text:#d1d5db;--muted-text:#9ca3af}}*{margin:0;padding:0;box-sizing:border-box}body{font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Aria<PERSON>,sans-serif;background:#f5f5f5;min-height:100vh;display:flex;align-items:center;justify-content:center;padding:1rem;color:var(--text)}@media (prefers-color-scheme:dark){body{background:#0a0a0a}}.error-card{background:var(--background);border-radius:12px;box-shadow:0 4px 6px -1px rgb(0 0 0 / .1),0 2px 4px -2px rgb(0 0 0 / .1);width:100%;max-width:600px;padding:1.5rem 2rem 2rem;position:relative}.logo{position:absolute;top:1rem;right:1.5rem;width:40px;height:40px;border-radius:50%;overflow:hidden}.error-header{margin-bottom:1.5rem;padding-right:3.5rem}.error-title{display:inline-block;color:var(--error);font-size:.9375rem;font-weight:500;margin-bottom:1rem;padding:.25rem .5rem;background:var(--error-light);border-radius:4px}.error-message{font-size:1.125rem;line-height:1.5;color:var(--error);font-weight:400;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,sans-serif}.error-details{background:var(--details-bg);border:1px solid var(--border);border-radius:8px;padding:1rem;margin-top:1.5rem}.error-code{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,monospace;font-size:.875rem;color:var(--code-text)}.sandbox-url{color:var(--muted-text);font-size:.875rem;display:block;margin-bottom:.5rem}.highlight{font-weight:700}.help-text{margin-top:1.5rem;font-size:.875rem;color:var(--muted-text)}.debug-link{display:block;margin-top:2rem;color:var(--brand);text-decoration:none;font-size:.875rem}.debug-link:hover{text-decoration:underline}@media (max-width:640px){.error-card{margin:1rem;padding:1.25rem 1.5rem 1.5rem}.logo{top:.75rem;right:1rem;width:32px;height:32px}.error-header{padding-right:2.5rem}}</style>
</head>
<body>
    <main class="error-card">
        <img src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Symbol%20Gradient-Kr5pnWlK3ZhzBcRGf6Am4cNbJvY1Ge.svg" alt="Logo" class="logo">
        <div class="error-header">
            <h1 class="error-title">Sandbox Not Found</h1>
            <p class="error-message">The sandbox <span class="highlight" id="sandbox-id">{{.SandboxId}}</span> wasn't found.</p>
        </div>
    </main>
</body>
</html>