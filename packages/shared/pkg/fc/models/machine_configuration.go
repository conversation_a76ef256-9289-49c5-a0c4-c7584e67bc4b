// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"encoding/json"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
	"github.com/go-openapi/validate"
)

// MachineConfiguration Describes the number of vCPUs, memory size, SMT capabilities, huge page configuration and the CPU template.
//
// swagger:model MachineConfiguration
type MachineConfiguration struct {

	// cpu template
	CPUTemplate *CPUTemplate `json:"cpu_template,omitempty"`

	// Which huge pages configuration (if any) should be used to back guest memory.
	// Enum: [None 2M]
	HugePages string `json:"huge_pages,omitempty"`

	// Memory size of VM
	// Required: true
	MemSizeMib *int64 `json:"mem_size_mib"`

	// Flag for enabling/disabling simultaneous multithreading. Can be enabled only on x86.
	Smt *bool `json:"smt,omitempty"`

	// Enable dirty page tracking. If this is enabled, then incremental guest memory snapshots can be created. These belong to diff snapshots, which contain, besides the microVM state, only the memory dirtied since a previous snapshot. Full snapshots each contain a full copy of the guest memory.
	TrackDirtyPages *bool `json:"track_dirty_pages,omitempty"`

	// Number of vCPUs (either 1 or an even number)
	// Required: true
	// Maximum: 32
	// Minimum: 1
	VcpuCount *int64 `json:"vcpu_count"`
}

// Validate validates this machine configuration
func (m *MachineConfiguration) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateCPUTemplate(formats); err != nil {
		res = append(res, err)
	}

	if err := m.validateHugePages(formats); err != nil {
		res = append(res, err)
	}

	if err := m.validateMemSizeMib(formats); err != nil {
		res = append(res, err)
	}

	if err := m.validateVcpuCount(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *MachineConfiguration) validateCPUTemplate(formats strfmt.Registry) error {
	if swag.IsZero(m.CPUTemplate) { // not required
		return nil
	}

	if m.CPUTemplate != nil {
		if err := m.CPUTemplate.Validate(formats); err != nil {
			if ve, ok := err.(*errors.Validation); ok {
				return ve.ValidateName("cpu_template")
			} else if ce, ok := err.(*errors.CompositeError); ok {
				return ce.ValidateName("cpu_template")
			}
			return err
		}
	}

	return nil
}

var machineConfigurationTypeHugePagesPropEnum []interface{}

func init() {
	var res []string
	if err := json.Unmarshal([]byte(`["None","2M"]`), &res); err != nil {
		panic(err)
	}
	for _, v := range res {
		machineConfigurationTypeHugePagesPropEnum = append(machineConfigurationTypeHugePagesPropEnum, v)
	}
}

const (

	// MachineConfigurationHugePagesNone captures enum value "None"
	MachineConfigurationHugePagesNone string = "None"

	// MachineConfigurationHugePagesNr2M captures enum value "2M"
	MachineConfigurationHugePagesNr2M string = "2M"
)

// prop value enum
func (m *MachineConfiguration) validateHugePagesEnum(path, location string, value string) error {
	if err := validate.EnumCase(path, location, value, machineConfigurationTypeHugePagesPropEnum, true); err != nil {
		return err
	}
	return nil
}

func (m *MachineConfiguration) validateHugePages(formats strfmt.Registry) error {
	if swag.IsZero(m.HugePages) { // not required
		return nil
	}

	// value enum
	if err := m.validateHugePagesEnum("huge_pages", "body", m.HugePages); err != nil {
		return err
	}

	return nil
}

func (m *MachineConfiguration) validateMemSizeMib(formats strfmt.Registry) error {

	if err := validate.Required("mem_size_mib", "body", m.MemSizeMib); err != nil {
		return err
	}

	return nil
}

func (m *MachineConfiguration) validateVcpuCount(formats strfmt.Registry) error {

	if err := validate.Required("vcpu_count", "body", m.VcpuCount); err != nil {
		return err
	}

	if err := validate.MinimumInt("vcpu_count", "body", *m.VcpuCount, 1, false); err != nil {
		return err
	}

	if err := validate.MaximumInt("vcpu_count", "body", *m.VcpuCount, 32, false); err != nil {
		return err
	}

	return nil
}

// ContextValidate validate this machine configuration based on the context it is used
func (m *MachineConfiguration) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	var res []error

	if err := m.contextValidateCPUTemplate(ctx, formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *MachineConfiguration) contextValidateCPUTemplate(ctx context.Context, formats strfmt.Registry) error {

	if m.CPUTemplate != nil {

		if swag.IsZero(m.CPUTemplate) { // not required
			return nil
		}

		if err := m.CPUTemplate.ContextValidate(ctx, formats); err != nil {
			if ve, ok := err.(*errors.Validation); ok {
				return ve.ValidateName("cpu_template")
			} else if ce, ok := err.(*errors.CompositeError); ok {
				return ce.ValidateName("cpu_template")
			}
			return err
		}
	}

	return nil
}

// MarshalBinary interface implementation
func (m *MachineConfiguration) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *MachineConfiguration) UnmarshalBinary(b []byte) error {
	var res MachineConfiguration
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
