// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"encoding/json"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
	"github.com/go-openapi/validate"
)

// SnapshotCreateParams snapshot create params
//
// swagger:model SnapshotCreateParams
type SnapshotCreateParams struct {

	// Path to the file that will contain the guest memory.
	// Required: true
	MemFilePath *string `json:"mem_file_path"`

	// Path to the file that will contain the microVM state.
	// Required: true
	SnapshotPath *string `json:"snapshot_path"`

	// Type of snapshot to create. It is optional and by default, a full snapshot is created.
	// Enum: [Full Diff]
	SnapshotType string `json:"snapshot_type,omitempty"`
}

// Validate validates this snapshot create params
func (m *SnapshotCreateParams) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateMemFilePath(formats); err != nil {
		res = append(res, err)
	}

	if err := m.validateSnapshotPath(formats); err != nil {
		res = append(res, err)
	}

	if err := m.validateSnapshotType(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *SnapshotCreateParams) validateMemFilePath(formats strfmt.Registry) error {

	if err := validate.Required("mem_file_path", "body", m.MemFilePath); err != nil {
		return err
	}

	return nil
}

func (m *SnapshotCreateParams) validateSnapshotPath(formats strfmt.Registry) error {

	if err := validate.Required("snapshot_path", "body", m.SnapshotPath); err != nil {
		return err
	}

	return nil
}

var snapshotCreateParamsTypeSnapshotTypePropEnum []interface{}

func init() {
	var res []string
	if err := json.Unmarshal([]byte(`["Full","Diff"]`), &res); err != nil {
		panic(err)
	}
	for _, v := range res {
		snapshotCreateParamsTypeSnapshotTypePropEnum = append(snapshotCreateParamsTypeSnapshotTypePropEnum, v)
	}
}

const (

	// SnapshotCreateParamsSnapshotTypeFull captures enum value "Full"
	SnapshotCreateParamsSnapshotTypeFull string = "Full"

	// SnapshotCreateParamsSnapshotTypeDiff captures enum value "Diff"
	SnapshotCreateParamsSnapshotTypeDiff string = "Diff"
)

// prop value enum
func (m *SnapshotCreateParams) validateSnapshotTypeEnum(path, location string, value string) error {
	if err := validate.EnumCase(path, location, value, snapshotCreateParamsTypeSnapshotTypePropEnum, true); err != nil {
		return err
	}
	return nil
}

func (m *SnapshotCreateParams) validateSnapshotType(formats strfmt.Registry) error {
	if swag.IsZero(m.SnapshotType) { // not required
		return nil
	}

	// value enum
	if err := m.validateSnapshotTypeEnum("snapshot_type", "body", m.SnapshotType); err != nil {
		return err
	}

	return nil
}

// ContextValidate validates this snapshot create params based on context it is used
func (m *SnapshotCreateParams) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *SnapshotCreateParams) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *SnapshotCreateParams) UnmarshalBinary(b []byte) error {
	var res SnapshotCreateParams
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
