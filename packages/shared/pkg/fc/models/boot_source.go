// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
	"github.com/go-openapi/validate"
)

// BootSource Boot source descriptor.
//
// swagger:model BootSource
type BootSource struct {

	// Kernel boot arguments
	BootArgs string `json:"boot_args,omitempty"`

	// Host level path to the initrd image used to boot the guest
	InitrdPath string `json:"initrd_path,omitempty"`

	// Host level path to the kernel image used to boot the guest
	// Required: true
	KernelImagePath *string `json:"kernel_image_path"`
}

// Validate validates this boot source
func (m *BootSource) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateKernelImagePath(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *BootSource) validateKernelImagePath(formats strfmt.Registry) error {

	if err := validate.Required("kernel_image_path", "body", m.KernelImagePath); err != nil {
		return err
	}

	return nil
}

// ContextValidate validates this boot source based on context it is used
func (m *BootSource) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *BootSource) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *BootSource) UnmarshalBinary(b []byte) error {
	var res BootSource
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
