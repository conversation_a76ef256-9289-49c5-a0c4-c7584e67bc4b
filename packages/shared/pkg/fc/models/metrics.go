// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
	"github.com/go-openapi/validate"
)

// Metrics Describes the configuration option for the metrics capability.
//
// swagger:model Metrics
type Metrics struct {

	// Path to the named pipe or file where the JSON-formatted metrics are flushed.
	// Required: true
	MetricsPath *string `json:"metrics_path"`
}

// Validate validates this metrics
func (m *Metrics) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateMetricsPath(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *Metrics) validateMetricsPath(formats strfmt.Registry) error {

	if err := validate.Required("metrics_path", "body", m.MetricsPath); err != nil {
		return err
	}

	return nil
}

// ContextValidate validates this metrics based on context it is used
func (m *Metrics) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *Metrics) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *Metrics) UnmarshalBinary(b []byte) error {
	var res Metrics
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
