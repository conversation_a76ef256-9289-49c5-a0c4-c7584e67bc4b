// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// EntropyDevice Defines an entropy device.
//
// swagger:model EntropyDevice
type EntropyDevice struct {

	// rate limiter
	RateLimiter *RateLimiter `json:"rate_limiter,omitempty"`
}

// Validate validates this entropy device
func (m *EntropyDevice) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateRateLimiter(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *EntropyDevice) validateRateLimiter(formats strfmt.Registry) error {
	if swag.IsZero(m.RateLimiter) { // not required
		return nil
	}

	if m.RateLimiter != nil {
		if err := m.RateLimiter.Validate(formats); err != nil {
			if ve, ok := err.(*errors.Validation); ok {
				return ve.ValidateName("rate_limiter")
			} else if ce, ok := err.(*errors.CompositeError); ok {
				return ce.ValidateName("rate_limiter")
			}
			return err
		}
	}

	return nil
}

// ContextValidate validate this entropy device based on the context it is used
func (m *EntropyDevice) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	var res []error

	if err := m.contextValidateRateLimiter(ctx, formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *EntropyDevice) contextValidateRateLimiter(ctx context.Context, formats strfmt.Registry) error {

	if m.RateLimiter != nil {

		if swag.IsZero(m.RateLimiter) { // not required
			return nil
		}

		if err := m.RateLimiter.ContextValidate(ctx, formats); err != nil {
			if ve, ok := err.(*errors.Validation); ok {
				return ve.ValidateName("rate_limiter")
			} else if ce, ok := err.(*errors.CompositeError); ok {
				return ce.ValidateName("rate_limiter")
			}
			return err
		}
	}

	return nil
}

// MarshalBinary interface implementation
func (m *EntropyDevice) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *EntropyDevice) UnmarshalBinary(b []byte) error {
	var res EntropyDevice
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
