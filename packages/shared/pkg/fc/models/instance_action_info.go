// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"encoding/json"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
	"github.com/go-openapi/validate"
)

// InstanceActionInfo Variant wrapper containing the real action.
//
// swagger:model InstanceActionInfo
type InstanceActionInfo struct {

	// Enumeration indicating what type of action is contained in the payload
	// Required: true
	// Enum: [FlushMetrics InstanceStart SendCtrlAltDel]
	ActionType *string `json:"action_type"`
}

// Validate validates this instance action info
func (m *InstanceActionInfo) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateActionType(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

var instanceActionInfoTypeActionTypePropEnum []interface{}

func init() {
	var res []string
	if err := json.Unmarshal([]byte(`["FlushMetrics","InstanceStart","SendCtrlAltDel"]`), &res); err != nil {
		panic(err)
	}
	for _, v := range res {
		instanceActionInfoTypeActionTypePropEnum = append(instanceActionInfoTypeActionTypePropEnum, v)
	}
}

const (

	// InstanceActionInfoActionTypeFlushMetrics captures enum value "FlushMetrics"
	InstanceActionInfoActionTypeFlushMetrics string = "FlushMetrics"

	// InstanceActionInfoActionTypeInstanceStart captures enum value "InstanceStart"
	InstanceActionInfoActionTypeInstanceStart string = "InstanceStart"

	// InstanceActionInfoActionTypeSendCtrlAltDel captures enum value "SendCtrlAltDel"
	InstanceActionInfoActionTypeSendCtrlAltDel string = "SendCtrlAltDel"
)

// prop value enum
func (m *InstanceActionInfo) validateActionTypeEnum(path, location string, value string) error {
	if err := validate.EnumCase(path, location, value, instanceActionInfoTypeActionTypePropEnum, true); err != nil {
		return err
	}
	return nil
}

func (m *InstanceActionInfo) validateActionType(formats strfmt.Registry) error {

	if err := validate.Required("action_type", "body", m.ActionType); err != nil {
		return err
	}

	// value enum
	if err := m.validateActionTypeEnum("action_type", "body", *m.ActionType); err != nil {
		return err
	}

	return nil
}

// ContextValidate validates this instance action info based on context it is used
func (m *InstanceActionInfo) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *InstanceActionInfo) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *InstanceActionInfo) UnmarshalBinary(b []byte) error {
	var res InstanceActionInfo
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
