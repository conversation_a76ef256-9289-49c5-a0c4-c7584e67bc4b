// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
	"github.com/go-openapi/validate"
)

// FirecrackerVersion Describes the Firecracker version.
//
// swagger:model FirecrackerVersion
type FirecrackerVersion struct {

	// Firecracker build version.
	// Required: true
	FirecrackerVersion *string `json:"firecracker_version"`
}

// Validate validates this firecracker version
func (m *FirecrackerVersion) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateFirecrackerVersion(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *FirecrackerVersion) validateFirecrackerVersion(formats strfmt.Registry) error {

	if err := validate.Required("firecracker_version", "body", m.FirecrackerVersion); err != nil {
		return err
	}

	return nil
}

// ContextValidate validates this firecracker version based on context it is used
func (m *FirecrackerVersion) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *FirecrackerVersion) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *FirecrackerVersion) UnmarshalBinary(b []byte) error {
	var res FirecrackerVersion
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
