// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"encoding/json"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
	"github.com/go-openapi/validate"
)

// VM Defines the microVM running state. It is especially useful in the snapshotting context.
//
// swagger:model Vm
type VM struct {

	// state
	// Required: true
	// Enum: [Paused Resumed]
	State *string `json:"state"`
}

// Validate validates this Vm
func (m *VM) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateState(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

var vmTypeStatePropEnum []interface{}

func init() {
	var res []string
	if err := json.Unmarshal([]byte(`["Paused","Resumed"]`), &res); err != nil {
		panic(err)
	}
	for _, v := range res {
		vmTypeStatePropEnum = append(vmTypeStatePropEnum, v)
	}
}

const (

	// VMStatePaused captures enum value "Paused"
	VMStatePaused string = "Paused"

	// VMStateResumed captures enum value "Resumed"
	VMStateResumed string = "Resumed"
)

// prop value enum
func (m *VM) validateStateEnum(path, location string, value string) error {
	if err := validate.EnumCase(path, location, value, vmTypeStatePropEnum, true); err != nil {
		return err
	}
	return nil
}

func (m *VM) validateState(formats strfmt.Registry) error {

	if err := validate.Required("state", "body", m.State); err != nil {
		return err
	}

	// value enum
	if err := m.validateStateEnum("state", "body", *m.State); err != nil {
		return err
	}

	return nil
}

// ContextValidate validates this Vm based on context it is used
func (m *VM) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *VM) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *VM) UnmarshalBinary(b []byte) error {
	var res VM
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
