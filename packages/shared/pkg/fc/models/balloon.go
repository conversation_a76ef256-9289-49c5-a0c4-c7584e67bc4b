// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
	"github.com/go-openapi/validate"
)

// Balloon Balloon device descriptor.
//
// swagger:model Balloon
type Balloon struct {

	// Target balloon size in MiB.
	// Required: true
	AmountMib *int64 `json:"amount_mib"`

	// Whether the balloon should deflate when the guest has memory pressure.
	// Required: true
	DeflateOnOom *bool `json:"deflate_on_oom"`

	// Interval in seconds between refreshing statistics. A non-zero value will enable the statistics. Defaults to 0.
	StatsPollingIntervals int64 `json:"stats_polling_interval_s,omitempty"`
}

// Validate validates this balloon
func (m *Balloon) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateAmountMib(formats); err != nil {
		res = append(res, err)
	}

	if err := m.validateDeflateOnOom(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *Balloon) validateAmountMib(formats strfmt.Registry) error {

	if err := validate.Required("amount_mib", "body", m.AmountMib); err != nil {
		return err
	}

	return nil
}

func (m *Balloon) validateDeflateOnOom(formats strfmt.Registry) error {

	if err := validate.Required("deflate_on_oom", "body", m.DeflateOnOom); err != nil {
		return err
	}

	return nil
}

// ContextValidate validates this balloon based on context it is used
func (m *Balloon) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *Balloon) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *Balloon) UnmarshalBinary(b []byte) error {
	var res Balloon
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
