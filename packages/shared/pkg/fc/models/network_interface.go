// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
	"github.com/go-openapi/validate"
)

// NetworkInterface Defines a network interface.
//
// swagger:model NetworkInterface
type NetworkInterface struct {

	// guest mac
	GuestMac string `json:"guest_mac,omitempty"`

	// Host level path for the guest network interface
	// Required: true
	HostDevName *string `json:"host_dev_name"`

	// iface id
	// Required: true
	IfaceID *string `json:"iface_id"`

	// rx rate limiter
	RxRateLimiter *RateLimiter `json:"rx_rate_limiter,omitempty"`

	// tx rate limiter
	TxRateLimiter *RateLimiter `json:"tx_rate_limiter,omitempty"`
}

// Validate validates this network interface
func (m *NetworkInterface) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateHostDevName(formats); err != nil {
		res = append(res, err)
	}

	if err := m.validateIfaceID(formats); err != nil {
		res = append(res, err)
	}

	if err := m.validateRxRateLimiter(formats); err != nil {
		res = append(res, err)
	}

	if err := m.validateTxRateLimiter(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *NetworkInterface) validateHostDevName(formats strfmt.Registry) error {

	if err := validate.Required("host_dev_name", "body", m.HostDevName); err != nil {
		return err
	}

	return nil
}

func (m *NetworkInterface) validateIfaceID(formats strfmt.Registry) error {

	if err := validate.Required("iface_id", "body", m.IfaceID); err != nil {
		return err
	}

	return nil
}

func (m *NetworkInterface) validateRxRateLimiter(formats strfmt.Registry) error {
	if swag.IsZero(m.RxRateLimiter) { // not required
		return nil
	}

	if m.RxRateLimiter != nil {
		if err := m.RxRateLimiter.Validate(formats); err != nil {
			if ve, ok := err.(*errors.Validation); ok {
				return ve.ValidateName("rx_rate_limiter")
			} else if ce, ok := err.(*errors.CompositeError); ok {
				return ce.ValidateName("rx_rate_limiter")
			}
			return err
		}
	}

	return nil
}

func (m *NetworkInterface) validateTxRateLimiter(formats strfmt.Registry) error {
	if swag.IsZero(m.TxRateLimiter) { // not required
		return nil
	}

	if m.TxRateLimiter != nil {
		if err := m.TxRateLimiter.Validate(formats); err != nil {
			if ve, ok := err.(*errors.Validation); ok {
				return ve.ValidateName("tx_rate_limiter")
			} else if ce, ok := err.(*errors.CompositeError); ok {
				return ce.ValidateName("tx_rate_limiter")
			}
			return err
		}
	}

	return nil
}

// ContextValidate validate this network interface based on the context it is used
func (m *NetworkInterface) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	var res []error

	if err := m.contextValidateRxRateLimiter(ctx, formats); err != nil {
		res = append(res, err)
	}

	if err := m.contextValidateTxRateLimiter(ctx, formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *NetworkInterface) contextValidateRxRateLimiter(ctx context.Context, formats strfmt.Registry) error {

	if m.RxRateLimiter != nil {

		if swag.IsZero(m.RxRateLimiter) { // not required
			return nil
		}

		if err := m.RxRateLimiter.ContextValidate(ctx, formats); err != nil {
			if ve, ok := err.(*errors.Validation); ok {
				return ve.ValidateName("rx_rate_limiter")
			} else if ce, ok := err.(*errors.CompositeError); ok {
				return ce.ValidateName("rx_rate_limiter")
			}
			return err
		}
	}

	return nil
}

func (m *NetworkInterface) contextValidateTxRateLimiter(ctx context.Context, formats strfmt.Registry) error {

	if m.TxRateLimiter != nil {

		if swag.IsZero(m.TxRateLimiter) { // not required
			return nil
		}

		if err := m.TxRateLimiter.ContextValidate(ctx, formats); err != nil {
			if ve, ok := err.(*errors.Validation); ok {
				return ve.ValidateName("tx_rate_limiter")
			} else if ce, ok := err.(*errors.CompositeError); ok {
				return ce.ValidateName("tx_rate_limiter")
			}
			return err
		}
	}

	return nil
}

// MarshalBinary interface implementation
func (m *NetworkInterface) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *NetworkInterface) UnmarshalBinary(b []byte) error {
	var res NetworkInterface
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
