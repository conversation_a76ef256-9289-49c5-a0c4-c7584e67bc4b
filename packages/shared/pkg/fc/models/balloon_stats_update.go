// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
	"github.com/go-openapi/validate"
)

// BalloonStatsUpdate Update the statistics polling interval, with the first statistics update scheduled immediately. Statistics cannot be turned on/off after boot.
//
// swagger:model BalloonStatsUpdate
type BalloonStatsUpdate struct {

	// Interval in seconds between refreshing statistics.
	// Required: true
	StatsPollingIntervals *int64 `json:"stats_polling_interval_s"`
}

// Validate validates this balloon stats update
func (m *BalloonStatsUpdate) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateStatsPollingIntervals(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *BalloonStatsUpdate) validateStatsPollingIntervals(formats strfmt.Registry) error {

	if err := validate.Required("stats_polling_interval_s", "body", m.StatsPollingIntervals); err != nil {
		return err
	}

	return nil
}

// ContextValidate validates this balloon stats update based on context it is used
func (m *BalloonStatsUpdate) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *BalloonStatsUpdate) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *BalloonStatsUpdate) UnmarshalBinary(b []byte) error {
	var res BalloonStatsUpdate
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
