// Code generated by go-swagger; DO NOT EDIT.

package operations

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
)

// NewGetMachineConfigurationParams creates a new GetMachineConfigurationParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewGetMachineConfigurationParams() *GetMachineConfigurationParams {
	return &GetMachineConfigurationParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewGetMachineConfigurationParamsWithTimeout creates a new GetMachineConfigurationParams object
// with the ability to set a timeout on a request.
func NewGetMachineConfigurationParamsWithTimeout(timeout time.Duration) *GetMachineConfigurationParams {
	return &GetMachineConfigurationParams{
		timeout: timeout,
	}
}

// NewGetMachineConfigurationParamsWithContext creates a new GetMachineConfigurationParams object
// with the ability to set a context for a request.
func NewGetMachineConfigurationParamsWithContext(ctx context.Context) *GetMachineConfigurationParams {
	return &GetMachineConfigurationParams{
		Context: ctx,
	}
}

// NewGetMachineConfigurationParamsWithHTTPClient creates a new GetMachineConfigurationParams object
// with the ability to set a custom HTTPClient for a request.
func NewGetMachineConfigurationParamsWithHTTPClient(client *http.Client) *GetMachineConfigurationParams {
	return &GetMachineConfigurationParams{
		HTTPClient: client,
	}
}

/*
GetMachineConfigurationParams contains all the parameters to send to the API endpoint

	for the get machine configuration operation.

	Typically these are written to a http.Request.
*/
type GetMachineConfigurationParams struct {
	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the get machine configuration params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *GetMachineConfigurationParams) WithDefaults() *GetMachineConfigurationParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the get machine configuration params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *GetMachineConfigurationParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the get machine configuration params
func (o *GetMachineConfigurationParams) WithTimeout(timeout time.Duration) *GetMachineConfigurationParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the get machine configuration params
func (o *GetMachineConfigurationParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the get machine configuration params
func (o *GetMachineConfigurationParams) WithContext(ctx context.Context) *GetMachineConfigurationParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the get machine configuration params
func (o *GetMachineConfigurationParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the get machine configuration params
func (o *GetMachineConfigurationParams) WithHTTPClient(client *http.Client) *GetMachineConfigurationParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the get machine configuration params
func (o *GetMachineConfigurationParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WriteToRequest writes these params to a swagger request
func (o *GetMachineConfigurationParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
