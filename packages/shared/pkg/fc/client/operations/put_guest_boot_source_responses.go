// Code generated by go-swagger; DO NOT EDIT.

package operations

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"

	"github.com/e2b-dev/infra/packages/shared/pkg/fc/models"
)

// PutGuestBootSourceReader is a Reader for the PutGuestBootSource structure.
type PutGuestBootSourceReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *PutGuestBootSourceReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 204:
		result := NewPutGuestBootSourceNoContent()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 400:
		result := NewPutGuestBootSourceBadRequest()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		result := NewPutGuestBootSourceDefault(response.Code())
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		if response.Code()/100 == 2 {
			return result, nil
		}
		return nil, result
	}
}

// NewPutGuestBootSourceNoContent creates a PutGuestBootSourceNoContent with default headers values
func NewPutGuestBootSourceNoContent() *PutGuestBootSourceNoContent {
	return &PutGuestBootSourceNoContent{}
}

/*
PutGuestBootSourceNoContent describes a response with status code 204, with default header values.

Boot source created/updated
*/
type PutGuestBootSourceNoContent struct {
}

// IsSuccess returns true when this put guest boot source no content response has a 2xx status code
func (o *PutGuestBootSourceNoContent) IsSuccess() bool {
	return true
}

// IsRedirect returns true when this put guest boot source no content response has a 3xx status code
func (o *PutGuestBootSourceNoContent) IsRedirect() bool {
	return false
}

// IsClientError returns true when this put guest boot source no content response has a 4xx status code
func (o *PutGuestBootSourceNoContent) IsClientError() bool {
	return false
}

// IsServerError returns true when this put guest boot source no content response has a 5xx status code
func (o *PutGuestBootSourceNoContent) IsServerError() bool {
	return false
}

// IsCode returns true when this put guest boot source no content response a status code equal to that given
func (o *PutGuestBootSourceNoContent) IsCode(code int) bool {
	return code == 204
}

// Code gets the status code for the put guest boot source no content response
func (o *PutGuestBootSourceNoContent) Code() int {
	return 204
}

func (o *PutGuestBootSourceNoContent) Error() string {
	return fmt.Sprintf("[PUT /boot-source][%d] putGuestBootSourceNoContent ", 204)
}

func (o *PutGuestBootSourceNoContent) String() string {
	return fmt.Sprintf("[PUT /boot-source][%d] putGuestBootSourceNoContent ", 204)
}

func (o *PutGuestBootSourceNoContent) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewPutGuestBootSourceBadRequest creates a PutGuestBootSourceBadRequest with default headers values
func NewPutGuestBootSourceBadRequest() *PutGuestBootSourceBadRequest {
	return &PutGuestBootSourceBadRequest{}
}

/*
PutGuestBootSourceBadRequest describes a response with status code 400, with default header values.

Boot source cannot be created due to bad input
*/
type PutGuestBootSourceBadRequest struct {
	Payload *models.Error
}

// IsSuccess returns true when this put guest boot source bad request response has a 2xx status code
func (o *PutGuestBootSourceBadRequest) IsSuccess() bool {
	return false
}

// IsRedirect returns true when this put guest boot source bad request response has a 3xx status code
func (o *PutGuestBootSourceBadRequest) IsRedirect() bool {
	return false
}

// IsClientError returns true when this put guest boot source bad request response has a 4xx status code
func (o *PutGuestBootSourceBadRequest) IsClientError() bool {
	return true
}

// IsServerError returns true when this put guest boot source bad request response has a 5xx status code
func (o *PutGuestBootSourceBadRequest) IsServerError() bool {
	return false
}

// IsCode returns true when this put guest boot source bad request response a status code equal to that given
func (o *PutGuestBootSourceBadRequest) IsCode(code int) bool {
	return code == 400
}

// Code gets the status code for the put guest boot source bad request response
func (o *PutGuestBootSourceBadRequest) Code() int {
	return 400
}

func (o *PutGuestBootSourceBadRequest) Error() string {
	return fmt.Sprintf("[PUT /boot-source][%d] putGuestBootSourceBadRequest  %+v", 400, o.Payload)
}

func (o *PutGuestBootSourceBadRequest) String() string {
	return fmt.Sprintf("[PUT /boot-source][%d] putGuestBootSourceBadRequest  %+v", 400, o.Payload)
}

func (o *PutGuestBootSourceBadRequest) GetPayload() *models.Error {
	return o.Payload
}

func (o *PutGuestBootSourceBadRequest) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.Error)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewPutGuestBootSourceDefault creates a PutGuestBootSourceDefault with default headers values
func NewPutGuestBootSourceDefault(code int) *PutGuestBootSourceDefault {
	return &PutGuestBootSourceDefault{
		_statusCode: code,
	}
}

/*
PutGuestBootSourceDefault describes a response with status code -1, with default header values.

Internal server error
*/
type PutGuestBootSourceDefault struct {
	_statusCode int

	Payload *models.Error
}

// IsSuccess returns true when this put guest boot source default response has a 2xx status code
func (o *PutGuestBootSourceDefault) IsSuccess() bool {
	return o._statusCode/100 == 2
}

// IsRedirect returns true when this put guest boot source default response has a 3xx status code
func (o *PutGuestBootSourceDefault) IsRedirect() bool {
	return o._statusCode/100 == 3
}

// IsClientError returns true when this put guest boot source default response has a 4xx status code
func (o *PutGuestBootSourceDefault) IsClientError() bool {
	return o._statusCode/100 == 4
}

// IsServerError returns true when this put guest boot source default response has a 5xx status code
func (o *PutGuestBootSourceDefault) IsServerError() bool {
	return o._statusCode/100 == 5
}

// IsCode returns true when this put guest boot source default response a status code equal to that given
func (o *PutGuestBootSourceDefault) IsCode(code int) bool {
	return o._statusCode == code
}

// Code gets the status code for the put guest boot source default response
func (o *PutGuestBootSourceDefault) Code() int {
	return o._statusCode
}

func (o *PutGuestBootSourceDefault) Error() string {
	return fmt.Sprintf("[PUT /boot-source][%d] putGuestBootSource default  %+v", o._statusCode, o.Payload)
}

func (o *PutGuestBootSourceDefault) String() string {
	return fmt.Sprintf("[PUT /boot-source][%d] putGuestBootSource default  %+v", o._statusCode, o.Payload)
}

func (o *PutGuestBootSourceDefault) GetPayload() *models.Error {
	return o.Payload
}

func (o *PutGuestBootSourceDefault) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.Error)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}
