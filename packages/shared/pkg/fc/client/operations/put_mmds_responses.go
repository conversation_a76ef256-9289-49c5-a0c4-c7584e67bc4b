// Code generated by go-swagger; DO NOT EDIT.

package operations

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"

	"github.com/e2b-dev/infra/packages/shared/pkg/fc/models"
)

// PutMmdsReader is a Reader for the PutMmds structure.
type PutMmdsReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *PutMmdsReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 204:
		result := NewPutMmdsNoContent()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 400:
		result := NewPutMmdsBadRequest()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		result := NewPutMmdsDefault(response.Code())
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		if response.Code()/100 == 2 {
			return result, nil
		}
		return nil, result
	}
}

// NewPutMmdsNoContent creates a PutMmdsNoContent with default headers values
func NewPutMmdsNoContent() *PutMmdsNoContent {
	return &PutMmdsNoContent{}
}

/*
PutMmdsNoContent describes a response with status code 204, with default header values.

MMDS data store created/updated.
*/
type PutMmdsNoContent struct {
}

// IsSuccess returns true when this put mmds no content response has a 2xx status code
func (o *PutMmdsNoContent) IsSuccess() bool {
	return true
}

// IsRedirect returns true when this put mmds no content response has a 3xx status code
func (o *PutMmdsNoContent) IsRedirect() bool {
	return false
}

// IsClientError returns true when this put mmds no content response has a 4xx status code
func (o *PutMmdsNoContent) IsClientError() bool {
	return false
}

// IsServerError returns true when this put mmds no content response has a 5xx status code
func (o *PutMmdsNoContent) IsServerError() bool {
	return false
}

// IsCode returns true when this put mmds no content response a status code equal to that given
func (o *PutMmdsNoContent) IsCode(code int) bool {
	return code == 204
}

// Code gets the status code for the put mmds no content response
func (o *PutMmdsNoContent) Code() int {
	return 204
}

func (o *PutMmdsNoContent) Error() string {
	return fmt.Sprintf("[PUT /mmds][%d] putMmdsNoContent ", 204)
}

func (o *PutMmdsNoContent) String() string {
	return fmt.Sprintf("[PUT /mmds][%d] putMmdsNoContent ", 204)
}

func (o *PutMmdsNoContent) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewPutMmdsBadRequest creates a PutMmdsBadRequest with default headers values
func NewPutMmdsBadRequest() *PutMmdsBadRequest {
	return &PutMmdsBadRequest{}
}

/*
PutMmdsBadRequest describes a response with status code 400, with default header values.

MMDS data store cannot be created due to bad input.
*/
type PutMmdsBadRequest struct {
	Payload *models.Error
}

// IsSuccess returns true when this put mmds bad request response has a 2xx status code
func (o *PutMmdsBadRequest) IsSuccess() bool {
	return false
}

// IsRedirect returns true when this put mmds bad request response has a 3xx status code
func (o *PutMmdsBadRequest) IsRedirect() bool {
	return false
}

// IsClientError returns true when this put mmds bad request response has a 4xx status code
func (o *PutMmdsBadRequest) IsClientError() bool {
	return true
}

// IsServerError returns true when this put mmds bad request response has a 5xx status code
func (o *PutMmdsBadRequest) IsServerError() bool {
	return false
}

// IsCode returns true when this put mmds bad request response a status code equal to that given
func (o *PutMmdsBadRequest) IsCode(code int) bool {
	return code == 400
}

// Code gets the status code for the put mmds bad request response
func (o *PutMmdsBadRequest) Code() int {
	return 400
}

func (o *PutMmdsBadRequest) Error() string {
	return fmt.Sprintf("[PUT /mmds][%d] putMmdsBadRequest  %+v", 400, o.Payload)
}

func (o *PutMmdsBadRequest) String() string {
	return fmt.Sprintf("[PUT /mmds][%d] putMmdsBadRequest  %+v", 400, o.Payload)
}

func (o *PutMmdsBadRequest) GetPayload() *models.Error {
	return o.Payload
}

func (o *PutMmdsBadRequest) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.Error)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewPutMmdsDefault creates a PutMmdsDefault with default headers values
func NewPutMmdsDefault(code int) *PutMmdsDefault {
	return &PutMmdsDefault{
		_statusCode: code,
	}
}

/*
PutMmdsDefault describes a response with status code -1, with default header values.

Internal server error
*/
type PutMmdsDefault struct {
	_statusCode int

	Payload *models.Error
}

// IsSuccess returns true when this put mmds default response has a 2xx status code
func (o *PutMmdsDefault) IsSuccess() bool {
	return o._statusCode/100 == 2
}

// IsRedirect returns true when this put mmds default response has a 3xx status code
func (o *PutMmdsDefault) IsRedirect() bool {
	return o._statusCode/100 == 3
}

// IsClientError returns true when this put mmds default response has a 4xx status code
func (o *PutMmdsDefault) IsClientError() bool {
	return o._statusCode/100 == 4
}

// IsServerError returns true when this put mmds default response has a 5xx status code
func (o *PutMmdsDefault) IsServerError() bool {
	return o._statusCode/100 == 5
}

// IsCode returns true when this put mmds default response a status code equal to that given
func (o *PutMmdsDefault) IsCode(code int) bool {
	return o._statusCode == code
}

// Code gets the status code for the put mmds default response
func (o *PutMmdsDefault) Code() int {
	return o._statusCode
}

func (o *PutMmdsDefault) Error() string {
	return fmt.Sprintf("[PUT /mmds][%d] putMmds default  %+v", o._statusCode, o.Payload)
}

func (o *PutMmdsDefault) String() string {
	return fmt.Sprintf("[PUT /mmds][%d] putMmds default  %+v", o._statusCode, o.Payload)
}

func (o *PutMmdsDefault) GetPayload() *models.Error {
	return o.Payload
}

func (o *PutMmdsDefault) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.Error)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}
