// Code generated by go-swagger; DO NOT EDIT.

package operations

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"

	"github.com/e2b-dev/infra/packages/shared/pkg/fc/models"
)

// PutCPUConfigurationReader is a Reader for the PutCPUConfiguration structure.
type PutCPUConfigurationReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *PutCPUConfigurationReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 204:
		result := NewPutCPUConfigurationNoContent()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 400:
		result := NewPutCPUConfigurationBadRequest()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		result := NewPutCPUConfigurationDefault(response.Code())
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		if response.Code()/100 == 2 {
			return result, nil
		}
		return nil, result
	}
}

// NewPutCPUConfigurationNoContent creates a PutCPUConfigurationNoContent with default headers values
func NewPutCPUConfigurationNoContent() *PutCPUConfigurationNoContent {
	return &PutCPUConfigurationNoContent{}
}

/*
PutCPUConfigurationNoContent describes a response with status code 204, with default header values.

CPU configuration set successfully
*/
type PutCPUConfigurationNoContent struct {
}

// IsSuccess returns true when this put Cpu configuration no content response has a 2xx status code
func (o *PutCPUConfigurationNoContent) IsSuccess() bool {
	return true
}

// IsRedirect returns true when this put Cpu configuration no content response has a 3xx status code
func (o *PutCPUConfigurationNoContent) IsRedirect() bool {
	return false
}

// IsClientError returns true when this put Cpu configuration no content response has a 4xx status code
func (o *PutCPUConfigurationNoContent) IsClientError() bool {
	return false
}

// IsServerError returns true when this put Cpu configuration no content response has a 5xx status code
func (o *PutCPUConfigurationNoContent) IsServerError() bool {
	return false
}

// IsCode returns true when this put Cpu configuration no content response a status code equal to that given
func (o *PutCPUConfigurationNoContent) IsCode(code int) bool {
	return code == 204
}

// Code gets the status code for the put Cpu configuration no content response
func (o *PutCPUConfigurationNoContent) Code() int {
	return 204
}

func (o *PutCPUConfigurationNoContent) Error() string {
	return fmt.Sprintf("[PUT /cpu-config][%d] putCpuConfigurationNoContent ", 204)
}

func (o *PutCPUConfigurationNoContent) String() string {
	return fmt.Sprintf("[PUT /cpu-config][%d] putCpuConfigurationNoContent ", 204)
}

func (o *PutCPUConfigurationNoContent) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewPutCPUConfigurationBadRequest creates a PutCPUConfigurationBadRequest with default headers values
func NewPutCPUConfigurationBadRequest() *PutCPUConfigurationBadRequest {
	return &PutCPUConfigurationBadRequest{}
}

/*
PutCPUConfigurationBadRequest describes a response with status code 400, with default header values.

CPU configuration cannot be updated due to invalid input format
*/
type PutCPUConfigurationBadRequest struct {
	Payload *models.Error
}

// IsSuccess returns true when this put Cpu configuration bad request response has a 2xx status code
func (o *PutCPUConfigurationBadRequest) IsSuccess() bool {
	return false
}

// IsRedirect returns true when this put Cpu configuration bad request response has a 3xx status code
func (o *PutCPUConfigurationBadRequest) IsRedirect() bool {
	return false
}

// IsClientError returns true when this put Cpu configuration bad request response has a 4xx status code
func (o *PutCPUConfigurationBadRequest) IsClientError() bool {
	return true
}

// IsServerError returns true when this put Cpu configuration bad request response has a 5xx status code
func (o *PutCPUConfigurationBadRequest) IsServerError() bool {
	return false
}

// IsCode returns true when this put Cpu configuration bad request response a status code equal to that given
func (o *PutCPUConfigurationBadRequest) IsCode(code int) bool {
	return code == 400
}

// Code gets the status code for the put Cpu configuration bad request response
func (o *PutCPUConfigurationBadRequest) Code() int {
	return 400
}

func (o *PutCPUConfigurationBadRequest) Error() string {
	return fmt.Sprintf("[PUT /cpu-config][%d] putCpuConfigurationBadRequest  %+v", 400, o.Payload)
}

func (o *PutCPUConfigurationBadRequest) String() string {
	return fmt.Sprintf("[PUT /cpu-config][%d] putCpuConfigurationBadRequest  %+v", 400, o.Payload)
}

func (o *PutCPUConfigurationBadRequest) GetPayload() *models.Error {
	return o.Payload
}

func (o *PutCPUConfigurationBadRequest) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.Error)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewPutCPUConfigurationDefault creates a PutCPUConfigurationDefault with default headers values
func NewPutCPUConfigurationDefault(code int) *PutCPUConfigurationDefault {
	return &PutCPUConfigurationDefault{
		_statusCode: code,
	}
}

/*
PutCPUConfigurationDefault describes a response with status code -1, with default header values.

Internal server error
*/
type PutCPUConfigurationDefault struct {
	_statusCode int

	Payload *models.Error
}

// IsSuccess returns true when this put Cpu configuration default response has a 2xx status code
func (o *PutCPUConfigurationDefault) IsSuccess() bool {
	return o._statusCode/100 == 2
}

// IsRedirect returns true when this put Cpu configuration default response has a 3xx status code
func (o *PutCPUConfigurationDefault) IsRedirect() bool {
	return o._statusCode/100 == 3
}

// IsClientError returns true when this put Cpu configuration default response has a 4xx status code
func (o *PutCPUConfigurationDefault) IsClientError() bool {
	return o._statusCode/100 == 4
}

// IsServerError returns true when this put Cpu configuration default response has a 5xx status code
func (o *PutCPUConfigurationDefault) IsServerError() bool {
	return o._statusCode/100 == 5
}

// IsCode returns true when this put Cpu configuration default response a status code equal to that given
func (o *PutCPUConfigurationDefault) IsCode(code int) bool {
	return o._statusCode == code
}

// Code gets the status code for the put Cpu configuration default response
func (o *PutCPUConfigurationDefault) Code() int {
	return o._statusCode
}

func (o *PutCPUConfigurationDefault) Error() string {
	return fmt.Sprintf("[PUT /cpu-config][%d] putCpuConfiguration default  %+v", o._statusCode, o.Payload)
}

func (o *PutCPUConfigurationDefault) String() string {
	return fmt.Sprintf("[PUT /cpu-config][%d] putCpuConfiguration default  %+v", o._statusCode, o.Payload)
}

func (o *PutCPUConfigurationDefault) GetPayload() *models.Error {
	return o.Payload
}

func (o *PutCPUConfigurationDefault) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.Error)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}
