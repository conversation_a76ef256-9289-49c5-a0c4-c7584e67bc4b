// Code generated by go-swagger; DO NOT EDIT.

package operations

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"

	"github.com/e2b-dev/infra/packages/shared/pkg/fc/models"
)

// PutLoggerReader is a Reader for the PutLogger structure.
type PutLoggerReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *PutLoggerReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 204:
		result := NewPutLoggerNoContent()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 400:
		result := NewPutLoggerBadRequest()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		result := NewPutLoggerDefault(response.Code())
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		if response.Code()/100 == 2 {
			return result, nil
		}
		return nil, result
	}
}

// NewPutLoggerNoContent creates a PutLoggerNoContent with default headers values
func NewPutLoggerNoContent() *PutLoggerNoContent {
	return &PutLoggerNoContent{}
}

/*
PutLoggerNoContent describes a response with status code 204, with default header values.

Logger created.
*/
type PutLoggerNoContent struct {
}

// IsSuccess returns true when this put logger no content response has a 2xx status code
func (o *PutLoggerNoContent) IsSuccess() bool {
	return true
}

// IsRedirect returns true when this put logger no content response has a 3xx status code
func (o *PutLoggerNoContent) IsRedirect() bool {
	return false
}

// IsClientError returns true when this put logger no content response has a 4xx status code
func (o *PutLoggerNoContent) IsClientError() bool {
	return false
}

// IsServerError returns true when this put logger no content response has a 5xx status code
func (o *PutLoggerNoContent) IsServerError() bool {
	return false
}

// IsCode returns true when this put logger no content response a status code equal to that given
func (o *PutLoggerNoContent) IsCode(code int) bool {
	return code == 204
}

// Code gets the status code for the put logger no content response
func (o *PutLoggerNoContent) Code() int {
	return 204
}

func (o *PutLoggerNoContent) Error() string {
	return fmt.Sprintf("[PUT /logger][%d] putLoggerNoContent ", 204)
}

func (o *PutLoggerNoContent) String() string {
	return fmt.Sprintf("[PUT /logger][%d] putLoggerNoContent ", 204)
}

func (o *PutLoggerNoContent) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewPutLoggerBadRequest creates a PutLoggerBadRequest with default headers values
func NewPutLoggerBadRequest() *PutLoggerBadRequest {
	return &PutLoggerBadRequest{}
}

/*
PutLoggerBadRequest describes a response with status code 400, with default header values.

Logger cannot be initialized due to bad input.
*/
type PutLoggerBadRequest struct {
	Payload *models.Error
}

// IsSuccess returns true when this put logger bad request response has a 2xx status code
func (o *PutLoggerBadRequest) IsSuccess() bool {
	return false
}

// IsRedirect returns true when this put logger bad request response has a 3xx status code
func (o *PutLoggerBadRequest) IsRedirect() bool {
	return false
}

// IsClientError returns true when this put logger bad request response has a 4xx status code
func (o *PutLoggerBadRequest) IsClientError() bool {
	return true
}

// IsServerError returns true when this put logger bad request response has a 5xx status code
func (o *PutLoggerBadRequest) IsServerError() bool {
	return false
}

// IsCode returns true when this put logger bad request response a status code equal to that given
func (o *PutLoggerBadRequest) IsCode(code int) bool {
	return code == 400
}

// Code gets the status code for the put logger bad request response
func (o *PutLoggerBadRequest) Code() int {
	return 400
}

func (o *PutLoggerBadRequest) Error() string {
	return fmt.Sprintf("[PUT /logger][%d] putLoggerBadRequest  %+v", 400, o.Payload)
}

func (o *PutLoggerBadRequest) String() string {
	return fmt.Sprintf("[PUT /logger][%d] putLoggerBadRequest  %+v", 400, o.Payload)
}

func (o *PutLoggerBadRequest) GetPayload() *models.Error {
	return o.Payload
}

func (o *PutLoggerBadRequest) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.Error)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewPutLoggerDefault creates a PutLoggerDefault with default headers values
func NewPutLoggerDefault(code int) *PutLoggerDefault {
	return &PutLoggerDefault{
		_statusCode: code,
	}
}

/*
PutLoggerDefault describes a response with status code -1, with default header values.

Internal server error.
*/
type PutLoggerDefault struct {
	_statusCode int

	Payload *models.Error
}

// IsSuccess returns true when this put logger default response has a 2xx status code
func (o *PutLoggerDefault) IsSuccess() bool {
	return o._statusCode/100 == 2
}

// IsRedirect returns true when this put logger default response has a 3xx status code
func (o *PutLoggerDefault) IsRedirect() bool {
	return o._statusCode/100 == 3
}

// IsClientError returns true when this put logger default response has a 4xx status code
func (o *PutLoggerDefault) IsClientError() bool {
	return o._statusCode/100 == 4
}

// IsServerError returns true when this put logger default response has a 5xx status code
func (o *PutLoggerDefault) IsServerError() bool {
	return o._statusCode/100 == 5
}

// IsCode returns true when this put logger default response a status code equal to that given
func (o *PutLoggerDefault) IsCode(code int) bool {
	return o._statusCode == code
}

// Code gets the status code for the put logger default response
func (o *PutLoggerDefault) Code() int {
	return o._statusCode
}

func (o *PutLoggerDefault) Error() string {
	return fmt.Sprintf("[PUT /logger][%d] putLogger default  %+v", o._statusCode, o.Payload)
}

func (o *PutLoggerDefault) String() string {
	return fmt.Sprintf("[PUT /logger][%d] putLogger default  %+v", o._statusCode, o.Payload)
}

func (o *PutLoggerDefault) GetPayload() *models.Error {
	return o.Payload
}

func (o *PutLoggerDefault) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.Error)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}
