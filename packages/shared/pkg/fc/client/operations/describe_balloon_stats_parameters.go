// Code generated by go-swagger; DO NOT EDIT.

package operations

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
)

// NewDescribeBalloonStatsParams creates a new DescribeBalloonStatsParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewDescribeBalloonStatsParams() *DescribeBalloonStatsParams {
	return &DescribeBalloonStatsParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewDescribeBalloonStatsParamsWithTimeout creates a new DescribeBalloonStatsParams object
// with the ability to set a timeout on a request.
func NewDescribeBalloonStatsParamsWithTimeout(timeout time.Duration) *DescribeBalloonStatsParams {
	return &DescribeBalloonStatsParams{
		timeout: timeout,
	}
}

// NewDescribeBalloonStatsParamsWithContext creates a new DescribeBalloonStatsParams object
// with the ability to set a context for a request.
func NewDescribeBalloonStatsParamsWithContext(ctx context.Context) *DescribeBalloonStatsParams {
	return &DescribeBalloonStatsParams{
		Context: ctx,
	}
}

// NewDescribeBalloonStatsParamsWithHTTPClient creates a new DescribeBalloonStatsParams object
// with the ability to set a custom HTTPClient for a request.
func NewDescribeBalloonStatsParamsWithHTTPClient(client *http.Client) *DescribeBalloonStatsParams {
	return &DescribeBalloonStatsParams{
		HTTPClient: client,
	}
}

/*
DescribeBalloonStatsParams contains all the parameters to send to the API endpoint

	for the describe balloon stats operation.

	Typically these are written to a http.Request.
*/
type DescribeBalloonStatsParams struct {
	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the describe balloon stats params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *DescribeBalloonStatsParams) WithDefaults() *DescribeBalloonStatsParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the describe balloon stats params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *DescribeBalloonStatsParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the describe balloon stats params
func (o *DescribeBalloonStatsParams) WithTimeout(timeout time.Duration) *DescribeBalloonStatsParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the describe balloon stats params
func (o *DescribeBalloonStatsParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the describe balloon stats params
func (o *DescribeBalloonStatsParams) WithContext(ctx context.Context) *DescribeBalloonStatsParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the describe balloon stats params
func (o *DescribeBalloonStatsParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the describe balloon stats params
func (o *DescribeBalloonStatsParams) WithHTTPClient(client *http.Client) *DescribeBalloonStatsParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the describe balloon stats params
func (o *DescribeBalloonStatsParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WriteToRequest writes these params to a swagger request
func (o *DescribeBalloonStatsParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
