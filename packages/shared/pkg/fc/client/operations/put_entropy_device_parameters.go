// Code generated by go-swagger; DO NOT EDIT.

package operations

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"

	"github.com/e2b-dev/infra/packages/shared/pkg/fc/models"
)

// NewPutEntropyDeviceParams creates a new PutEntropyDeviceParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewPutEntropyDeviceParams() *PutEntropyDeviceParams {
	return &PutEntropyDeviceParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewPutEntropyDeviceParamsWithTimeout creates a new PutEntropyDeviceParams object
// with the ability to set a timeout on a request.
func NewPutEntropyDeviceParamsWithTimeout(timeout time.Duration) *PutEntropyDeviceParams {
	return &PutEntropyDeviceParams{
		timeout: timeout,
	}
}

// NewPutEntropyDeviceParamsWithContext creates a new PutEntropyDeviceParams object
// with the ability to set a context for a request.
func NewPutEntropyDeviceParamsWithContext(ctx context.Context) *PutEntropyDeviceParams {
	return &PutEntropyDeviceParams{
		Context: ctx,
	}
}

// NewPutEntropyDeviceParamsWithHTTPClient creates a new PutEntropyDeviceParams object
// with the ability to set a custom HTTPClient for a request.
func NewPutEntropyDeviceParamsWithHTTPClient(client *http.Client) *PutEntropyDeviceParams {
	return &PutEntropyDeviceParams{
		HTTPClient: client,
	}
}

/*
PutEntropyDeviceParams contains all the parameters to send to the API endpoint

	for the put entropy device operation.

	Typically these are written to a http.Request.
*/
type PutEntropyDeviceParams struct {

	/* Body.

	   Guest entropy device properties
	*/
	Body *models.EntropyDevice

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the put entropy device params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *PutEntropyDeviceParams) WithDefaults() *PutEntropyDeviceParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the put entropy device params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *PutEntropyDeviceParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the put entropy device params
func (o *PutEntropyDeviceParams) WithTimeout(timeout time.Duration) *PutEntropyDeviceParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the put entropy device params
func (o *PutEntropyDeviceParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the put entropy device params
func (o *PutEntropyDeviceParams) WithContext(ctx context.Context) *PutEntropyDeviceParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the put entropy device params
func (o *PutEntropyDeviceParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the put entropy device params
func (o *PutEntropyDeviceParams) WithHTTPClient(client *http.Client) *PutEntropyDeviceParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the put entropy device params
func (o *PutEntropyDeviceParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithBody adds the body to the put entropy device params
func (o *PutEntropyDeviceParams) WithBody(body *models.EntropyDevice) *PutEntropyDeviceParams {
	o.SetBody(body)
	return o
}

// SetBody adds the body to the put entropy device params
func (o *PutEntropyDeviceParams) SetBody(body *models.EntropyDevice) {
	o.Body = body
}

// WriteToRequest writes these params to a swagger request
func (o *PutEntropyDeviceParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error
	if o.Body != nil {
		if err := r.SetBodyParam(o.Body); err != nil {
			return err
		}
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
