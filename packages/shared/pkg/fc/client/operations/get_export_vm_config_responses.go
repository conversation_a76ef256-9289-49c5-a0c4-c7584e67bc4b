// Code generated by go-swagger; DO NOT EDIT.

package operations

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"

	"github.com/e2b-dev/infra/packages/shared/pkg/fc/models"
)

// GetExportVMConfigReader is a Reader for the GetExportVMConfig structure.
type GetExportVMConfigReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *GetExportVMConfigReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 200:
		result := NewGetExportVMConfigOK()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	default:
		result := NewGetExportVMConfigDefault(response.Code())
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		if response.Code()/100 == 2 {
			return result, nil
		}
		return nil, result
	}
}

// NewGetExportVMConfigOK creates a GetExportVMConfigOK with default headers values
func NewGetExportVMConfigOK() *GetExportVMConfigOK {
	return &GetExportVMConfigOK{}
}

/*
GetExportVMConfigOK describes a response with status code 200, with default header values.

OK
*/
type GetExportVMConfigOK struct {
	Payload *models.FullVMConfiguration
}

// IsSuccess returns true when this get export Vm config o k response has a 2xx status code
func (o *GetExportVMConfigOK) IsSuccess() bool {
	return true
}

// IsRedirect returns true when this get export Vm config o k response has a 3xx status code
func (o *GetExportVMConfigOK) IsRedirect() bool {
	return false
}

// IsClientError returns true when this get export Vm config o k response has a 4xx status code
func (o *GetExportVMConfigOK) IsClientError() bool {
	return false
}

// IsServerError returns true when this get export Vm config o k response has a 5xx status code
func (o *GetExportVMConfigOK) IsServerError() bool {
	return false
}

// IsCode returns true when this get export Vm config o k response a status code equal to that given
func (o *GetExportVMConfigOK) IsCode(code int) bool {
	return code == 200
}

// Code gets the status code for the get export Vm config o k response
func (o *GetExportVMConfigOK) Code() int {
	return 200
}

func (o *GetExportVMConfigOK) Error() string {
	return fmt.Sprintf("[GET /vm/config][%d] getExportVmConfigOK  %+v", 200, o.Payload)
}

func (o *GetExportVMConfigOK) String() string {
	return fmt.Sprintf("[GET /vm/config][%d] getExportVmConfigOK  %+v", 200, o.Payload)
}

func (o *GetExportVMConfigOK) GetPayload() *models.FullVMConfiguration {
	return o.Payload
}

func (o *GetExportVMConfigOK) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.FullVMConfiguration)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewGetExportVMConfigDefault creates a GetExportVMConfigDefault with default headers values
func NewGetExportVMConfigDefault(code int) *GetExportVMConfigDefault {
	return &GetExportVMConfigDefault{
		_statusCode: code,
	}
}

/*
GetExportVMConfigDefault describes a response with status code -1, with default header values.

Internal server error
*/
type GetExportVMConfigDefault struct {
	_statusCode int

	Payload *models.Error
}

// IsSuccess returns true when this get export Vm config default response has a 2xx status code
func (o *GetExportVMConfigDefault) IsSuccess() bool {
	return o._statusCode/100 == 2
}

// IsRedirect returns true when this get export Vm config default response has a 3xx status code
func (o *GetExportVMConfigDefault) IsRedirect() bool {
	return o._statusCode/100 == 3
}

// IsClientError returns true when this get export Vm config default response has a 4xx status code
func (o *GetExportVMConfigDefault) IsClientError() bool {
	return o._statusCode/100 == 4
}

// IsServerError returns true when this get export Vm config default response has a 5xx status code
func (o *GetExportVMConfigDefault) IsServerError() bool {
	return o._statusCode/100 == 5
}

// IsCode returns true when this get export Vm config default response a status code equal to that given
func (o *GetExportVMConfigDefault) IsCode(code int) bool {
	return o._statusCode == code
}

// Code gets the status code for the get export Vm config default response
func (o *GetExportVMConfigDefault) Code() int {
	return o._statusCode
}

func (o *GetExportVMConfigDefault) Error() string {
	return fmt.Sprintf("[GET /vm/config][%d] getExportVmConfig default  %+v", o._statusCode, o.Payload)
}

func (o *GetExportVMConfigDefault) String() string {
	return fmt.Sprintf("[GET /vm/config][%d] getExportVmConfig default  %+v", o._statusCode, o.Payload)
}

func (o *GetExportVMConfigDefault) GetPayload() *models.Error {
	return o.Payload
}

func (o *GetExportVMConfigDefault) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.Error)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}
