// Code generated by go-swagger; DO NOT EDIT.

package operations

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"

	"github.com/e2b-dev/infra/packages/shared/pkg/fc/models"
)

// PutEntropyDeviceReader is a Reader for the PutEntropyDevice structure.
type PutEntropyDeviceReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *PutEntropyDeviceReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 204:
		result := NewPutEntropyDeviceNoContent()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	default:
		result := NewPutEntropyDeviceDefault(response.Code())
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		if response.Code()/100 == 2 {
			return result, nil
		}
		return nil, result
	}
}

// NewPutEntropyDeviceNoContent creates a PutEntropyDeviceNoContent with default headers values
func NewPutEntropyDeviceNoContent() *PutEntropyDeviceNoContent {
	return &PutEntropyDeviceNoContent{}
}

/*
PutEntropyDeviceNoContent describes a response with status code 204, with default header values.

Entropy device created
*/
type PutEntropyDeviceNoContent struct {
}

// IsSuccess returns true when this put entropy device no content response has a 2xx status code
func (o *PutEntropyDeviceNoContent) IsSuccess() bool {
	return true
}

// IsRedirect returns true when this put entropy device no content response has a 3xx status code
func (o *PutEntropyDeviceNoContent) IsRedirect() bool {
	return false
}

// IsClientError returns true when this put entropy device no content response has a 4xx status code
func (o *PutEntropyDeviceNoContent) IsClientError() bool {
	return false
}

// IsServerError returns true when this put entropy device no content response has a 5xx status code
func (o *PutEntropyDeviceNoContent) IsServerError() bool {
	return false
}

// IsCode returns true when this put entropy device no content response a status code equal to that given
func (o *PutEntropyDeviceNoContent) IsCode(code int) bool {
	return code == 204
}

// Code gets the status code for the put entropy device no content response
func (o *PutEntropyDeviceNoContent) Code() int {
	return 204
}

func (o *PutEntropyDeviceNoContent) Error() string {
	return fmt.Sprintf("[PUT /entropy][%d] putEntropyDeviceNoContent ", 204)
}

func (o *PutEntropyDeviceNoContent) String() string {
	return fmt.Sprintf("[PUT /entropy][%d] putEntropyDeviceNoContent ", 204)
}

func (o *PutEntropyDeviceNoContent) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewPutEntropyDeviceDefault creates a PutEntropyDeviceDefault with default headers values
func NewPutEntropyDeviceDefault(code int) *PutEntropyDeviceDefault {
	return &PutEntropyDeviceDefault{
		_statusCode: code,
	}
}

/*
PutEntropyDeviceDefault describes a response with status code -1, with default header values.

Internal server error
*/
type PutEntropyDeviceDefault struct {
	_statusCode int

	Payload *models.Error
}

// IsSuccess returns true when this put entropy device default response has a 2xx status code
func (o *PutEntropyDeviceDefault) IsSuccess() bool {
	return o._statusCode/100 == 2
}

// IsRedirect returns true when this put entropy device default response has a 3xx status code
func (o *PutEntropyDeviceDefault) IsRedirect() bool {
	return o._statusCode/100 == 3
}

// IsClientError returns true when this put entropy device default response has a 4xx status code
func (o *PutEntropyDeviceDefault) IsClientError() bool {
	return o._statusCode/100 == 4
}

// IsServerError returns true when this put entropy device default response has a 5xx status code
func (o *PutEntropyDeviceDefault) IsServerError() bool {
	return o._statusCode/100 == 5
}

// IsCode returns true when this put entropy device default response a status code equal to that given
func (o *PutEntropyDeviceDefault) IsCode(code int) bool {
	return o._statusCode == code
}

// Code gets the status code for the put entropy device default response
func (o *PutEntropyDeviceDefault) Code() int {
	return o._statusCode
}

func (o *PutEntropyDeviceDefault) Error() string {
	return fmt.Sprintf("[PUT /entropy][%d] putEntropyDevice default  %+v", o._statusCode, o.Payload)
}

func (o *PutEntropyDeviceDefault) String() string {
	return fmt.Sprintf("[PUT /entropy][%d] putEntropyDevice default  %+v", o._statusCode, o.Payload)
}

func (o *PutEntropyDeviceDefault) GetPayload() *models.Error {
	return o.Payload
}

func (o *PutEntropyDeviceDefault) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.Error)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}
