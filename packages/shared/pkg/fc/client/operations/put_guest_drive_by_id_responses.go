// Code generated by go-swagger; DO NOT EDIT.

package operations

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"

	"github.com/e2b-dev/infra/packages/shared/pkg/fc/models"
)

// PutGuestDriveByIDReader is a Reader for the PutGuestDriveByID structure.
type PutGuestDriveByIDReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *PutGuestDriveByIDReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 204:
		result := NewPutGuestDriveByIDNoContent()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 400:
		result := NewPutGuestDriveByIDBadRequest()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		result := NewPutGuestDriveByIDDefault(response.Code())
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		if response.Code()/100 == 2 {
			return result, nil
		}
		return nil, result
	}
}

// NewPutGuestDriveByIDNoContent creates a PutGuestDriveByIDNoContent with default headers values
func NewPutGuestDriveByIDNoContent() *PutGuestDriveByIDNoContent {
	return &PutGuestDriveByIDNoContent{}
}

/*
PutGuestDriveByIDNoContent describes a response with status code 204, with default header values.

Drive created/updated
*/
type PutGuestDriveByIDNoContent struct {
}

// IsSuccess returns true when this put guest drive by Id no content response has a 2xx status code
func (o *PutGuestDriveByIDNoContent) IsSuccess() bool {
	return true
}

// IsRedirect returns true when this put guest drive by Id no content response has a 3xx status code
func (o *PutGuestDriveByIDNoContent) IsRedirect() bool {
	return false
}

// IsClientError returns true when this put guest drive by Id no content response has a 4xx status code
func (o *PutGuestDriveByIDNoContent) IsClientError() bool {
	return false
}

// IsServerError returns true when this put guest drive by Id no content response has a 5xx status code
func (o *PutGuestDriveByIDNoContent) IsServerError() bool {
	return false
}

// IsCode returns true when this put guest drive by Id no content response a status code equal to that given
func (o *PutGuestDriveByIDNoContent) IsCode(code int) bool {
	return code == 204
}

// Code gets the status code for the put guest drive by Id no content response
func (o *PutGuestDriveByIDNoContent) Code() int {
	return 204
}

func (o *PutGuestDriveByIDNoContent) Error() string {
	return fmt.Sprintf("[PUT /drives/{drive_id}][%d] putGuestDriveByIdNoContent ", 204)
}

func (o *PutGuestDriveByIDNoContent) String() string {
	return fmt.Sprintf("[PUT /drives/{drive_id}][%d] putGuestDriveByIdNoContent ", 204)
}

func (o *PutGuestDriveByIDNoContent) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	return nil
}

// NewPutGuestDriveByIDBadRequest creates a PutGuestDriveByIDBadRequest with default headers values
func NewPutGuestDriveByIDBadRequest() *PutGuestDriveByIDBadRequest {
	return &PutGuestDriveByIDBadRequest{}
}

/*
PutGuestDriveByIDBadRequest describes a response with status code 400, with default header values.

Drive cannot be created/updated due to bad input
*/
type PutGuestDriveByIDBadRequest struct {
	Payload *models.Error
}

// IsSuccess returns true when this put guest drive by Id bad request response has a 2xx status code
func (o *PutGuestDriveByIDBadRequest) IsSuccess() bool {
	return false
}

// IsRedirect returns true when this put guest drive by Id bad request response has a 3xx status code
func (o *PutGuestDriveByIDBadRequest) IsRedirect() bool {
	return false
}

// IsClientError returns true when this put guest drive by Id bad request response has a 4xx status code
func (o *PutGuestDriveByIDBadRequest) IsClientError() bool {
	return true
}

// IsServerError returns true when this put guest drive by Id bad request response has a 5xx status code
func (o *PutGuestDriveByIDBadRequest) IsServerError() bool {
	return false
}

// IsCode returns true when this put guest drive by Id bad request response a status code equal to that given
func (o *PutGuestDriveByIDBadRequest) IsCode(code int) bool {
	return code == 400
}

// Code gets the status code for the put guest drive by Id bad request response
func (o *PutGuestDriveByIDBadRequest) Code() int {
	return 400
}

func (o *PutGuestDriveByIDBadRequest) Error() string {
	return fmt.Sprintf("[PUT /drives/{drive_id}][%d] putGuestDriveByIdBadRequest  %+v", 400, o.Payload)
}

func (o *PutGuestDriveByIDBadRequest) String() string {
	return fmt.Sprintf("[PUT /drives/{drive_id}][%d] putGuestDriveByIdBadRequest  %+v", 400, o.Payload)
}

func (o *PutGuestDriveByIDBadRequest) GetPayload() *models.Error {
	return o.Payload
}

func (o *PutGuestDriveByIDBadRequest) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.Error)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewPutGuestDriveByIDDefault creates a PutGuestDriveByIDDefault with default headers values
func NewPutGuestDriveByIDDefault(code int) *PutGuestDriveByIDDefault {
	return &PutGuestDriveByIDDefault{
		_statusCode: code,
	}
}

/*
PutGuestDriveByIDDefault describes a response with status code -1, with default header values.

Internal server error.
*/
type PutGuestDriveByIDDefault struct {
	_statusCode int

	Payload *models.Error
}

// IsSuccess returns true when this put guest drive by ID default response has a 2xx status code
func (o *PutGuestDriveByIDDefault) IsSuccess() bool {
	return o._statusCode/100 == 2
}

// IsRedirect returns true when this put guest drive by ID default response has a 3xx status code
func (o *PutGuestDriveByIDDefault) IsRedirect() bool {
	return o._statusCode/100 == 3
}

// IsClientError returns true when this put guest drive by ID default response has a 4xx status code
func (o *PutGuestDriveByIDDefault) IsClientError() bool {
	return o._statusCode/100 == 4
}

// IsServerError returns true when this put guest drive by ID default response has a 5xx status code
func (o *PutGuestDriveByIDDefault) IsServerError() bool {
	return o._statusCode/100 == 5
}

// IsCode returns true when this put guest drive by ID default response a status code equal to that given
func (o *PutGuestDriveByIDDefault) IsCode(code int) bool {
	return o._statusCode == code
}

// Code gets the status code for the put guest drive by ID default response
func (o *PutGuestDriveByIDDefault) Code() int {
	return o._statusCode
}

func (o *PutGuestDriveByIDDefault) Error() string {
	return fmt.Sprintf("[PUT /drives/{drive_id}][%d] putGuestDriveByID default  %+v", o._statusCode, o.Payload)
}

func (o *PutGuestDriveByIDDefault) String() string {
	return fmt.Sprintf("[PUT /drives/{drive_id}][%d] putGuestDriveByID default  %+v", o._statusCode, o.Payload)
}

func (o *PutGuestDriveByIDDefault) GetPayload() *models.Error {
	return o.Payload
}

func (o *PutGuestDriveByIDDefault) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.Error)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}
