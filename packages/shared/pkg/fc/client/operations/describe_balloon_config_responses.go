// Code generated by go-swagger; DO NOT EDIT.

package operations

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"

	"github.com/e2b-dev/infra/packages/shared/pkg/fc/models"
)

// DescribeBalloonConfigReader is a Reader for the DescribeBalloonConfig structure.
type DescribeBalloonConfigReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *DescribeBalloonConfigReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 200:
		result := NewDescribeBalloonConfigOK()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 400:
		result := NewDescribeBalloonConfigBadRequest()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		result := NewDescribeBalloonConfigDefault(response.Code())
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		if response.Code()/100 == 2 {
			return result, nil
		}
		return nil, result
	}
}

// NewDescribeBalloonConfigOK creates a DescribeBalloonConfigOK with default headers values
func NewDescribeBalloonConfigOK() *DescribeBalloonConfigOK {
	return &DescribeBalloonConfigOK{}
}

/*
DescribeBalloonConfigOK describes a response with status code 200, with default header values.

The balloon device configuration
*/
type DescribeBalloonConfigOK struct {
	Payload *models.Balloon
}

// IsSuccess returns true when this describe balloon config o k response has a 2xx status code
func (o *DescribeBalloonConfigOK) IsSuccess() bool {
	return true
}

// IsRedirect returns true when this describe balloon config o k response has a 3xx status code
func (o *DescribeBalloonConfigOK) IsRedirect() bool {
	return false
}

// IsClientError returns true when this describe balloon config o k response has a 4xx status code
func (o *DescribeBalloonConfigOK) IsClientError() bool {
	return false
}

// IsServerError returns true when this describe balloon config o k response has a 5xx status code
func (o *DescribeBalloonConfigOK) IsServerError() bool {
	return false
}

// IsCode returns true when this describe balloon config o k response a status code equal to that given
func (o *DescribeBalloonConfigOK) IsCode(code int) bool {
	return code == 200
}

// Code gets the status code for the describe balloon config o k response
func (o *DescribeBalloonConfigOK) Code() int {
	return 200
}

func (o *DescribeBalloonConfigOK) Error() string {
	return fmt.Sprintf("[GET /balloon][%d] describeBalloonConfigOK  %+v", 200, o.Payload)
}

func (o *DescribeBalloonConfigOK) String() string {
	return fmt.Sprintf("[GET /balloon][%d] describeBalloonConfigOK  %+v", 200, o.Payload)
}

func (o *DescribeBalloonConfigOK) GetPayload() *models.Balloon {
	return o.Payload
}

func (o *DescribeBalloonConfigOK) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.Balloon)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewDescribeBalloonConfigBadRequest creates a DescribeBalloonConfigBadRequest with default headers values
func NewDescribeBalloonConfigBadRequest() *DescribeBalloonConfigBadRequest {
	return &DescribeBalloonConfigBadRequest{}
}

/*
DescribeBalloonConfigBadRequest describes a response with status code 400, with default header values.

Balloon device not configured.
*/
type DescribeBalloonConfigBadRequest struct {
	Payload *models.Error
}

// IsSuccess returns true when this describe balloon config bad request response has a 2xx status code
func (o *DescribeBalloonConfigBadRequest) IsSuccess() bool {
	return false
}

// IsRedirect returns true when this describe balloon config bad request response has a 3xx status code
func (o *DescribeBalloonConfigBadRequest) IsRedirect() bool {
	return false
}

// IsClientError returns true when this describe balloon config bad request response has a 4xx status code
func (o *DescribeBalloonConfigBadRequest) IsClientError() bool {
	return true
}

// IsServerError returns true when this describe balloon config bad request response has a 5xx status code
func (o *DescribeBalloonConfigBadRequest) IsServerError() bool {
	return false
}

// IsCode returns true when this describe balloon config bad request response a status code equal to that given
func (o *DescribeBalloonConfigBadRequest) IsCode(code int) bool {
	return code == 400
}

// Code gets the status code for the describe balloon config bad request response
func (o *DescribeBalloonConfigBadRequest) Code() int {
	return 400
}

func (o *DescribeBalloonConfigBadRequest) Error() string {
	return fmt.Sprintf("[GET /balloon][%d] describeBalloonConfigBadRequest  %+v", 400, o.Payload)
}

func (o *DescribeBalloonConfigBadRequest) String() string {
	return fmt.Sprintf("[GET /balloon][%d] describeBalloonConfigBadRequest  %+v", 400, o.Payload)
}

func (o *DescribeBalloonConfigBadRequest) GetPayload() *models.Error {
	return o.Payload
}

func (o *DescribeBalloonConfigBadRequest) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.Error)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewDescribeBalloonConfigDefault creates a DescribeBalloonConfigDefault with default headers values
func NewDescribeBalloonConfigDefault(code int) *DescribeBalloonConfigDefault {
	return &DescribeBalloonConfigDefault{
		_statusCode: code,
	}
}

/*
DescribeBalloonConfigDefault describes a response with status code -1, with default header values.

Internal Server Error
*/
type DescribeBalloonConfigDefault struct {
	_statusCode int

	Payload *models.Error
}

// IsSuccess returns true when this describe balloon config default response has a 2xx status code
func (o *DescribeBalloonConfigDefault) IsSuccess() bool {
	return o._statusCode/100 == 2
}

// IsRedirect returns true when this describe balloon config default response has a 3xx status code
func (o *DescribeBalloonConfigDefault) IsRedirect() bool {
	return o._statusCode/100 == 3
}

// IsClientError returns true when this describe balloon config default response has a 4xx status code
func (o *DescribeBalloonConfigDefault) IsClientError() bool {
	return o._statusCode/100 == 4
}

// IsServerError returns true when this describe balloon config default response has a 5xx status code
func (o *DescribeBalloonConfigDefault) IsServerError() bool {
	return o._statusCode/100 == 5
}

// IsCode returns true when this describe balloon config default response a status code equal to that given
func (o *DescribeBalloonConfigDefault) IsCode(code int) bool {
	return o._statusCode == code
}

// Code gets the status code for the describe balloon config default response
func (o *DescribeBalloonConfigDefault) Code() int {
	return o._statusCode
}

func (o *DescribeBalloonConfigDefault) Error() string {
	return fmt.Sprintf("[GET /balloon][%d] describeBalloonConfig default  %+v", o._statusCode, o.Payload)
}

func (o *DescribeBalloonConfigDefault) String() string {
	return fmt.Sprintf("[GET /balloon][%d] describeBalloonConfig default  %+v", o._statusCode, o.Payload)
}

func (o *DescribeBalloonConfigDefault) GetPayload() *models.Error {
	return o.Payload
}

func (o *DescribeBalloonConfigDefault) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.Error)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}
