// Code generated by go-swagger; DO NOT EDIT.

package operations

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
)

// NewDescribeInstanceParams creates a new DescribeInstanceParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewDescribeInstanceParams() *DescribeInstanceParams {
	return &DescribeInstanceParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewDescribeInstanceParamsWithTimeout creates a new DescribeInstanceParams object
// with the ability to set a timeout on a request.
func NewDescribeInstanceParamsWithTimeout(timeout time.Duration) *DescribeInstanceParams {
	return &DescribeInstanceParams{
		timeout: timeout,
	}
}

// NewDescribeInstanceParamsWithContext creates a new DescribeInstanceParams object
// with the ability to set a context for a request.
func NewDescribeInstanceParamsWithContext(ctx context.Context) *DescribeInstanceParams {
	return &DescribeInstanceParams{
		Context: ctx,
	}
}

// NewDescribeInstanceParamsWithHTTPClient creates a new DescribeInstanceParams object
// with the ability to set a custom HTTPClient for a request.
func NewDescribeInstanceParamsWithHTTPClient(client *http.Client) *DescribeInstanceParams {
	return &DescribeInstanceParams{
		HTTPClient: client,
	}
}

/*
DescribeInstanceParams contains all the parameters to send to the API endpoint

	for the describe instance operation.

	Typically these are written to a http.Request.
*/
type DescribeInstanceParams struct {
	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the describe instance params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *DescribeInstanceParams) WithDefaults() *DescribeInstanceParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the describe instance params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *DescribeInstanceParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the describe instance params
func (o *DescribeInstanceParams) WithTimeout(timeout time.Duration) *DescribeInstanceParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the describe instance params
func (o *DescribeInstanceParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the describe instance params
func (o *DescribeInstanceParams) WithContext(ctx context.Context) *DescribeInstanceParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the describe instance params
func (o *DescribeInstanceParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the describe instance params
func (o *DescribeInstanceParams) WithHTTPClient(client *http.Client) *DescribeInstanceParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the describe instance params
func (o *DescribeInstanceParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WriteToRequest writes these params to a swagger request
func (o *DescribeInstanceParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
