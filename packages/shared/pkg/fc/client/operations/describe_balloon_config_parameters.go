// Code generated by go-swagger; DO NOT EDIT.

package operations

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"
)

// NewDescribeBalloonConfigParams creates a new DescribeBalloonConfigParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewDescribeBalloonConfigParams() *DescribeBalloonConfigParams {
	return &DescribeBalloonConfigParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewDescribeBalloonConfigParamsWithTimeout creates a new DescribeBalloonConfigParams object
// with the ability to set a timeout on a request.
func NewDescribeBalloonConfigParamsWithTimeout(timeout time.Duration) *DescribeBalloonConfigParams {
	return &DescribeBalloonConfigParams{
		timeout: timeout,
	}
}

// NewDescribeBalloonConfigParamsWithContext creates a new DescribeBalloonConfigParams object
// with the ability to set a context for a request.
func NewDescribeBalloonConfigParamsWithContext(ctx context.Context) *DescribeBalloonConfigParams {
	return &DescribeBalloonConfigParams{
		Context: ctx,
	}
}

// NewDescribeBalloonConfigParamsWithHTTPClient creates a new DescribeBalloonConfigParams object
// with the ability to set a custom HTTPClient for a request.
func NewDescribeBalloonConfigParamsWithHTTPClient(client *http.Client) *DescribeBalloonConfigParams {
	return &DescribeBalloonConfigParams{
		HTTPClient: client,
	}
}

/*
DescribeBalloonConfigParams contains all the parameters to send to the API endpoint

	for the describe balloon config operation.

	Typically these are written to a http.Request.
*/
type DescribeBalloonConfigParams struct {
	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the describe balloon config params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *DescribeBalloonConfigParams) WithDefaults() *DescribeBalloonConfigParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the describe balloon config params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *DescribeBalloonConfigParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the describe balloon config params
func (o *DescribeBalloonConfigParams) WithTimeout(timeout time.Duration) *DescribeBalloonConfigParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the describe balloon config params
func (o *DescribeBalloonConfigParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the describe balloon config params
func (o *DescribeBalloonConfigParams) WithContext(ctx context.Context) *DescribeBalloonConfigParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the describe balloon config params
func (o *DescribeBalloonConfigParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the describe balloon config params
func (o *DescribeBalloonConfigParams) WithHTTPClient(client *http.Client) *DescribeBalloonConfigParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the describe balloon config params
func (o *DescribeBalloonConfigParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WriteToRequest writes these params to a swagger request
func (o *DescribeBalloonConfigParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
