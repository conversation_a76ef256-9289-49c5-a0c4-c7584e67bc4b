// Code generated by go-swagger; DO NOT EDIT.

package operations

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"

	"github.com/e2b-dev/infra/packages/shared/pkg/fc/models"
)

// NewPutMmdsConfigParams creates a new PutMmdsConfigParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewPutMmdsConfigParams() *PutMmdsConfigParams {
	return &PutMmdsConfigParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewPutMmdsConfigParamsWithTimeout creates a new PutMmdsConfigParams object
// with the ability to set a timeout on a request.
func NewPutMmdsConfigParamsWithTimeout(timeout time.Duration) *PutMmdsConfigParams {
	return &PutMmdsConfigParams{
		timeout: timeout,
	}
}

// NewPutMmdsConfigParamsWithContext creates a new PutMmdsConfigParams object
// with the ability to set a context for a request.
func NewPutMmdsConfigParamsWithContext(ctx context.Context) *PutMmdsConfigParams {
	return &PutMmdsConfigParams{
		Context: ctx,
	}
}

// NewPutMmdsConfigParamsWithHTTPClient creates a new PutMmdsConfigParams object
// with the ability to set a custom HTTPClient for a request.
func NewPutMmdsConfigParamsWithHTTPClient(client *http.Client) *PutMmdsConfigParams {
	return &PutMmdsConfigParams{
		HTTPClient: client,
	}
}

/*
PutMmdsConfigParams contains all the parameters to send to the API endpoint

	for the put mmds config operation.

	Typically these are written to a http.Request.
*/
type PutMmdsConfigParams struct {

	/* Body.

	   The MMDS configuration as JSON.
	*/
	Body *models.MmdsConfig

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the put mmds config params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *PutMmdsConfigParams) WithDefaults() *PutMmdsConfigParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the put mmds config params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *PutMmdsConfigParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the put mmds config params
func (o *PutMmdsConfigParams) WithTimeout(timeout time.Duration) *PutMmdsConfigParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the put mmds config params
func (o *PutMmdsConfigParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the put mmds config params
func (o *PutMmdsConfigParams) WithContext(ctx context.Context) *PutMmdsConfigParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the put mmds config params
func (o *PutMmdsConfigParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the put mmds config params
func (o *PutMmdsConfigParams) WithHTTPClient(client *http.Client) *PutMmdsConfigParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the put mmds config params
func (o *PutMmdsConfigParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithBody adds the body to the put mmds config params
func (o *PutMmdsConfigParams) WithBody(body *models.MmdsConfig) *PutMmdsConfigParams {
	o.SetBody(body)
	return o
}

// SetBody adds the body to the put mmds config params
func (o *PutMmdsConfigParams) SetBody(body *models.MmdsConfig) {
	o.Body = body
}

// WriteToRequest writes these params to a swagger request
func (o *PutMmdsConfigParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error
	if o.Body != nil {
		if err := r.SetBodyParam(o.Body); err != nil {
			return err
		}
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
