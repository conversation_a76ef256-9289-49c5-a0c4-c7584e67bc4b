// Code generated by go-swagger; DO NOT EDIT.

package operations

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"

	"github.com/e2b-dev/infra/packages/shared/pkg/fc/models"
)

// GetMmdsReader is a Reader for the GetMmds structure.
type GetMmdsReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *GetMmdsReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 200:
		result := NewGetMmdsOK()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	case 404:
		result := NewGetMmdsNotFound()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return nil, result
	default:
		result := NewGetMmdsDefault(response.Code())
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		if response.Code()/100 == 2 {
			return result, nil
		}
		return nil, result
	}
}

// NewGetMmdsOK creates a GetMmdsOK with default headers values
func NewGetMmdsOK() *GetMmdsOK {
	return &GetMmdsOK{}
}

/*
GetMmdsOK describes a response with status code 200, with default header values.

The MMDS data store JSON.
*/
type GetMmdsOK struct {
	Payload interface{}
}

// IsSuccess returns true when this get mmds o k response has a 2xx status code
func (o *GetMmdsOK) IsSuccess() bool {
	return true
}

// IsRedirect returns true when this get mmds o k response has a 3xx status code
func (o *GetMmdsOK) IsRedirect() bool {
	return false
}

// IsClientError returns true when this get mmds o k response has a 4xx status code
func (o *GetMmdsOK) IsClientError() bool {
	return false
}

// IsServerError returns true when this get mmds o k response has a 5xx status code
func (o *GetMmdsOK) IsServerError() bool {
	return false
}

// IsCode returns true when this get mmds o k response a status code equal to that given
func (o *GetMmdsOK) IsCode(code int) bool {
	return code == 200
}

// Code gets the status code for the get mmds o k response
func (o *GetMmdsOK) Code() int {
	return 200
}

func (o *GetMmdsOK) Error() string {
	return fmt.Sprintf("[GET /mmds][%d] getMmdsOK  %+v", 200, o.Payload)
}

func (o *GetMmdsOK) String() string {
	return fmt.Sprintf("[GET /mmds][%d] getMmdsOK  %+v", 200, o.Payload)
}

func (o *GetMmdsOK) GetPayload() interface{} {
	return o.Payload
}

func (o *GetMmdsOK) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// response payload
	if err := consumer.Consume(response.Body(), &o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewGetMmdsNotFound creates a GetMmdsNotFound with default headers values
func NewGetMmdsNotFound() *GetMmdsNotFound {
	return &GetMmdsNotFound{}
}

/*
GetMmdsNotFound describes a response with status code 404, with default header values.

The MMDS data store content can not be found.
*/
type GetMmdsNotFound struct {
	Payload *models.Error
}

// IsSuccess returns true when this get mmds not found response has a 2xx status code
func (o *GetMmdsNotFound) IsSuccess() bool {
	return false
}

// IsRedirect returns true when this get mmds not found response has a 3xx status code
func (o *GetMmdsNotFound) IsRedirect() bool {
	return false
}

// IsClientError returns true when this get mmds not found response has a 4xx status code
func (o *GetMmdsNotFound) IsClientError() bool {
	return true
}

// IsServerError returns true when this get mmds not found response has a 5xx status code
func (o *GetMmdsNotFound) IsServerError() bool {
	return false
}

// IsCode returns true when this get mmds not found response a status code equal to that given
func (o *GetMmdsNotFound) IsCode(code int) bool {
	return code == 404
}

// Code gets the status code for the get mmds not found response
func (o *GetMmdsNotFound) Code() int {
	return 404
}

func (o *GetMmdsNotFound) Error() string {
	return fmt.Sprintf("[GET /mmds][%d] getMmdsNotFound  %+v", 404, o.Payload)
}

func (o *GetMmdsNotFound) String() string {
	return fmt.Sprintf("[GET /mmds][%d] getMmdsNotFound  %+v", 404, o.Payload)
}

func (o *GetMmdsNotFound) GetPayload() *models.Error {
	return o.Payload
}

func (o *GetMmdsNotFound) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.Error)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}

// NewGetMmdsDefault creates a GetMmdsDefault with default headers values
func NewGetMmdsDefault(code int) *GetMmdsDefault {
	return &GetMmdsDefault{
		_statusCode: code,
	}
}

/*
GetMmdsDefault describes a response with status code -1, with default header values.

Internal server error
*/
type GetMmdsDefault struct {
	_statusCode int

	Payload *models.Error
}

// IsSuccess returns true when this get mmds default response has a 2xx status code
func (o *GetMmdsDefault) IsSuccess() bool {
	return o._statusCode/100 == 2
}

// IsRedirect returns true when this get mmds default response has a 3xx status code
func (o *GetMmdsDefault) IsRedirect() bool {
	return o._statusCode/100 == 3
}

// IsClientError returns true when this get mmds default response has a 4xx status code
func (o *GetMmdsDefault) IsClientError() bool {
	return o._statusCode/100 == 4
}

// IsServerError returns true when this get mmds default response has a 5xx status code
func (o *GetMmdsDefault) IsServerError() bool {
	return o._statusCode/100 == 5
}

// IsCode returns true when this get mmds default response a status code equal to that given
func (o *GetMmdsDefault) IsCode(code int) bool {
	return o._statusCode == code
}

// Code gets the status code for the get mmds default response
func (o *GetMmdsDefault) Code() int {
	return o._statusCode
}

func (o *GetMmdsDefault) Error() string {
	return fmt.Sprintf("[GET /mmds][%d] getMmds default  %+v", o._statusCode, o.Payload)
}

func (o *GetMmdsDefault) String() string {
	return fmt.Sprintf("[GET /mmds][%d] getMmds default  %+v", o._statusCode, o.Payload)
}

func (o *GetMmdsDefault) GetPayload() *models.Error {
	return o.Payload
}

func (o *GetMmdsDefault) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	o.Payload = new(models.Error)

	// response payload
	if err := consumer.Consume(response.Body(), o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}
