// Package api provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package api

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"

	"github.com/oapi-codegen/runtime"
)

// RequestEditorFn  is the function signature for the RequestEditor callback function
type RequestEditorFn func(ctx context.Context, req *http.Request) error

// Do<PERSON> performs HTTP requests.
//
// The standard http.Client implements this interface.
type HttpRequestDoer interface {
	Do(req *http.Request) (*http.Response, error)
}

// Client which conforms to the OpenAPI3 specification for this service.
type Client struct {
	// The endpoint of the server conforming to this interface, with scheme,
	// https://api.deepmap.com for example. This can contain a path relative
	// to the server, such as https://api.deepmap.com/dev-test, and all the
	// paths in the swagger spec will be appended to the server.
	Server string

	// Doer for performing requests, typically a *http.Client with any
	// customized settings, such as certificate chains.
	Client HttpRequestDoer

	// A list of callbacks for modifying requests which are generated before sending over
	// the network.
	RequestEditors []RequestEditorFn
}

// ClientOption allows setting custom parameters during construction
type ClientOption func(*Client) error

// Creates a new Client, with reasonable defaults
func NewClient(server string, opts ...ClientOption) (*Client, error) {
	// create a client with sane default values
	client := Client{
		Server: server,
	}
	// mutate client and add all optional params
	for _, o := range opts {
		if err := o(&client); err != nil {
			return nil, err
		}
	}
	// ensure the server URL always has a trailing slash
	if !strings.HasSuffix(client.Server, "/") {
		client.Server += "/"
	}
	// create httpClient, if not already present
	if client.Client == nil {
		client.Client = &http.Client{}
	}
	return &client, nil
}

// WithHTTPClient allows overriding the default Doer, which is
// automatically created using http.Client. This is useful for tests.
func WithHTTPClient(doer HttpRequestDoer) ClientOption {
	return func(c *Client) error {
		c.Client = doer
		return nil
	}
}

// WithRequestEditorFn allows setting up a callback function, which will be
// called right before sending the request. This can be used to mutate the request.
func WithRequestEditorFn(fn RequestEditorFn) ClientOption {
	return func(c *Client) error {
		c.RequestEditors = append(c.RequestEditors, fn)
		return nil
	}
}

// The interface specification for the client above.
type ClientInterface interface {
	// HealthCheck request
	HealthCheck(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// HealthCheckTraffic request
	HealthCheckTraffic(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1Info request
	V1Info(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1ListSandboxes request
	V1ListSandboxes(ctx context.Context, params *V1ListSandboxesParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1CreateSandboxWithBody request with any body
	V1CreateSandboxWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1CreateSandbox(ctx context.Context, body V1CreateSandboxJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1DeleteSandbox request
	V1DeleteSandbox(ctx context.Context, sandboxId string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1UpdateSandboxWithBody request with any body
	V1UpdateSandboxWithBody(ctx context.Context, sandboxId string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1UpdateSandbox(ctx context.Context, sandboxId string, body V1UpdateSandboxJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1PauseSandboxWithBody request with any body
	V1PauseSandboxWithBody(ctx context.Context, sandboxId string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1PauseSandbox(ctx context.Context, sandboxId string, body V1PauseSandboxJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1ServiceDiscoveryNodes request
	V1ServiceDiscoveryNodes(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1ServiceDiscoveryGetOrchestrators request
	V1ServiceDiscoveryGetOrchestrators(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1ServiceDiscoveryNodeDrain request
	V1ServiceDiscoveryNodeDrain(ctx context.Context, nodeId string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1ServiceDiscoveryNodeKill request
	V1ServiceDiscoveryNodeKill(ctx context.Context, nodeId string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1TemplateBuildCreateWithBody request with any body
	V1TemplateBuildCreateWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1TemplateBuildCreate(ctx context.Context, body V1TemplateBuildCreateJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1TemplateBuildStatus request
	V1TemplateBuildStatus(ctx context.Context, buildId string, params *V1TemplateBuildStatusParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1TemplateBuildDelete request
	V1TemplateBuildDelete(ctx context.Context, buildId string, params *V1TemplateBuildDeleteParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1TemplateBuildLogs request
	V1TemplateBuildLogs(ctx context.Context, buildId string, params *V1TemplateBuildLogsParams, reqEditors ...RequestEditorFn) (*http.Response, error)
}

func (c *Client) HealthCheck(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewHealthCheckRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) HealthCheckTraffic(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewHealthCheckTrafficRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1Info(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1InfoRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1ListSandboxes(ctx context.Context, params *V1ListSandboxesParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1ListSandboxesRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1CreateSandboxWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1CreateSandboxRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1CreateSandbox(ctx context.Context, body V1CreateSandboxJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1CreateSandboxRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1DeleteSandbox(ctx context.Context, sandboxId string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1DeleteSandboxRequest(c.Server, sandboxId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1UpdateSandboxWithBody(ctx context.Context, sandboxId string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1UpdateSandboxRequestWithBody(c.Server, sandboxId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1UpdateSandbox(ctx context.Context, sandboxId string, body V1UpdateSandboxJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1UpdateSandboxRequest(c.Server, sandboxId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1PauseSandboxWithBody(ctx context.Context, sandboxId string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1PauseSandboxRequestWithBody(c.Server, sandboxId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1PauseSandbox(ctx context.Context, sandboxId string, body V1PauseSandboxJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1PauseSandboxRequest(c.Server, sandboxId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1ServiceDiscoveryNodes(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1ServiceDiscoveryNodesRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1ServiceDiscoveryGetOrchestrators(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1ServiceDiscoveryGetOrchestratorsRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1ServiceDiscoveryNodeDrain(ctx context.Context, nodeId string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1ServiceDiscoveryNodeDrainRequest(c.Server, nodeId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1ServiceDiscoveryNodeKill(ctx context.Context, nodeId string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1ServiceDiscoveryNodeKillRequest(c.Server, nodeId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1TemplateBuildCreateWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1TemplateBuildCreateRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1TemplateBuildCreate(ctx context.Context, body V1TemplateBuildCreateJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1TemplateBuildCreateRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1TemplateBuildStatus(ctx context.Context, buildId string, params *V1TemplateBuildStatusParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1TemplateBuildStatusRequest(c.Server, buildId, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1TemplateBuildDelete(ctx context.Context, buildId string, params *V1TemplateBuildDeleteParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1TemplateBuildDeleteRequest(c.Server, buildId, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1TemplateBuildLogs(ctx context.Context, buildId string, params *V1TemplateBuildLogsParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1TemplateBuildLogsRequest(c.Server, buildId, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

// NewHealthCheckRequest generates requests for HealthCheck
func NewHealthCheckRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/health")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewHealthCheckTrafficRequest generates requests for HealthCheckTraffic
func NewHealthCheckTrafficRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/health/traffic")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1InfoRequest generates requests for V1Info
func NewV1InfoRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/info")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1ListSandboxesRequest generates requests for V1ListSandboxes
func NewV1ListSandboxesRequest(server string, params *V1ListSandboxesParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/sandboxes")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "orchestratorId", runtime.ParamLocationQuery, params.OrchestratorId); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1CreateSandboxRequest calls the generic V1CreateSandbox builder with application/json body
func NewV1CreateSandboxRequest(server string, body V1CreateSandboxJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1CreateSandboxRequestWithBody(server, "application/json", bodyReader)
}

// NewV1CreateSandboxRequestWithBody generates requests for V1CreateSandbox with any type of body
func NewV1CreateSandboxRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/sandboxes")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1DeleteSandboxRequest generates requests for V1DeleteSandbox
func NewV1DeleteSandboxRequest(server string, sandboxId string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "sandbox_id", runtime.ParamLocationPath, sandboxId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/sandboxes/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1UpdateSandboxRequest calls the generic V1UpdateSandbox builder with application/json body
func NewV1UpdateSandboxRequest(server string, sandboxId string, body V1UpdateSandboxJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1UpdateSandboxRequestWithBody(server, sandboxId, "application/json", bodyReader)
}

// NewV1UpdateSandboxRequestWithBody generates requests for V1UpdateSandbox with any type of body
func NewV1UpdateSandboxRequestWithBody(server string, sandboxId string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "sandbox_id", runtime.ParamLocationPath, sandboxId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/sandboxes/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PATCH", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1PauseSandboxRequest calls the generic V1PauseSandbox builder with application/json body
func NewV1PauseSandboxRequest(server string, sandboxId string, body V1PauseSandboxJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1PauseSandboxRequestWithBody(server, sandboxId, "application/json", bodyReader)
}

// NewV1PauseSandboxRequestWithBody generates requests for V1PauseSandbox with any type of body
func NewV1PauseSandboxRequestWithBody(server string, sandboxId string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "sandbox_id", runtime.ParamLocationPath, sandboxId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/sandboxes/%s/pause", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1ServiceDiscoveryNodesRequest generates requests for V1ServiceDiscoveryNodes
func NewV1ServiceDiscoveryNodesRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/service-discovery/nodes")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1ServiceDiscoveryGetOrchestratorsRequest generates requests for V1ServiceDiscoveryGetOrchestrators
func NewV1ServiceDiscoveryGetOrchestratorsRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/service-discovery/nodes/orchestrators")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1ServiceDiscoveryNodeDrainRequest generates requests for V1ServiceDiscoveryNodeDrain
func NewV1ServiceDiscoveryNodeDrainRequest(server string, nodeId string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "node_id", runtime.ParamLocationPath, nodeId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/service-discovery/nodes/%s/drain", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1ServiceDiscoveryNodeKillRequest generates requests for V1ServiceDiscoveryNodeKill
func NewV1ServiceDiscoveryNodeKillRequest(server string, nodeId string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "node_id", runtime.ParamLocationPath, nodeId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/service-discovery/nodes/%s/kill", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1TemplateBuildCreateRequest calls the generic V1TemplateBuildCreate builder with application/json body
func NewV1TemplateBuildCreateRequest(server string, body V1TemplateBuildCreateJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1TemplateBuildCreateRequestWithBody(server, "application/json", bodyReader)
}

// NewV1TemplateBuildCreateRequestWithBody generates requests for V1TemplateBuildCreate with any type of body
func NewV1TemplateBuildCreateRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/templates/builds")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1TemplateBuildStatusRequest generates requests for V1TemplateBuildStatus
func NewV1TemplateBuildStatusRequest(server string, buildId string, params *V1TemplateBuildStatusParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "build_id", runtime.ParamLocationPath, buildId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/templates/builds/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "orchestrator_id", runtime.ParamLocationQuery, params.OrchestratorId); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "template_id", runtime.ParamLocationQuery, params.TemplateId); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1TemplateBuildDeleteRequest generates requests for V1TemplateBuildDelete
func NewV1TemplateBuildDeleteRequest(server string, buildId string, params *V1TemplateBuildDeleteParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "build_id", runtime.ParamLocationPath, buildId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/templates/builds/%s/logs", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "template_id", runtime.ParamLocationQuery, params.TemplateId); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "orchestrator_id", runtime.ParamLocationQuery, params.OrchestratorId); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1TemplateBuildLogsRequest generates requests for V1TemplateBuildLogs
func NewV1TemplateBuildLogsRequest(server string, buildId string, params *V1TemplateBuildLogsParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "build_id", runtime.ParamLocationPath, buildId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/templates/builds/%s/logs", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "orchestrator_id", runtime.ParamLocationQuery, params.OrchestratorId); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "template_id", runtime.ParamLocationQuery, params.TemplateId); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		if params.Offset != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "offset", runtime.ParamLocationQuery, *params.Offset); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

func (c *Client) applyEditors(ctx context.Context, req *http.Request, additionalEditors []RequestEditorFn) error {
	for _, r := range c.RequestEditors {
		if err := r(ctx, req); err != nil {
			return err
		}
	}
	for _, r := range additionalEditors {
		if err := r(ctx, req); err != nil {
			return err
		}
	}
	return nil
}

// ClientWithResponses builds on ClientInterface to offer response payloads
type ClientWithResponses struct {
	ClientInterface
}

// NewClientWithResponses creates a new ClientWithResponses, which wraps
// Client with return type handling
func NewClientWithResponses(server string, opts ...ClientOption) (*ClientWithResponses, error) {
	client, err := NewClient(server, opts...)
	if err != nil {
		return nil, err
	}
	return &ClientWithResponses{client}, nil
}

// WithBaseURL overrides the baseURL.
func WithBaseURL(baseURL string) ClientOption {
	return func(c *Client) error {
		newBaseURL, err := url.Parse(baseURL)
		if err != nil {
			return err
		}
		c.Server = newBaseURL.String()
		return nil
	}
}

// ClientWithResponsesInterface is the interface specification for the client with responses above.
type ClientWithResponsesInterface interface {
	// HealthCheckWithResponse request
	HealthCheckWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*HealthCheckResponse, error)

	// HealthCheckTrafficWithResponse request
	HealthCheckTrafficWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*HealthCheckTrafficResponse, error)

	// V1InfoWithResponse request
	V1InfoWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*V1InfoResponse, error)

	// V1ListSandboxesWithResponse request
	V1ListSandboxesWithResponse(ctx context.Context, params *V1ListSandboxesParams, reqEditors ...RequestEditorFn) (*V1ListSandboxesResponse, error)

	// V1CreateSandboxWithBodyWithResponse request with any body
	V1CreateSandboxWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1CreateSandboxResponse, error)

	V1CreateSandboxWithResponse(ctx context.Context, body V1CreateSandboxJSONRequestBody, reqEditors ...RequestEditorFn) (*V1CreateSandboxResponse, error)

	// V1DeleteSandboxWithResponse request
	V1DeleteSandboxWithResponse(ctx context.Context, sandboxId string, reqEditors ...RequestEditorFn) (*V1DeleteSandboxResponse, error)

	// V1UpdateSandboxWithBodyWithResponse request with any body
	V1UpdateSandboxWithBodyWithResponse(ctx context.Context, sandboxId string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1UpdateSandboxResponse, error)

	V1UpdateSandboxWithResponse(ctx context.Context, sandboxId string, body V1UpdateSandboxJSONRequestBody, reqEditors ...RequestEditorFn) (*V1UpdateSandboxResponse, error)

	// V1PauseSandboxWithBodyWithResponse request with any body
	V1PauseSandboxWithBodyWithResponse(ctx context.Context, sandboxId string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1PauseSandboxResponse, error)

	V1PauseSandboxWithResponse(ctx context.Context, sandboxId string, body V1PauseSandboxJSONRequestBody, reqEditors ...RequestEditorFn) (*V1PauseSandboxResponse, error)

	// V1ServiceDiscoveryNodesWithResponse request
	V1ServiceDiscoveryNodesWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*V1ServiceDiscoveryNodesResponse, error)

	// V1ServiceDiscoveryGetOrchestratorsWithResponse request
	V1ServiceDiscoveryGetOrchestratorsWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*V1ServiceDiscoveryGetOrchestratorsResponse, error)

	// V1ServiceDiscoveryNodeDrainWithResponse request
	V1ServiceDiscoveryNodeDrainWithResponse(ctx context.Context, nodeId string, reqEditors ...RequestEditorFn) (*V1ServiceDiscoveryNodeDrainResponse, error)

	// V1ServiceDiscoveryNodeKillWithResponse request
	V1ServiceDiscoveryNodeKillWithResponse(ctx context.Context, nodeId string, reqEditors ...RequestEditorFn) (*V1ServiceDiscoveryNodeKillResponse, error)

	// V1TemplateBuildCreateWithBodyWithResponse request with any body
	V1TemplateBuildCreateWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1TemplateBuildCreateResponse, error)

	V1TemplateBuildCreateWithResponse(ctx context.Context, body V1TemplateBuildCreateJSONRequestBody, reqEditors ...RequestEditorFn) (*V1TemplateBuildCreateResponse, error)

	// V1TemplateBuildStatusWithResponse request
	V1TemplateBuildStatusWithResponse(ctx context.Context, buildId string, params *V1TemplateBuildStatusParams, reqEditors ...RequestEditorFn) (*V1TemplateBuildStatusResponse, error)

	// V1TemplateBuildDeleteWithResponse request
	V1TemplateBuildDeleteWithResponse(ctx context.Context, buildId string, params *V1TemplateBuildDeleteParams, reqEditors ...RequestEditorFn) (*V1TemplateBuildDeleteResponse, error)

	// V1TemplateBuildLogsWithResponse request
	V1TemplateBuildLogsWithResponse(ctx context.Context, buildId string, params *V1TemplateBuildLogsParams, reqEditors ...RequestEditorFn) (*V1TemplateBuildLogsResponse, error)
}

type HealthCheckResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r HealthCheckResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r HealthCheckResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type HealthCheckTrafficResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r HealthCheckTrafficResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r HealthCheckTrafficResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1InfoResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ClusterNodeInfo
	JSON401      *N401
	JSON500      *N500
}

// Status returns HTTPResponse.Status
func (r V1InfoResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1InfoResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1ListSandboxesResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *SandboxListResponse
	JSON400      *N400
	JSON401      *N401
	JSON500      *N500
}

// Status returns HTTPResponse.Status
func (r V1ListSandboxesResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1ListSandboxesResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1CreateSandboxResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *SandboxCreateResponse
	JSON400      *N400
	JSON401      *N401
	JSON500      *N500
}

// Status returns HTTPResponse.Status
func (r V1CreateSandboxResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1CreateSandboxResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1DeleteSandboxResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON400      *N400
	JSON401      *N401
	JSON500      *N500
}

// Status returns HTTPResponse.Status
func (r V1DeleteSandboxResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1DeleteSandboxResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1UpdateSandboxResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON400      *N400
	JSON401      *N401
	JSON500      *N500
}

// Status returns HTTPResponse.Status
func (r V1UpdateSandboxResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1UpdateSandboxResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1PauseSandboxResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON400      *N400
	JSON401      *N401
	JSON500      *N500
}

// Status returns HTTPResponse.Status
func (r V1PauseSandboxResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1PauseSandboxResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1ServiceDiscoveryNodesResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *[]ClusterNode
	JSON401      *N401
	JSON500      *N500
}

// Status returns HTTPResponse.Status
func (r V1ServiceDiscoveryNodesResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1ServiceDiscoveryNodesResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1ServiceDiscoveryGetOrchestratorsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *[]ClusterOrchestratorNode
	JSON401      *N401
	JSON500      *N500
}

// Status returns HTTPResponse.Status
func (r V1ServiceDiscoveryGetOrchestratorsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1ServiceDiscoveryGetOrchestratorsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1ServiceDiscoveryNodeDrainResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON400      *N400
	JSON401      *N401
	JSON404      *N404
	JSON500      *N500
}

// Status returns HTTPResponse.Status
func (r V1ServiceDiscoveryNodeDrainResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1ServiceDiscoveryNodeDrainResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1ServiceDiscoveryNodeKillResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON400      *N400
	JSON401      *N401
	JSON404      *N404
	JSON500      *N500
}

// Status returns HTTPResponse.Status
func (r V1ServiceDiscoveryNodeKillResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1ServiceDiscoveryNodeKillResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1TemplateBuildCreateResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON400      *N400
	JSON401      *N401
	JSON500      *N500
}

// Status returns HTTPResponse.Status
func (r V1TemplateBuildCreateResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1TemplateBuildCreateResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1TemplateBuildStatusResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *TemplateBuildStatusResponse
	JSON400      *N400
	JSON401      *N401
	JSON500      *N500
}

// Status returns HTTPResponse.Status
func (r V1TemplateBuildStatusResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1TemplateBuildStatusResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1TemplateBuildDeleteResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON400      *N400
	JSON401      *N401
	JSON500      *N500
}

// Status returns HTTPResponse.Status
func (r V1TemplateBuildDeleteResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1TemplateBuildDeleteResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1TemplateBuildLogsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *TemplateBuildLogsResponse
	JSON400      *N400
	JSON401      *N401
	JSON500      *N500
}

// Status returns HTTPResponse.Status
func (r V1TemplateBuildLogsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1TemplateBuildLogsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

// HealthCheckWithResponse request returning *HealthCheckResponse
func (c *ClientWithResponses) HealthCheckWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*HealthCheckResponse, error) {
	rsp, err := c.HealthCheck(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseHealthCheckResponse(rsp)
}

// HealthCheckTrafficWithResponse request returning *HealthCheckTrafficResponse
func (c *ClientWithResponses) HealthCheckTrafficWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*HealthCheckTrafficResponse, error) {
	rsp, err := c.HealthCheckTraffic(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseHealthCheckTrafficResponse(rsp)
}

// V1InfoWithResponse request returning *V1InfoResponse
func (c *ClientWithResponses) V1InfoWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*V1InfoResponse, error) {
	rsp, err := c.V1Info(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1InfoResponse(rsp)
}

// V1ListSandboxesWithResponse request returning *V1ListSandboxesResponse
func (c *ClientWithResponses) V1ListSandboxesWithResponse(ctx context.Context, params *V1ListSandboxesParams, reqEditors ...RequestEditorFn) (*V1ListSandboxesResponse, error) {
	rsp, err := c.V1ListSandboxes(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1ListSandboxesResponse(rsp)
}

// V1CreateSandboxWithBodyWithResponse request with arbitrary body returning *V1CreateSandboxResponse
func (c *ClientWithResponses) V1CreateSandboxWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1CreateSandboxResponse, error) {
	rsp, err := c.V1CreateSandboxWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1CreateSandboxResponse(rsp)
}

func (c *ClientWithResponses) V1CreateSandboxWithResponse(ctx context.Context, body V1CreateSandboxJSONRequestBody, reqEditors ...RequestEditorFn) (*V1CreateSandboxResponse, error) {
	rsp, err := c.V1CreateSandbox(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1CreateSandboxResponse(rsp)
}

// V1DeleteSandboxWithResponse request returning *V1DeleteSandboxResponse
func (c *ClientWithResponses) V1DeleteSandboxWithResponse(ctx context.Context, sandboxId string, reqEditors ...RequestEditorFn) (*V1DeleteSandboxResponse, error) {
	rsp, err := c.V1DeleteSandbox(ctx, sandboxId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1DeleteSandboxResponse(rsp)
}

// V1UpdateSandboxWithBodyWithResponse request with arbitrary body returning *V1UpdateSandboxResponse
func (c *ClientWithResponses) V1UpdateSandboxWithBodyWithResponse(ctx context.Context, sandboxId string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1UpdateSandboxResponse, error) {
	rsp, err := c.V1UpdateSandboxWithBody(ctx, sandboxId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1UpdateSandboxResponse(rsp)
}

func (c *ClientWithResponses) V1UpdateSandboxWithResponse(ctx context.Context, sandboxId string, body V1UpdateSandboxJSONRequestBody, reqEditors ...RequestEditorFn) (*V1UpdateSandboxResponse, error) {
	rsp, err := c.V1UpdateSandbox(ctx, sandboxId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1UpdateSandboxResponse(rsp)
}

// V1PauseSandboxWithBodyWithResponse request with arbitrary body returning *V1PauseSandboxResponse
func (c *ClientWithResponses) V1PauseSandboxWithBodyWithResponse(ctx context.Context, sandboxId string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1PauseSandboxResponse, error) {
	rsp, err := c.V1PauseSandboxWithBody(ctx, sandboxId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1PauseSandboxResponse(rsp)
}

func (c *ClientWithResponses) V1PauseSandboxWithResponse(ctx context.Context, sandboxId string, body V1PauseSandboxJSONRequestBody, reqEditors ...RequestEditorFn) (*V1PauseSandboxResponse, error) {
	rsp, err := c.V1PauseSandbox(ctx, sandboxId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1PauseSandboxResponse(rsp)
}

// V1ServiceDiscoveryNodesWithResponse request returning *V1ServiceDiscoveryNodesResponse
func (c *ClientWithResponses) V1ServiceDiscoveryNodesWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*V1ServiceDiscoveryNodesResponse, error) {
	rsp, err := c.V1ServiceDiscoveryNodes(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1ServiceDiscoveryNodesResponse(rsp)
}

// V1ServiceDiscoveryGetOrchestratorsWithResponse request returning *V1ServiceDiscoveryGetOrchestratorsResponse
func (c *ClientWithResponses) V1ServiceDiscoveryGetOrchestratorsWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*V1ServiceDiscoveryGetOrchestratorsResponse, error) {
	rsp, err := c.V1ServiceDiscoveryGetOrchestrators(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1ServiceDiscoveryGetOrchestratorsResponse(rsp)
}

// V1ServiceDiscoveryNodeDrainWithResponse request returning *V1ServiceDiscoveryNodeDrainResponse
func (c *ClientWithResponses) V1ServiceDiscoveryNodeDrainWithResponse(ctx context.Context, nodeId string, reqEditors ...RequestEditorFn) (*V1ServiceDiscoveryNodeDrainResponse, error) {
	rsp, err := c.V1ServiceDiscoveryNodeDrain(ctx, nodeId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1ServiceDiscoveryNodeDrainResponse(rsp)
}

// V1ServiceDiscoveryNodeKillWithResponse request returning *V1ServiceDiscoveryNodeKillResponse
func (c *ClientWithResponses) V1ServiceDiscoveryNodeKillWithResponse(ctx context.Context, nodeId string, reqEditors ...RequestEditorFn) (*V1ServiceDiscoveryNodeKillResponse, error) {
	rsp, err := c.V1ServiceDiscoveryNodeKill(ctx, nodeId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1ServiceDiscoveryNodeKillResponse(rsp)
}

// V1TemplateBuildCreateWithBodyWithResponse request with arbitrary body returning *V1TemplateBuildCreateResponse
func (c *ClientWithResponses) V1TemplateBuildCreateWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1TemplateBuildCreateResponse, error) {
	rsp, err := c.V1TemplateBuildCreateWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1TemplateBuildCreateResponse(rsp)
}

func (c *ClientWithResponses) V1TemplateBuildCreateWithResponse(ctx context.Context, body V1TemplateBuildCreateJSONRequestBody, reqEditors ...RequestEditorFn) (*V1TemplateBuildCreateResponse, error) {
	rsp, err := c.V1TemplateBuildCreate(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1TemplateBuildCreateResponse(rsp)
}

// V1TemplateBuildStatusWithResponse request returning *V1TemplateBuildStatusResponse
func (c *ClientWithResponses) V1TemplateBuildStatusWithResponse(ctx context.Context, buildId string, params *V1TemplateBuildStatusParams, reqEditors ...RequestEditorFn) (*V1TemplateBuildStatusResponse, error) {
	rsp, err := c.V1TemplateBuildStatus(ctx, buildId, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1TemplateBuildStatusResponse(rsp)
}

// V1TemplateBuildDeleteWithResponse request returning *V1TemplateBuildDeleteResponse
func (c *ClientWithResponses) V1TemplateBuildDeleteWithResponse(ctx context.Context, buildId string, params *V1TemplateBuildDeleteParams, reqEditors ...RequestEditorFn) (*V1TemplateBuildDeleteResponse, error) {
	rsp, err := c.V1TemplateBuildDelete(ctx, buildId, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1TemplateBuildDeleteResponse(rsp)
}

// V1TemplateBuildLogsWithResponse request returning *V1TemplateBuildLogsResponse
func (c *ClientWithResponses) V1TemplateBuildLogsWithResponse(ctx context.Context, buildId string, params *V1TemplateBuildLogsParams, reqEditors ...RequestEditorFn) (*V1TemplateBuildLogsResponse, error) {
	rsp, err := c.V1TemplateBuildLogs(ctx, buildId, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1TemplateBuildLogsResponse(rsp)
}

// ParseHealthCheckResponse parses an HTTP response from a HealthCheckWithResponse call
func ParseHealthCheckResponse(rsp *http.Response) (*HealthCheckResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &HealthCheckResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseHealthCheckTrafficResponse parses an HTTP response from a HealthCheckTrafficWithResponse call
func ParseHealthCheckTrafficResponse(rsp *http.Response) (*HealthCheckTrafficResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &HealthCheckTrafficResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseV1InfoResponse parses an HTTP response from a V1InfoWithResponse call
func ParseV1InfoResponse(rsp *http.Response) (*V1InfoResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1InfoResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ClusterNodeInfo
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest N401
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest N500
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseV1ListSandboxesResponse parses an HTTP response from a V1ListSandboxesWithResponse call
func ParseV1ListSandboxesResponse(rsp *http.Response) (*V1ListSandboxesResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1ListSandboxesResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest SandboxListResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest N400
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest N401
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest N500
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseV1CreateSandboxResponse parses an HTTP response from a V1CreateSandboxWithResponse call
func ParseV1CreateSandboxResponse(rsp *http.Response) (*V1CreateSandboxResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1CreateSandboxResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest SandboxCreateResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest N400
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest N401
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest N500
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseV1DeleteSandboxResponse parses an HTTP response from a V1DeleteSandboxWithResponse call
func ParseV1DeleteSandboxResponse(rsp *http.Response) (*V1DeleteSandboxResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1DeleteSandboxResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest N400
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest N401
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest N500
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseV1UpdateSandboxResponse parses an HTTP response from a V1UpdateSandboxWithResponse call
func ParseV1UpdateSandboxResponse(rsp *http.Response) (*V1UpdateSandboxResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1UpdateSandboxResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest N400
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest N401
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest N500
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseV1PauseSandboxResponse parses an HTTP response from a V1PauseSandboxWithResponse call
func ParseV1PauseSandboxResponse(rsp *http.Response) (*V1PauseSandboxResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1PauseSandboxResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest N400
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest N401
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest N500
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseV1ServiceDiscoveryNodesResponse parses an HTTP response from a V1ServiceDiscoveryNodesWithResponse call
func ParseV1ServiceDiscoveryNodesResponse(rsp *http.Response) (*V1ServiceDiscoveryNodesResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1ServiceDiscoveryNodesResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest []ClusterNode
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest N401
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest N500
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseV1ServiceDiscoveryGetOrchestratorsResponse parses an HTTP response from a V1ServiceDiscoveryGetOrchestratorsWithResponse call
func ParseV1ServiceDiscoveryGetOrchestratorsResponse(rsp *http.Response) (*V1ServiceDiscoveryGetOrchestratorsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1ServiceDiscoveryGetOrchestratorsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest []ClusterOrchestratorNode
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest N401
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest N500
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseV1ServiceDiscoveryNodeDrainResponse parses an HTTP response from a V1ServiceDiscoveryNodeDrainWithResponse call
func ParseV1ServiceDiscoveryNodeDrainResponse(rsp *http.Response) (*V1ServiceDiscoveryNodeDrainResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1ServiceDiscoveryNodeDrainResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest N400
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest N401
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest N404
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest N500
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseV1ServiceDiscoveryNodeKillResponse parses an HTTP response from a V1ServiceDiscoveryNodeKillWithResponse call
func ParseV1ServiceDiscoveryNodeKillResponse(rsp *http.Response) (*V1ServiceDiscoveryNodeKillResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1ServiceDiscoveryNodeKillResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest N400
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest N401
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest N404
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest N500
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseV1TemplateBuildCreateResponse parses an HTTP response from a V1TemplateBuildCreateWithResponse call
func ParseV1TemplateBuildCreateResponse(rsp *http.Response) (*V1TemplateBuildCreateResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1TemplateBuildCreateResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest N400
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest N401
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest N500
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseV1TemplateBuildStatusResponse parses an HTTP response from a V1TemplateBuildStatusWithResponse call
func ParseV1TemplateBuildStatusResponse(rsp *http.Response) (*V1TemplateBuildStatusResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1TemplateBuildStatusResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest TemplateBuildStatusResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest N400
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest N401
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest N500
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseV1TemplateBuildDeleteResponse parses an HTTP response from a V1TemplateBuildDeleteWithResponse call
func ParseV1TemplateBuildDeleteResponse(rsp *http.Response) (*V1TemplateBuildDeleteResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1TemplateBuildDeleteResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest N400
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest N401
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest N500
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseV1TemplateBuildLogsResponse parses an HTTP response from a V1TemplateBuildLogsWithResponse call
func ParseV1TemplateBuildLogsResponse(rsp *http.Response) (*V1TemplateBuildLogsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1TemplateBuildLogsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest TemplateBuildLogsResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest N400
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest N401
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest N500
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}
