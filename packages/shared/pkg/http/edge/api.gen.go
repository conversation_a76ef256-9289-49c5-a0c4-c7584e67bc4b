// Package api provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package api

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/oapi-codegen/runtime"
)

// ServerInterface represents all server handlers.
type ServerInterface interface {

	// (GET /health)
	HealthCheck(c *gin.Context)

	// (GET /health/traffic)
	HealthCheckTraffic(c *gin.Context)

	// (GET /v1/info)
	V1Info(c *gin.Context)
	// List running sandboxes for an orchestrator
	// (GET /v1/sandboxes)
	V1ListSandboxes(c *gin.Context, params V1ListSandboxesParams)
	// Create a new sandbox
	// (POST /v1/sandboxes)
	V1CreateSandbox(c *gin.Context)
	// Delete a sandbox
	// (DELETE /v1/sandboxes/{sandbox_id})
	V1DeleteSandbox(c *gin.Context, sandboxId string)
	// Update an existing sandbox
	// (PATCH /v1/sandboxes/{sandbox_id})
	V1UpdateSandbox(c *gin.Context, sandboxId string)
	// Pause a running sandbox
	// (POST /v1/sandboxes/{sandbox_id}/pause)
	V1PauseSandbox(c *gin.Context, sandboxId string)

	// (GET /v1/service-discovery/nodes)
	V1ServiceDiscoveryNodes(c *gin.Context)
	// Get the orchestrators
	// (GET /v1/service-discovery/nodes/orchestrators)
	V1ServiceDiscoveryGetOrchestrators(c *gin.Context)

	// (POST /v1/service-discovery/nodes/{node_id}/drain)
	V1ServiceDiscoveryNodeDrain(c *gin.Context, nodeId string)

	// (POST /v1/service-discovery/nodes/{node_id}/kill)
	V1ServiceDiscoveryNodeKill(c *gin.Context, nodeId string)
	// Create a new template build
	// (POST /v1/templates/builds)
	V1TemplateBuildCreate(c *gin.Context)
	// Template build status
	// (GET /v1/templates/builds/{build_id})
	V1TemplateBuildStatus(c *gin.Context, buildId string, params V1TemplateBuildStatusParams)
	// Template build delete
	// (DELETE /v1/templates/builds/{build_id}/logs)
	V1TemplateBuildDelete(c *gin.Context, buildId string, params V1TemplateBuildDeleteParams)
	// Template build logs
	// (GET /v1/templates/builds/{build_id}/logs)
	V1TemplateBuildLogs(c *gin.Context, buildId string, params V1TemplateBuildLogsParams)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandler       func(*gin.Context, error, int)
}

type MiddlewareFunc func(c *gin.Context)

// HealthCheck operation middleware
func (siw *ServerInterfaceWrapper) HealthCheck(c *gin.Context) {

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.HealthCheck(c)
}

// HealthCheckTraffic operation middleware
func (siw *ServerInterfaceWrapper) HealthCheckTraffic(c *gin.Context) {

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.HealthCheckTraffic(c)
}

// V1Info operation middleware
func (siw *ServerInterfaceWrapper) V1Info(c *gin.Context) {

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.V1Info(c)
}

// V1ListSandboxes operation middleware
func (siw *ServerInterfaceWrapper) V1ListSandboxes(c *gin.Context) {

	var err error

	c.Set(ApiKeyAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params V1ListSandboxesParams

	// ------------- Required query parameter "orchestratorId" -------------

	if paramValue := c.Query("orchestratorId"); paramValue != "" {

	} else {
		siw.ErrorHandler(c, fmt.Errorf("Query argument orchestratorId is required, but not found"), http.StatusBadRequest)
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "orchestratorId", c.Request.URL.Query(), &params.OrchestratorId)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter orchestratorId: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.V1ListSandboxes(c, params)
}

// V1CreateSandbox operation middleware
func (siw *ServerInterfaceWrapper) V1CreateSandbox(c *gin.Context) {

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.V1CreateSandbox(c)
}

// V1DeleteSandbox operation middleware
func (siw *ServerInterfaceWrapper) V1DeleteSandbox(c *gin.Context) {

	var err error

	// ------------- Path parameter "sandbox_id" -------------
	var sandboxId string

	err = runtime.BindStyledParameterWithOptions("simple", "sandbox_id", c.Param("sandbox_id"), &sandboxId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter sandbox_id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.V1DeleteSandbox(c, sandboxId)
}

// V1UpdateSandbox operation middleware
func (siw *ServerInterfaceWrapper) V1UpdateSandbox(c *gin.Context) {

	var err error

	// ------------- Path parameter "sandbox_id" -------------
	var sandboxId string

	err = runtime.BindStyledParameterWithOptions("simple", "sandbox_id", c.Param("sandbox_id"), &sandboxId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter sandbox_id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.V1UpdateSandbox(c, sandboxId)
}

// V1PauseSandbox operation middleware
func (siw *ServerInterfaceWrapper) V1PauseSandbox(c *gin.Context) {

	var err error

	// ------------- Path parameter "sandbox_id" -------------
	var sandboxId string

	err = runtime.BindStyledParameterWithOptions("simple", "sandbox_id", c.Param("sandbox_id"), &sandboxId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter sandbox_id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.V1PauseSandbox(c, sandboxId)
}

// V1ServiceDiscoveryNodes operation middleware
func (siw *ServerInterfaceWrapper) V1ServiceDiscoveryNodes(c *gin.Context) {

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.V1ServiceDiscoveryNodes(c)
}

// V1ServiceDiscoveryGetOrchestrators operation middleware
func (siw *ServerInterfaceWrapper) V1ServiceDiscoveryGetOrchestrators(c *gin.Context) {

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.V1ServiceDiscoveryGetOrchestrators(c)
}

// V1ServiceDiscoveryNodeDrain operation middleware
func (siw *ServerInterfaceWrapper) V1ServiceDiscoveryNodeDrain(c *gin.Context) {

	var err error

	// ------------- Path parameter "node_id" -------------
	var nodeId string

	err = runtime.BindStyledParameterWithOptions("simple", "node_id", c.Param("node_id"), &nodeId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter node_id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.V1ServiceDiscoveryNodeDrain(c, nodeId)
}

// V1ServiceDiscoveryNodeKill operation middleware
func (siw *ServerInterfaceWrapper) V1ServiceDiscoveryNodeKill(c *gin.Context) {

	var err error

	// ------------- Path parameter "node_id" -------------
	var nodeId string

	err = runtime.BindStyledParameterWithOptions("simple", "node_id", c.Param("node_id"), &nodeId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter node_id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.V1ServiceDiscoveryNodeKill(c, nodeId)
}

// V1TemplateBuildCreate operation middleware
func (siw *ServerInterfaceWrapper) V1TemplateBuildCreate(c *gin.Context) {

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.V1TemplateBuildCreate(c)
}

// V1TemplateBuildStatus operation middleware
func (siw *ServerInterfaceWrapper) V1TemplateBuildStatus(c *gin.Context) {

	var err error

	// ------------- Path parameter "build_id" -------------
	var buildId string

	err = runtime.BindStyledParameterWithOptions("simple", "build_id", c.Param("build_id"), &buildId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter build_id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(ApiKeyAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params V1TemplateBuildStatusParams

	// ------------- Required query parameter "orchestrator_id" -------------

	if paramValue := c.Query("orchestrator_id"); paramValue != "" {

	} else {
		siw.ErrorHandler(c, fmt.Errorf("Query argument orchestrator_id is required, but not found"), http.StatusBadRequest)
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "orchestrator_id", c.Request.URL.Query(), &params.OrchestratorId)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter orchestrator_id: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Required query parameter "template_id" -------------

	if paramValue := c.Query("template_id"); paramValue != "" {

	} else {
		siw.ErrorHandler(c, fmt.Errorf("Query argument template_id is required, but not found"), http.StatusBadRequest)
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "template_id", c.Request.URL.Query(), &params.TemplateId)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter template_id: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.V1TemplateBuildStatus(c, buildId, params)
}

// V1TemplateBuildDelete operation middleware
func (siw *ServerInterfaceWrapper) V1TemplateBuildDelete(c *gin.Context) {

	var err error

	// ------------- Path parameter "build_id" -------------
	var buildId string

	err = runtime.BindStyledParameterWithOptions("simple", "build_id", c.Param("build_id"), &buildId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter build_id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(ApiKeyAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params V1TemplateBuildDeleteParams

	// ------------- Required query parameter "template_id" -------------

	if paramValue := c.Query("template_id"); paramValue != "" {

	} else {
		siw.ErrorHandler(c, fmt.Errorf("Query argument template_id is required, but not found"), http.StatusBadRequest)
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "template_id", c.Request.URL.Query(), &params.TemplateId)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter template_id: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Required query parameter "orchestrator_id" -------------

	if paramValue := c.Query("orchestrator_id"); paramValue != "" {

	} else {
		siw.ErrorHandler(c, fmt.Errorf("Query argument orchestrator_id is required, but not found"), http.StatusBadRequest)
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "orchestrator_id", c.Request.URL.Query(), &params.OrchestratorId)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter orchestrator_id: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.V1TemplateBuildDelete(c, buildId, params)
}

// V1TemplateBuildLogs operation middleware
func (siw *ServerInterfaceWrapper) V1TemplateBuildLogs(c *gin.Context) {

	var err error

	// ------------- Path parameter "build_id" -------------
	var buildId string

	err = runtime.BindStyledParameterWithOptions("simple", "build_id", c.Param("build_id"), &buildId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter build_id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(ApiKeyAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params V1TemplateBuildLogsParams

	// ------------- Required query parameter "orchestrator_id" -------------

	if paramValue := c.Query("orchestrator_id"); paramValue != "" {

	} else {
		siw.ErrorHandler(c, fmt.Errorf("Query argument orchestrator_id is required, but not found"), http.StatusBadRequest)
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "orchestrator_id", c.Request.URL.Query(), &params.OrchestratorId)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter orchestrator_id: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Required query parameter "template_id" -------------

	if paramValue := c.Query("template_id"); paramValue != "" {

	} else {
		siw.ErrorHandler(c, fmt.Errorf("Query argument template_id is required, but not found"), http.StatusBadRequest)
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "template_id", c.Request.URL.Query(), &params.TemplateId)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter template_id: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", c.Request.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter offset: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.V1TemplateBuildLogs(c, buildId, params)
}

// GinServerOptions provides options for the Gin server.
type GinServerOptions struct {
	BaseURL      string
	Middlewares  []MiddlewareFunc
	ErrorHandler func(*gin.Context, error, int)
}

// RegisterHandlers creates http.Handler with routing matching OpenAPI spec.
func RegisterHandlers(router gin.IRouter, si ServerInterface) {
	RegisterHandlersWithOptions(router, si, GinServerOptions{})
}

// RegisterHandlersWithOptions creates http.Handler with additional options
func RegisterHandlersWithOptions(router gin.IRouter, si ServerInterface, options GinServerOptions) {
	errorHandler := options.ErrorHandler
	if errorHandler == nil {
		errorHandler = func(c *gin.Context, err error, statusCode int) {
			c.JSON(statusCode, gin.H{"msg": err.Error()})
		}
	}

	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandler:       errorHandler,
	}

	router.GET(options.BaseURL+"/health", wrapper.HealthCheck)
	router.GET(options.BaseURL+"/health/traffic", wrapper.HealthCheckTraffic)
	router.GET(options.BaseURL+"/v1/info", wrapper.V1Info)
	router.GET(options.BaseURL+"/v1/sandboxes", wrapper.V1ListSandboxes)
	router.POST(options.BaseURL+"/v1/sandboxes", wrapper.V1CreateSandbox)
	router.DELETE(options.BaseURL+"/v1/sandboxes/:sandbox_id", wrapper.V1DeleteSandbox)
	router.PATCH(options.BaseURL+"/v1/sandboxes/:sandbox_id", wrapper.V1UpdateSandbox)
	router.POST(options.BaseURL+"/v1/sandboxes/:sandbox_id/pause", wrapper.V1PauseSandbox)
	router.GET(options.BaseURL+"/v1/service-discovery/nodes", wrapper.V1ServiceDiscoveryNodes)
	router.GET(options.BaseURL+"/v1/service-discovery/nodes/orchestrators", wrapper.V1ServiceDiscoveryGetOrchestrators)
	router.POST(options.BaseURL+"/v1/service-discovery/nodes/:node_id/drain", wrapper.V1ServiceDiscoveryNodeDrain)
	router.POST(options.BaseURL+"/v1/service-discovery/nodes/:node_id/kill", wrapper.V1ServiceDiscoveryNodeKill)
	router.POST(options.BaseURL+"/v1/templates/builds", wrapper.V1TemplateBuildCreate)
	router.GET(options.BaseURL+"/v1/templates/builds/:build_id", wrapper.V1TemplateBuildStatus)
	router.DELETE(options.BaseURL+"/v1/templates/builds/:build_id/logs", wrapper.V1TemplateBuildDelete)
	router.GET(options.BaseURL+"/v1/templates/builds/:build_id/logs", wrapper.V1TemplateBuildLogs)
}
