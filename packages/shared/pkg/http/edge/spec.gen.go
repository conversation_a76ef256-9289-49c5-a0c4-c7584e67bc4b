// Package api provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package api

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"fmt"
	"net/url"
	"path"
	"strings"

	"github.com/getkin/kin-openapi/openapi3"
)

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+Rb227bOtZ+FUL/fzEDqLXbnZmL3OVQ7AnatEGSBgMUxoARly3uSKRKUkm8A737gAed",
	"KVlJ7EyNfZU0IrlO3/rItcg+BRFPM86AKRkcPgUZFjgFBcL8S2JGbvnjfyjR/yIgI0EzRTkLDoPvjP7M",
	"AVECTNElBYH4EqkYkJsUhAHV4zKs4iAMGE4hOGyuGAYCfuZUAAkOlcghDGQUQ4q1KLXOzGglKFsFRVHo",
	"wTLjTIJR7GA+1z8izhQwpX/FWZbQCGvlZn9IreFTY73/F7AMDoP/m9XWzuxXOfskBBdWRtvCY0yQVhGk",
	"CoowOJh/2L3Mo1zF2qF2VQR2nBZ+sHvhX7lCS54zoiX+4y1cfAXiHkRpZlFCwMT4JMmlAvGVEzDIFDwD",
	"oagFQMTTlKo+Kq94LiJAESeA7kFI7USHS6YXCrvQCoOYS89CWizKBL3HCpAegjAhAqREmBEkQdzTCFDG",
	"hfKt6UuYKzfn7NQ3Q2t3Rgb08E+RCgsF5Mij/TVNAT3EwCrL0QOWSMCKap+Czr4lFylWwWFAsIJ3iqYw",
	"IETlclOAG7G6shOKcq3JE6/18CIMXNj6Rt1MiWfRpJUfgeEZ59t66bDEj5vuQNB0aWX5ogibUDxjSz4d",
	"jjfTIPh2cMmzSWBxfngLlLxtuEsnDIX3qjKkEw2lecCpEtnxpUrA8lQLjwEnKl4HYUAEpkzrFwY5K/+8",
	"8PitC/+e3JOGKOSwWsrjIopBKoEVF/rPZAVjQr41hj+TVB0Ubyp//qIkmoISNDql8u78+LsEz/yjlOdM",
	"6UASKu+QzLDeLXIhgKlkjXIJBFGGzo+b0KdM/fOglkeZghWIWuAlTjfLuzw6f7WgK3t6AnmZM4Ovt7Hv",
	"5iTL/eZ9zdNbe/K7P7n4LhuSKNPCpkl5AZkJnljMUgXpVOJpZsAlT6DepAIsBF7vw6Y6SJcbk/S5VDm8",
	"JZbu7+GjnxCenBwE88LPVSZSfWLEGb6lCVXrkpW7dOhnSQVplmAF55hhDT8fYdrjqoceiUcRM9gcODtg",
	"/+3jQEpJiVdDC20MmxNUrqJ95vzn/OnRO6HAlCviesZGnC3pahMW3eIndnCh3Ut0Pmyap8dIhdOsyq1n",
	"zqpTlN/+AZGpxNrK9OzFCbUVBMuTBN9q9Nj6smc7zhW/wJqnas/ccp4AZvrzLZZw7fBi+Wnjirc5TciZ",
	"39PA7m+wLawxIVSHHScXLdX75zy/wNoXwO7JURSBlNf8DtiQYHJT80bv+5IKiASO7kCMDYvzFVzgVUvT",
	"hrfuQDBIxhZI8aML3RdgKxX3c+AcP9I0TxHJhS2AKUMxz4WcvF1hghXeroubDDIQWqEJT3+ZoKRrgQys",
	"JBnOZMyV38cKcDowUbWA2v/MFU40C1/RP2GyrnpXnzS0w1Idl7W0q5Ok6YvKti6QvPBsY7rhtSZK+0Y7",
	"g8p4eRC5GKEbAVjBpWsI9VjnRYRYU/azyPeFRNqMUN2lqxerWX2CG2w3bmi38aKwu5WVI0ekfaFSDcuS",
	"5RFi8kmws1f2DoB+L4Ec09HsIIO4GNsRRnO2o4o3g0a0+p6R7cK1o88YVMpN81iruSFvxvxDnstXb7WT",
	"bXlLEIDJ+oSnKWYDu4LO0bEBG9h/1zzeJN2SWx3Vkib9TqL2lrEd52xE2xe+ksOEkfCVa+4scZ6o4PDH",
	"otf116sgMzCsOaXv8DHaMLMXXdXOG6eTbjJWm9lnWPePRZ9hjZZcmDKn0dXW06qSXn8zAfGXy1wtpQ7D",
	"xvX1ULSkCci1VJAiSf+cVNl0XNCWGHZN7DnHVrjDkbNYO+3rfta7gBr0QvN4OEp63qi16vZ+dzCXpfwy",
	"RxpFqFHJNgQfMFX2NwNsPcgUfr5CtMq2SXY35E7cSk6rJD5td0Rr2m+SxkhPQzsHolxQtb7SXrRBO8ro",
	"Z1gf5faob64DY8AERH0h+O93Rxdn7yxGypwys+x1EXXtdkWVPqEHnz4eo09kBY2WxWEwf//h/dzQcgYM",
	"ZzQ4DH57P38/D0Jz+2hUmdk2rP51BZ7mzr/MZxTFEN0FZiVbgWhCdR9P3LfWTeRHe03WXsxtdqY9JHNT",
	"nS3zRFtUhKUmMyXwckmjSRrZ7LQTUCb443pMx2u38utVvf8wKyPg1VGHwnbC9DANE0vhbdVuPphbE786",
	"W7lg7F7Q+K4aK+OSNRKgcsGA9HWvL3p98ioDZnpQfUs6PlYPqh3aOrQ6r3b9pY+9VX/MwLi+lv/hMuln",
	"DmJdJ1Jvq55+u77YYWR853hPdPR3zWO1c0wg5lMCMd9h0GpaM45vEtqPhXaczNMUi3VpgrD1RW2HyVzM",
	"ut1JhfVBpFVgFGGQuVuULhzsEfqqKtncq4RjTtbbDlT7tF60tw4NpGL3YOmUmL5ktgNRZEaS/QOLNRFh",
	"xOCh8WLGB4subcye6kc0haXkBBT4cHNqvtS48dHIVt7n9BnkoL9ZWG0I+hvjyMHl7/sXOWsFwhui1nO2",
	"T416yKzheC0vwyqKfSG1Zf1bhXRnNNPuTkyiGQ+i7Cr7jihrhd4k4JFK1dg+XsIIs6y80HgdAAe2ItPs",
	"2nv4tVp2L0WfWWTfwWeMQLh7cNmAPHvX/I5QGfF7EOuZPkfLwTLhd1D2aai7o67mbSgb3J32aTn8q5Hy",
	"ygNI1dPBSfJtOZgantIiKBb9zs/EQgMnSevdkPxfhr8KbTeOG0M8a55jxyqYbuh+B/WtNXVbUXzmuw8b",
	"x9dEse2BXyKJy/ziHQ+/Is5P+ofZT8xjNrOfeN94nWNxZ8toLFHj4duUXD41S/d2kaFXQJ7dxWm5jXJ3",
	"JP6mI63LU2NnZeQOyd6999409uAXYoYaMXc0SYYB85kmSfNF5xSg6Dl7hxPjhr8mRsoOs5yZ9rJsoqEb",
	"b89l4Y5aHCPXkpPOgB/64be3ReVz6f1uQ5RBqy9QXKCraA4HePZkfpYtiYFDgefGZ1INUa79rPQNN/dJ",
	"t7Rk6YwtEcz2sd65XJt83GneKllUIFm9RN0vpF97zXgpxmf1RfJQ760VANs0enOsvxSYO0id52+mxLUL",
	"O7S078gjJRR8yAsnMecX+zjhL8ubYe8unBF4rP7/qd6MKVs5fyd8hVSMFZIxzxOCbqHmtweq4u7Vudf2",
	"5VKCeQBf6VS9IJl73kaklNE0T83H3juJN2P91lOY13C+Ibt9zzv3osfL90X196eqbdk73GrYtXuaIINi",
	"Ufw3AAD///TwPsVDPQAA",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
