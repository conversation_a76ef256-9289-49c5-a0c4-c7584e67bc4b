receivers:
  otlp:
    protocols:
      grpc:
        max_recv_msg_size_mib: 100
        read_buffer_size: 10943040
        max_concurrent_streams: 200
        write_buffer_size: 10943040
  prometheus:
    config:
      scrape_configs:
        - job_name: nomad
          scrape_interval: 15s
          scrape_timeout: 5s
          metrics_path: '/v1/metrics'
          static_configs:
            - targets: ['localhost:4646']
          params:
            format: ['prometheus']

processors:
  batch:
    timeout: 5s

  batch/clickhouse:
    timeout: 5s
    send_batch_size: 50000

  # keep only metrics that are used
  filter/otlp:
    # https://github.com/open-telemetry/opentelemetry-collector-contrib/tree/main/processor/filterprocessor
    metrics:
      include:
        match_type: regexp
        metric_names:
          - "orchestrator.*"
          - "api.*"
          - "client_proxy.*"
          - "Click*"
          - "click*"


  filter/prometheus:
    metrics:
      include:
        match_type: strict
        metric_names:
          - "nomad_client.host_cpu_total_percent"
          - "nomad_client_host_cpu_idle"
          - "nomad_client_host_disk_available"
          - "nomad_client_host_disk_size"
          - "nomad_client_host_memory_available"
          - "nomad_client_host_memory_total"
          - "nomad_client_allocs_memory_usage"
          - "nomad_client_allocs_memory_allocated"
          - "nomad_client_allocs_cpu_usage"
          - "nomad_client_allocs_cpu_allocated"

  filter/external_metrics:
    metrics:
      include:
        match_type: regexp
        metric_names:
          - "e2b.*"

  metricstransform:
    # https://github.com/open-telemetry/opentelemetry-collector-contrib/tree/main/processor/metricstransformprocessor
    transforms:
      - include: "nomad_client_host_cpu_idle"
        match_type: strict
        action: update
        operations:
          - action: aggregate_labels
            aggregation_type: sum
            label_set: [instance, node_id, node_status, node_pool]

  resourcedetection:
    # https://github.com/open-telemetry/opentelemetry-collector-contrib/tree/main/processor/resourcedetectionprocessor
    detectors: [gcp]
    override: true
    gcp:
      resource_attributes:
        cloud.provider:
          enabled: false
        cloud.platform:
          enabled: false
        cloud.account.id:
          enabled: false
        cloud.availability_zone:
          enabled: false
        cloud.region:
          enabled: false
        host.type:
          enabled: true
        host.id:
          enabled: true
        gcp.gce.instance.name:
          enabled: true
        host.name:
          enabled: true

  transform/set-name:
    metric_statements:
      - delete_key(datapoint.attributes, "instance")
      - delete_key(datapoint.attributes, "node_id")
      - delete_key(datapoint.attributes, "node_scheduling_eligibility")
      - delete_key(datapoint.attributes, "node_class")
      - delete_key(datapoint.attributes, "node_status")
      - delete_key(datapoint.attributes, "service_name")
      - set(datapoint.attributes["service.instance.id"], resource.attributes["gcp.gce.instance.name"])

  filter/rpc_duration_only:
    metrics:
      include:
        match_type: regexp
        # Include info about grpc server endpoint durations - used for monitoring request times
        metric_names:
          - "rpc.server.duration.*"
  resource/remove_instance:
    attributes:
      - action: delete
        key: service.instance.id
extensions:
  basicauth/grafana_cloud:
    # https://github.com/open-telemetry/opentelemetry-collector-contrib/tree/main/extension/basicauthextension
    client_auth:
      username: "${grafana_username}"
      password: "${grafana_otel_collector_token}"

  health_check:

exporters:
  debug:
    verbosity: detailed
  otlphttp/grafana_cloud:
    # https://github.com/open-telemetry/opentelemetry-collector/tree/main/exporter/otlpexporter
    endpoint: "${grafana_otlp_url}/otlp"
    auth:
      authenticator: basicauth/grafana_cloud
  clickhouse:
    endpoint: tcp://${clickhouse_host}:${clickhouse_port}
    database: ${clickhouse_database}
    username: ${clickhouse_username}
    password: ${clickhouse_password}
    async_insert: true
    create_schema: false
    metrics_tables:
      gauge:
        name: "metrics_gauge"
service:
  telemetry:
    logs:
      level: warn
  extensions:
    - basicauth/grafana_cloud
    - health_check
  pipelines:
    metrics:
      receivers:
        - otlp
      processors: [filter/otlp, batch]
      exporters:
        - otlphttp/grafana_cloud
    metrics/prometheus:
      receivers:
        - prometheus
      processors: [filter/prometheus, metricstransform, resourcedetection, transform/set-name, batch]
      exporters:
        - otlphttp/grafana_cloud
    metrics/rpc_only:
      receivers:
        - otlp
      processors: [filter/rpc_duration_only, resource/remove_instance, batch]
      exporters:
        - otlphttp/grafana_cloud
    metrics/external:
      receivers:  [otlp]
      processors: [filter/external_metrics, batch/clickhouse]
      exporters:  [clickhouse]
    traces:
      receivers:
        - otlp
      processors: [batch]
      exporters:
        - otlphttp/grafana_cloud
    logs:
      receivers:
        - otlp
      processors: [batch]
      exporters:
        - otlphttp/grafana_cloud