receivers:
  prometheus:
    config:
      scrape_configs:
        - job_name: "clickhouse"
          scrape_interval: 30s
          metrics_path: /metrics
          static_configs:
            - targets: ['localhost:${clickhouse_metrics_port}']
exporters:
  otlp:
    endpoint: http://localhost:${otel_collector_grpc_port}
    tls:
      insecure: true
processors:
  batch: {}

  resourcedetection:
    detectors: [gcp]
    override: true
    gcp:
      resource_attributes:
        cloud.provider:
          enabled: false
        cloud.platform:
          enabled: false
        cloud.account.id:
          enabled: false
        cloud.availability_zone:
          enabled: false
        cloud.region:
          enabled: false
        host.type:
          enabled: true
        host.id:
          enabled: true
        gcp.gce.instance.name:
          enabled: true
        host.name:
          enabled: true

  transform/set-name:
    metric_statements:
      - set(datapoint.attributes["service.instance.id"], resource.attributes["gcp.gce.instance.name"])


  # keep only metrics that are used
  filter:
    metrics:
      include:
        match_type: regexp
        metric_names:
          - "nomad_client_host_cpu_idle"
          - "nomad_client_host_disk_available"
          - "nomad_client_host_disk_size"
          - "nomad_client_allocs_memory_usage"
          - "nomad_client_allocs_cpu_usage"
          - "nomad_client_host_memory_available"
          - "nomad_client_host_memory_total"
          - "nomad_client_unallocated_memory"
          - "nomad_nomad_job_summary_running"
          - "orchestrator.*"
          - "api.*"
          - "client_proxy.*"
          # ──────  Query load & latency ──────
          - ClickHouseProfileEvents_SelectQuery
          - ClickHouseProfileEvents_FailedSelectQuery
          - ClickHouseProfileEvents_SelectQueryTimeMicroseconds
          - ClickHouseProfileEvents_InsertQuery
          - ClickHouseProfileEvents_FailedInsertQuery
          - ClickHouseProfileEvents_InsertQueryTimeMicroseconds
          - ClickHouseProfileEvents_QueryTimeMicroseconds
          - ClickHouseProfileEvents_Query
          - ClickHouseMetrics_Query
          - ClickHouseProfileEvents_QueryMemoryLimitExceeded

          # ──────  Table stats ──────
          - ClickHouseAsyncMetrics_TotalRowsOfMergeTreeTables
          - ClickHouseAsyncMetrics_TotalPartsOfMergeTreeTables
          - ClickHouseAsyncMetrics_TotalBytesOfMergeTreeTables

          # ──────  Read / write throughput ──────
          - ClickHouseProfileEvents_AsyncInsertBytes
          - ClickHouseProfileEvents_AsyncInsertRows
          - ClickHouseProfileEvents_InsertedBytes
          - ClickHouseProfileEvents_InsertedRows
          - ClickHouseProfileEvents_SelectedBytes
          - ClickHouseProfileEvents_SelectedRows
          - ClickHouseProfileEvents_SlowRead

          # ──────  Memory ──────
          - ClickHouseAsyncMetrics_CGroupMemoryUsed
          - ClickHouseAsyncMetrics_CGroupMemoryTotal

          # ──────  Network ──────
          - ClickHouseMetrics_NetworkSend
          - ClickHouseMetrics_NetworkReceive

          # ──────  Disk / S3 traffic ──────
          - ClickHouseAsyncMetrics_DiskTotal_default
          - ClickHouseAsyncMetrics_DiskAvailable_default
          - ClickHouseAsyncMetrics_DiskUsed_default
          - ClickHouseProfileEvents_S3GetObject
          - ClickHouseProfileEvents_S3PutObject
          - ClickHouseProfileEvents_ReadBufferFromS3Bytes
          - ClickHouseProfileEvents_WriteBufferFromS3Bytes

          # ──────  Connections ──────
          - ClickHouseMetrics_TCPConnection
          - ClickHouseMetrics_HTTPConnectionsTotal

service:
  telemetry:
    metrics:
      readers:
        - pull:
            exporter:
              prometheus:
                host: '0.0.0.0'
                port: 9999

  pipelines:
    metrics:
      receivers:  [prometheus]
      processors: [filter, resourcedetection, transform/set-name, batch]
      exporters:  [otlp]
