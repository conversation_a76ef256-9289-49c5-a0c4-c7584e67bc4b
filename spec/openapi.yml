openapi: 3.0.0
info:
  version: 0.1.0
  title: E2B API

servers:
  - url: https://api.e2b.app

components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key
    AccessTokenAuth:
      type: http
      scheme: bearer
      bearerFormat: access_token
    # Generated code uses security schemas in the alphabetical order.
    # In order to check first the token, and then the team (so we can already use the user),
    # there is a 1 and 2 present in the names of the security schemas.
    Supabase1TokenAuth:
      type: apiKey
      in: header
      name: X-Supabase-Token
    Supabase2TeamAuth:
      type: apiKey
      in: header
      name: X-Supabase-Team
    AdminTokenAuth:
      type: apiKey
      in: header
      name: X-Admin-Token

  parameters:
    templateID:
      name: templateID
      in: path
      required: true
      schema:
        type: string
    buildID:
      name: buildID
      in: path
      required: true
      schema:
        type: string
    sandboxID:
      name: sandboxID
      in: path
      required: true
      schema:
        type: string
    nodeID:
      name: nodeID
      in: path
      required: true
      schema:
        type: string
    apiKeyID:
      name: api<PERSON>ey<PERSON>
      in: path
      required: true
      schema:
        type: string
    accessTokenID:
      name: accessTokenID
      in: path
      required: true
      schema:
        type: string

  responses:
    "400":
      description: Bad request
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"
    "401":
      description: Authentication error
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"
    "404":
      description: Not found
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"
    "409":
      description: Conflict
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"
    "500":
      description: Server error
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"

  schemas:
    Team:
      required:
        - teamID
        - name
        - apiKey
        - isDefault
      properties:
        teamID:
          type: string
          description: Identifier of the team
        name:
          type: string
          description: Name of the team
        apiKey:
          type: string
          description: API key for the team
        isDefault:
          type: boolean
          description: Whether the team is the default team

    TeamUser:
      required:
        - id
        - email
      properties:
        id:
          type: string
          format: uuid
          description: Identifier of the user
        email:
          type: string
          description: Email of the user

    TemplateUpdateRequest:
      properties:
        public:
          type: boolean
          description: Whether the template is public or only accessible by the team

    CPUCount:
      type: integer
      format: int32
      minimum: 1
      description: CPU cores for the sandbox

    MemoryMB:
      type: integer
      format: int32
      minimum: 128
      description: Memory for the sandbox in MB

    SandboxMetadata:
      additionalProperties:
        type: string
        description: Metadata of the sandbox

    SandboxState:
      type: string
      description: State of the sandbox
      enum:
        - running
        - paused

    EnvVars:
      additionalProperties:
        type: string
        description: Environment variables for the sandbox

    SandboxLog:
      description: Log entry with timestamp and line
      required:
        - timestamp
        - line
      properties:
        timestamp:
          type: string
          format: date-time
          description: Timestamp of the log entry
        line:
          type: string
          description: Log line content

    SandboxLogs:
      required:
        - logs
      properties:
        logs:
          description: Logs of the sandbox
          type: array
          items:
            $ref: "#/components/schemas/SandboxLog"

    SandboxMetric:
      description: Metric entry with timestamp and line
      required:
        - timestamp
        - cpuCount
        - cpuUsedPct
        - memUsedMiB
        - memTotalMiB
      properties:
        timestamp:
          type: string
          format: date-time
          description: Timestamp of the metric entry
        cpuCount:
          type: integer
          format: int32
          description: Number of CPU cores
        cpuUsedPct:
          type: number
          format: float
          description: CPU usage percentage
        memUsedMiB:
          type: integer
          format: int64
          description: Memory used in MiB
        memTotalMiB:
          type: integer
          format: int64
          description: Total memory in MiB

    Sandbox:
      required:
        - templateID
        - sandboxID
        - clientID
        - envdVersion
      properties:
        templateID:
          type: string
          description: Identifier of the template from which is the sandbox created
        sandboxID:
          type: string
          description: Identifier of the sandbox
        alias:
          type: string
          description: Alias of the template
        clientID:
          type: string
          description: Identifier of the client
        envdVersion:
          type: string
          description: Version of the envd running in the sandbox
        envdAccessToken:
          type: string
          description: Access token used for envd communication

    SandboxDetail:
      required:
        - templateID
        - sandboxID
        - clientID
        - startedAt
        - cpuCount
        - memoryMB
        - endAt
        - state
      properties:
        templateID:
          type: string
          description: Identifier of the template from which is the sandbox created
        alias:
          type: string
          description: Alias of the template
        sandboxID:
          type: string
          description: Identifier of the sandbox
        clientID:
          type: string
          description: Identifier of the client
        startedAt:
          type: string
          format: date-time
          description: Time when the sandbox was started
        endAt:
          type: string
          format: date-time
          description: Time when the sandbox will expire
        envdVersion:
          type: string
          description: Version of the envd running in the sandbox
        envdAccessToken:
          type: string
          description: Access token used for envd communication
        cpuCount:
          $ref: "#/components/schemas/CPUCount"
        memoryMB:
          $ref: "#/components/schemas/MemoryMB"
        metadata:
          $ref: "#/components/schemas/SandboxMetadata"
        state:
          $ref: "#/components/schemas/SandboxState"

    ListedSandbox:
      required:
        - templateID
        - sandboxID
        - clientID
        - startedAt
        - cpuCount
        - memoryMB
        - endAt
        - state
      properties:
        templateID:
          type: string
          description: Identifier of the template from which is the sandbox created
        alias:
          type: string
          description: Alias of the template
        sandboxID:
          type: string
          description: Identifier of the sandbox
        clientID:
          type: string
          description: Identifier of the client
        startedAt:
          type: string
          format: date-time
          description: Time when the sandbox was started
        endAt:
          type: string
          format: date-time
          description: Time when the sandbox will expire
        cpuCount:
          $ref: "#/components/schemas/CPUCount"
        memoryMB:
          $ref: "#/components/schemas/MemoryMB"
        metadata:
          $ref: "#/components/schemas/SandboxMetadata"
        state:
          $ref: "#/components/schemas/SandboxState"

    RunningSandboxWithMetrics:
      required:
        - templateID
        - sandboxID
        - clientID
        - startedAt
        - cpuCount
        - memoryMB
        - endAt
      properties:
        templateID:
          type: string
          description: Identifier of the template from which is the sandbox created
        alias:
          type: string
          description: Alias of the template
        sandboxID:
          type: string
          description: Identifier of the sandbox
        clientID:
          type: string
          description: Identifier of the client
        startedAt:
          type: string
          format: date-time
          description: Time when the sandbox was started
        endAt:
          type: string
          format: date-time
          description: Time when the sandbox will expire
        cpuCount:
          $ref: "#/components/schemas/CPUCount"
        memoryMB:
          $ref: "#/components/schemas/MemoryMB"
        metadata:
          $ref: "#/components/schemas/SandboxMetadata"
        metrics:
          type: array
          items:
            $ref: "#/components/schemas/SandboxMetric"

    NewSandbox:
      required:
        - templateID
      properties:
        templateID:
          type: string
          description: Identifier of the required template
        timeout:
          type: integer
          format: int32
          minimum: 0
          default: 15
          description: Time to live for the sandbox in seconds.
        autoPause:
          type: boolean
          default: false
          description: Automatically pauses the sandbox after the timeout
        secure:
          type: boolean
          description: Secure all system communication with sandbox
        metadata:
          $ref: "#/components/schemas/SandboxMetadata"
        envVars:
          $ref: "#/components/schemas/EnvVars"

    ResumedSandbox:
      properties:
        timeout:
          type: integer
          format: int32
          minimum: 0
          default: 15
          description: Time to live for the sandbox in seconds.
        autoPause:
          type: boolean
          default: false
          description: Automatically pauses the sandbox after the timeout

    Template:
      required:
        - templateID
        - buildID
        - cpuCount
        - memoryMB
        - public
        - createdAt
        - updatedAt
        - createdBy
        - lastSpawnedAt
        - spawnCount
        - buildCount
      properties:
        templateID:
          type: string
          description: Identifier of the template
        buildID:
          type: string
          description: Identifier of the last successful build for given template
        cpuCount:
          $ref: "#/components/schemas/CPUCount"
        memoryMB:
          $ref: "#/components/schemas/MemoryMB"
        public:
          type: boolean
          description: Whether the template is public or only accessible by the team
        aliases:
          type: array
          description: Aliases of the template
          items:
            type: string
        createdAt:
          type: string
          format: date-time
          description: Time when the template was created
        updatedAt:
          type: string
          format: date-time
          description: Time when the template was last updated
        createdBy:
          allOf:
            - $ref: "#/components/schemas/TeamUser"
          nullable: true
        lastSpawnedAt:
          type: string
          format: date-time
          description: Time when the template was last used
        spawnCount:
          type: integer
          format: int64
          description: Number of times the template was used
        buildCount:
          type: integer
          format: int32
          description: Number of times the template was built

    TemplateBuildRequest:
      required:
        - dockerfile
      properties:
        alias:
          description: Alias of the template
          type: string
        dockerfile:
          description: Dockerfile for the template
          type: string
        teamID:
          type: string
          description: Identifier of the team
        startCmd:
          description: Start command to execute in the template after the build
          type: string
        readyCmd:
          description: Ready check command to execute in the template after the build
          type: string
        cpuCount:
          $ref: "#/components/schemas/CPUCount"
        memoryMB:
          $ref: "#/components/schemas/MemoryMB"

    TemplateBuild:
      required:
        - templateID
        - buildID
        - status
        - logs
      properties:
        logs:
          default: []
          description: Build logs
          type: array
          items:
            type: string
        templateID:
          type: string
          description: Identifier of the template
        buildID:
          type: string
          description: Identifier of the build
        status:
          type: string
          description: Status of the template
          enum:
            - building
            - waiting
            - ready
            - error

    NodeStatus:
      type: string
      description: Status of the node
      enum:
        - ready
        - draining
        - connecting
        - unhealthy

    NodeStatusChange:
      required:
        - status
      properties:
        status:
          $ref: "#/components/schemas/NodeStatus"

    Node:
      required:
        - nodeID
        - status
        - sandboxCount
        - allocatedCPU
        - allocatedMemoryMiB
        - createFails
        - sandboxStartingCount
        - version
        - commit
      properties:
        version:
          type: string
          description: Version of the orchestrator
        commit:
          type: string
          description: Commit of the orchestrator
        nodeID:
          type: string
          description: Identifier of the node
        status:
          $ref: "#/components/schemas/NodeStatus"
        sandboxCount:
          type: integer
          format: int32
          description: Number of sandboxes running on the node
        allocatedCPU:
          type: integer
          format: int32
          description: Number of allocated CPU cores
        allocatedMemoryMiB:
          type: integer
          format: int32
          description: Amount of allocated memory in MiB
        createFails:
          type: integer
          format: uint64
          description: Number of sandbox create fails
        sandboxStartingCount:
          type: integer
          format: int
          description: Number of starting Sandboxes

    NodeDetail:
      required:
        - nodeID
        - status
        - sandboxes
        - cachedBuilds
        - createFails
        - version
        - commit
      properties:
        version:
          type: string
          description: Version of the orchestrator
        commit:
          type: string
          description: Commit of the orchestrator
        nodeID:
          type: string
          description: Identifier of the node
        status:
          $ref: "#/components/schemas/NodeStatus"
        sandboxes:
          type: array
          description: List of sandboxes running on the node
          items:
            $ref: "#/components/schemas/ListedSandbox"
        cachedBuilds:
          type: array
          description: List of cached builds id on the node
          items:
            type: string
        createFails:
          type: integer
          format: uint64
          description: Number of sandbox create fails

    CreatedAccessToken:
      required:
        - id
        - name
        - token
        - mask
        - createdAt
      properties:
        id:
          type: string
          format: uuid
          description: Identifier of the access token
        name:
          type: string
          description: Name of the access token
        token:
          type: string
          description: The fully created access token
        mask:
          $ref: "#/components/schemas/IdentifierMaskingDetails"
        createdAt:
          type: string
          format: date-time
          description: Timestamp of access token creation

    NewAccessToken:
      required:
        - name
      properties:
        name:
          type: string
          description: Name of the access token

    TeamAPIKey:
      required:
        - id
        - name
        - mask
        - createdAt
      properties:
        id:
          type: string
          format: uuid
          description: Identifier of the API key
        name:
          type: string
          description: Name of the API key
        mask:
          $ref: "#/components/schemas/IdentifierMaskingDetails"
        createdAt:
          type: string
          format: date-time
          description: Timestamp of API key creation
        createdBy:
          allOf:
            - $ref: "#/components/schemas/TeamUser"
          nullable: true
        lastUsed:
          type: string
          format: date-time
          description: Last time this API key was used
          nullable: true

    CreatedTeamAPIKey:
      required:
        - id
        - key
        - mask
        - name
        - createdAt
      properties:
        id:
          type: string
          format: uuid
          description: Identifier of the API key
        key:
          type: string
          description: Raw value of the API key
        mask:
          $ref: "#/components/schemas/IdentifierMaskingDetails"
        name:
          type: string
          description: Name of the API key
        createdAt:
          type: string
          format: date-time
          description: Timestamp of API key creation
        createdBy:
          allOf:
            - $ref: "#/components/schemas/TeamUser"
          nullable: true
        lastUsed:
          type: string
          format: date-time
          description: Last time this API key was used
          nullable: true

    NewTeamAPIKey:
      required:
        - name
      properties:
        name:
          type: string
          description: Name of the API key

    UpdateTeamAPIKey:
      required:
        - name
      properties:
        name:
          type: string
          description: New name for the API key

    Error:
      required:
        - code
        - message
      properties:
        code:
          type: integer
          format: int32
          description: Error code
        message:
          type: string
          description: Error

    IdentifierMaskingDetails:
      required:
        - prefix
        - valueLength
        - maskedValuePrefix
        - maskedValueSuffix
      properties:
        prefix:
          type: string
          description: Prefix that identifies the token or key type
        valueLength:
          type: integer
          description: Length of the token or key
        maskedValuePrefix:
          type: string
          description: Prefix used in masked version of the token or key
        maskedValueSuffix:
          type: string
          description: Suffix used in masked version of the token or key

tags:
  - name: templates
  - name: sandboxes
  - name: auth
  - name: access-tokens
  - name: api-keys

paths:
  /health:
    get:
      description: Health check
      responses:
        "200":
          description: Request was successful
        "401":
          $ref: "#/components/responses/401"

  /teams:
    get:
      description: List all teams
      tags: [auth]
      security:
        - AccessTokenAuth: []
        - Supabase1TokenAuth: []
      responses:
        "200":
          description: Successfully returned all teams
          content:
            application/json:
              schema:
                type: array
                items:
                  allOf:
                    - $ref: "#/components/schemas/Team"
        "401":
          $ref: "#/components/responses/401"
        "500":
          $ref: "#/components/responses/500"

  /sandboxes:
    get:
      description: List all running sandboxes
      tags: [sandboxes]
      security:
        - ApiKeyAuth: []
        - Supabase1TokenAuth: []
          Supabase2TeamAuth: []
      parameters:
        - name: metadata
          in: query
          description: Metadata query used to filter the sandboxes (e.g. "user=abc&app=prod"). Each key and values must be URL encoded.
          required: false
          schema:
            type: string
      responses:
        "200":
          description: Successfully returned all running sandboxes
          content:
            application/json:
              schema:
                type: array
                items:
                  allOf:
                    - $ref: "#/components/schemas/ListedSandbox"
        "401":
          $ref: "#/components/responses/401"
        "400":
          $ref: "#/components/responses/400"
        "500":
          $ref: "#/components/responses/500"
    post:
      description: Create a sandbox from the template
      tags: [sandboxes]
      security:
        - ApiKeyAuth: []
        - Supabase1TokenAuth: []
          Supabase2TeamAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/NewSandbox"
      responses:
        "201":
          description: The sandbox was created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Sandbox"
        "401":
          $ref: "#/components/responses/401"
        "400":
          $ref: "#/components/responses/400"
        "500":
          $ref: "#/components/responses/500"

  /v2/sandboxes:
    get:
      description: List all sandboxes
      tags: [sandboxes]
      security:
        - ApiKeyAuth: []
        - Supabase1TokenAuth: []
          Supabase2TeamAuth: []
      parameters:
        - name: metadata
          in: query
          description: Metadata query used to filter the sandboxes (e.g. "user=abc&app=prod"). Each key and values must be URL encoded.
          required: false
          schema:
            type: string
        - name: state
          in: query
          description: Filter sandboxes by one or more states
          required: false
          schema:
            type: array
            items:
              $ref: "#/components/schemas/SandboxState"
          style: form
          explode: false
        - name: nextToken
          in: query
          description: Cursor to start the list from
          required: false
          schema:
            type: string
        - name: limit
          in: query
          description: Maximum number of items to return per page
          required: false
          schema:
            type: integer
            format: int32
            minimum: 1
            default: 1000
            maximum: 1000
      responses:
        "200":
          description: Successfully returned all running sandboxes
          content:
            application/json:
              schema:
                type: array
                items:
                  allOf:
                    - $ref: "#/components/schemas/ListedSandbox"
        "401":
          $ref: "#/components/responses/401"
        "400":
          $ref: "#/components/responses/400"
        "500":
          $ref: "#/components/responses/500"

  /sandboxes/metrics:
    get:
      description: List all running sandboxes with metrics
      tags: [sandboxes]
      security:
        - ApiKeyAuth: []
      parameters:
        - name: metadata
          in: query
          description: Metadata query used to filter the sandboxes (e.g. "user=abc&app=prod"). Each key and values must be URL encoded.
          required: false
          schema:
            type: string
      responses:
        "200":
          description: Successfully returned all running sandboxes with metrics
          content:
            application/json:
              schema:
                type: array
                items:
                  allOf:
                    - $ref: "#/components/schemas/RunningSandboxWithMetrics"
        "401":
          $ref: "#/components/responses/401"
        "400":
          $ref: "#/components/responses/400"
        "500":
          $ref: "#/components/responses/500"

  /sandboxes/{sandboxID}/logs:
    get:
      description: Get sandbox logs
      tags: [sandboxes]
      security:
        - ApiKeyAuth: []
        - Supabase1TokenAuth: []
          Supabase2TeamAuth: []
      parameters:
        - $ref: "#/components/parameters/sandboxID"
        - in: query
          name: start
          schema:
            type: integer
            format: int64
            minimum: 0
          description: Starting timestamp of the logs that should be returned in milliseconds
        - in: query
          name: limit
          schema:
            default: 1000
            format: int32
            minimum: 0
            type: integer
          description: Maximum number of logs that should be returned
      responses:
        "200":
          description: Successfully returned the sandbox logs
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SandboxLogs"
        "404":
          $ref: "#/components/responses/404"
        "401":
          $ref: "#/components/responses/401"
        "500":
          $ref: "#/components/responses/500"

  /sandboxes/{sandboxID}/metrics:
    get:
      description: Get sandbox metrics
      tags: [sandboxes]
      security:
        - ApiKeyAuth: []
        - Supabase1TokenAuth: []
          Supabase2TeamAuth: []
      parameters:
        - $ref: "#/components/parameters/sandboxID"
      responses:
        "200":
          description: Successfully returned the sandbox metrics
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/SandboxMetric"
        "404":
          $ref: "#/components/responses/404"
        "401":
          $ref: "#/components/responses/401"
        "500":
          $ref: "#/components/responses/500"

  /sandboxes/{sandboxID}:
    get:
      description: Get a sandbox by id
      tags: [sandboxes]
      security:
        - ApiKeyAuth: []
        - Supabase1TokenAuth: []
          Supabase2TeamAuth: []
      parameters:
        - $ref: "#/components/parameters/sandboxID"
      responses:
        "200":
          description: Successfully returned the sandbox
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SandboxDetail"
        "404":
          $ref: "#/components/responses/404"
        "401":
          $ref: "#/components/responses/401"
        "500":
          $ref: "#/components/responses/500"

    delete:
      description: Kill a sandbox
      tags: [sandboxes]
      security:
        - ApiKeyAuth: []
        - Supabase1TokenAuth: []
          Supabase2TeamAuth: []
      parameters:
        - $ref: "#/components/parameters/sandboxID"
      responses:
        "204":
          description: The sandbox was killed successfully
        "404":
          $ref: "#/components/responses/404"
        "401":
          $ref: "#/components/responses/401"
        "500":
          $ref: "#/components/responses/500"

  # TODO: Pause and resume might be exposed as POST /sandboxes/{sandboxID}/snapshot and then POST /sandboxes with specified snapshotting setup
  /sandboxes/{sandboxID}/pause:
    post:
      description: Pause the sandbox
      tags: [sandboxes]
      security:
        - ApiKeyAuth: []
        - Supabase1TokenAuth: []
          Supabase2TeamAuth: []
      parameters:
        - $ref: "#/components/parameters/sandboxID"
      responses:
        "204":
          description: The sandbox was paused successfully and can be resumed
        "409":
          $ref: "#/components/responses/409"
        "404":
          $ref: "#/components/responses/404"
        "401":
          $ref: "#/components/responses/401"
        "500":
          $ref: "#/components/responses/500"

  /sandboxes/{sandboxID}/resume:
    post:
      description: Resume the sandbox
      tags: [sandboxes]
      security:
        - ApiKeyAuth: []
        - Supabase1TokenAuth: []
          Supabase2TeamAuth: []
      parameters:
        - $ref: "#/components/parameters/sandboxID"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ResumedSandbox"
      responses:
        "201":
          description: The sandbox was resumed successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Sandbox"
        "409":
          $ref: "#/components/responses/409"
        "404":
          $ref: "#/components/responses/404"
        "401":
          $ref: "#/components/responses/401"
        "500":
          $ref: "#/components/responses/500"

  /sandboxes/{sandboxID}/timeout:
    post:
      description: Set the timeout for the sandbox. The sandbox will expire x seconds from the time of the request. Calling this method multiple times overwrites the TTL, each time using the current timestamp as the starting point to measure the timeout duration.
      security:
        - ApiKeyAuth: []
        - Supabase1TokenAuth: []
          Supabase2TeamAuth: []
      tags: [sandboxes]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - timeout
              properties:
                timeout:
                  description: Timeout in seconds from the current time after which the sandbox should expire
                  type: integer
                  format: int32
                  minimum: 0
      parameters:
        - $ref: "#/components/parameters/sandboxID"
      responses:
        "204":
          description: Successfully set the sandbox timeout
        "401":
          $ref: "#/components/responses/401"
        "404":
          $ref: "#/components/responses/404"
        "500":
          $ref: "#/components/responses/500"

  /sandboxes/{sandboxID}/refreshes:
    post:
      description: Refresh the sandbox extending its time to live
      security:
        - ApiKeyAuth: []
        - Supabase1TokenAuth: []
          Supabase2TeamAuth: []
      tags: [sandboxes]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                duration:
                  description: Duration for which the sandbox should be kept alive in seconds
                  type: integer
                  maximum: 3600 # 1 hour
                  minimum: 0
      parameters:
        - $ref: "#/components/parameters/sandboxID"
      responses:
        "204":
          description: Successfully refreshed the sandbox
        "401":
          $ref: "#/components/responses/401"
        "404":
          $ref: "#/components/responses/404"

  /templates:
    get:
      description: List all templates
      tags: [templates]
      security:
        - AccessTokenAuth: []
        - Supabase1TokenAuth: []
      parameters:
        - in: query
          required: false
          name: teamID
          schema:
            type: string
            description: Identifier of the team
      responses:
        "200":
          description: Successfully returned all templates
          content:
            application/json:
              schema:
                type: array
                items:
                  allOf:
                    - $ref: "#/components/schemas/Template"
        "401":
          $ref: "#/components/responses/401"
        "500":
          $ref: "#/components/responses/500"
    post:
      description: Create a new template
      tags: [templates]
      security:
        - AccessTokenAuth: []
        - Supabase1TokenAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/TemplateBuildRequest"

      responses:
        "202":
          description: The build was accepted
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Template"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "500":
          $ref: "#/components/responses/500"

  /templates/{templateID}:
    post:
      description: Rebuild an template
      tags: [templates]
      security:
        - AccessTokenAuth: []
        - Supabase1TokenAuth: []
      parameters:
        - $ref: "#/components/parameters/templateID"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/TemplateBuildRequest"

      responses:
        "202":
          description: The build was accepted
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Template"
        "401":
          $ref: "#/components/responses/401"
        "500":
          $ref: "#/components/responses/500"
    delete:
      description: Delete a template
      tags: [templates]
      security:
        - AccessTokenAuth: []
        - Supabase1TokenAuth: []
      parameters:
        - $ref: "#/components/parameters/templateID"
      responses:
        "204":
          description: The template was deleted successfully
        "401":
          $ref: "#/components/responses/401"
        "500":
          $ref: "#/components/responses/500"
    patch:
      description: Update template
      tags: [templates]
      security:
        - AccessTokenAuth: []
        - Supabase1TokenAuth: []
      parameters:
        - $ref: "#/components/parameters/templateID"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/TemplateUpdateRequest"
      responses:
        "200":
          description: The template was updated successfully
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "500":
          $ref: "#/components/responses/500"

  /templates/{templateID}/builds/{buildID}:
    post:
      description: Start the build
      tags: [templates]
      security:
        - AccessTokenAuth: []
        - Supabase1TokenAuth: []
      parameters:
        - $ref: "#/components/parameters/templateID"
        - $ref: "#/components/parameters/buildID"
      responses:
        "202":
          description: The build has started
        "401":
          $ref: "#/components/responses/401"
        "500":
          $ref: "#/components/responses/500"

  /templates/{templateID}/builds/{buildID}/status:
    get:
      description: Get template build info
      tags: [templates]
      security:
        - AccessTokenAuth: []
        - Supabase1TokenAuth: []
      parameters:
        - $ref: "#/components/parameters/templateID"
        - $ref: "#/components/parameters/buildID"
        - in: query
          name: logsOffset
          schema:
            default: 0
            type: integer
            format: int32
            minimum: 0
          description: Index of the starting build log that should be returned with the template
      responses:
        "200":
          description: Successfully returned the template
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TemplateBuild"
        "401":
          $ref: "#/components/responses/401"
        "404":
          $ref: "#/components/responses/404"
        "500":
          $ref: "#/components/responses/500"

  /nodes:
    get:
      description: List all nodes
      tags: [admin]
      security:
        - AdminTokenAuth: []
      responses:
        "200":
          description: Successfully returned all nodes
          content:
            application/json:
              schema:
                type: array
                items:
                  allOf:
                    - $ref: "#/components/schemas/Node"
        "401":
          $ref: "#/components/responses/401"
        "500":
          $ref: "#/components/responses/500"

  /nodes/{nodeID}:
    get:
      description: Get node info
      tags: [admin]
      security:
        - AdminTokenAuth: []
      parameters:
        - $ref: "#/components/parameters/nodeID"
      responses:
        "200":
          description: Successfully returned the node
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/NodeDetail"
        "401":
          $ref: "#/components/responses/401"
        "404":
          $ref: "#/components/responses/404"
        "500":
          $ref: "#/components/responses/500"
    post:
      description: Change status of a node
      tags: [admin]
      security:
        - AdminTokenAuth: []
      parameters:
        - $ref: "#/components/parameters/nodeID"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/NodeStatusChange"
      responses:
        "204":
          description: The node status was changed successfully
        "401":
          $ref: "#/components/responses/401"
        "404":
          $ref: "#/components/responses/404"
        "500":
          $ref: "#/components/responses/500"

  /access-tokens:
    post:
      description: Create a new access token
      tags: [access-tokens]
      security:
        - Supabase1TokenAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/NewAccessToken"
      responses:
        "201":
          description: Access token created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreatedAccessToken"
        "401":
          $ref: "#/components/responses/401"
        "500":
          $ref: "#/components/responses/500"

  /access-tokens/{accessTokenID}:
    delete:
      description: Delete an access token
      tags: [access-tokens]
      security:
        - Supabase1TokenAuth: []
      parameters:
        - $ref: "#/components/parameters/accessTokenID"
      responses:
        "204":
          description: Access token deleted successfully
        "401":
          $ref: "#/components/responses/401"
        "404":
          $ref: "#/components/responses/404"
        "500":
          $ref: "#/components/responses/500"

  /api-keys:
    get:
      description: List all team API keys
      tags: [api-keys]
      security:
        - Supabase1TokenAuth: []
          Supabase2TeamAuth: []
      responses:
        "200":
          description: Successfully returned all team API keys
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/TeamAPIKey"
        "401":
          $ref: "#/components/responses/401"
        "500":
          $ref: "#/components/responses/500"
    post:
      description: Create a new team API key
      tags: [api-keys]
      security:
        - Supabase1TokenAuth: []
          Supabase2TeamAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/NewTeamAPIKey"
      responses:
        "201":
          description: Team API key created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreatedTeamAPIKey"
        "401":
          $ref: "#/components/responses/401"
        "500":
          $ref: "#/components/responses/500"

  /api-keys/{apiKeyID}:
    patch:
      description: Update a team API key
      tags: [api-keys]
      security:
        - Supabase1TokenAuth: []
          Supabase2TeamAuth: []
      parameters:
        - $ref: "#/components/parameters/apiKeyID"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateTeamAPIKey"
      responses:
        "200":
          description: Team API key updated successfully
        "401":
          $ref: "#/components/responses/401"
        "404":
          $ref: "#/components/responses/404"
        "500":
          $ref: "#/components/responses/500"
    delete:
      description: Delete a team API key
      tags: [api-keys]
      security:
        - Supabase1TokenAuth: []
          Supabase2TeamAuth: []
      parameters:
        - $ref: "#/components/parameters/apiKeyID"
      responses:
        "204":
          description: Team API key deleted successfully
        "401":
          $ref: "#/components/responses/401"
        "404":
          $ref: "#/components/responses/404"
        "500":
          $ref: "#/components/responses/500"
