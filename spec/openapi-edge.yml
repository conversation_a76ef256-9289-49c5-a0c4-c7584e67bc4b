openapi: 3.0.0
info:
  version: 0.1.0
  title: E2B Edge

components:
  parameters:
    sandbox_id:
      name: sandbox_id
      in: path
      required: true
      schema:
        type: string
      description: Unique identifier of the sandbox

  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key

  responses:
    "400":
      description: Bad request
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"
    "401":
      description: Authentication error
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"
    "404":
      description: Not found
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"
    "409":
      description: Conflict
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"
    "500":
      description: Server error
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"

  schemas:
    ClusterNodeInfo:
      required:
        - id
        - nodeId
        - version
        - commit
        - startup
        - status
      properties:
        id:
          type: string
          description: Service ID
        nodeId:
          type: string
          description: Node ID
        version:
          type: string
          description: Version of the node
        commit:
          type: string
          description: Version of the node
        startup:
          type: string
          format: date-time
          description: Time when the node started
        status:
          $ref: "#/components/schemas/ClusterNodeStatus"

    ClusterNode:
      required:
        - id
        - nodeId
        - version
        - commit
        - type
        - host
        - startedAt
        - status
      properties:
        id:
          type: string
          description: Service ID
        nodeId:
          type: string
          description: Node ID
        version:
            type: string
            description: Version of the node
        commit:
          type: string
          description: Source code version of the node
        host:
          type: string
          description: Node private host address and service port
        startedAt:
          type: string
          format: date-time
          description: Time when the node was registered
        type:
          $ref: "#/components/schemas/ClusterNodeType"
        status:
          $ref: "#/components/schemas/ClusterNodeStatus"

    ClusterNodeStatus:
      type: string
      description: State of the cluster node
      enum:
        - healthy
        - draining
        - unhealthy

    ClusterNodeType:
      type: string
      description: Cluster node type
      enum:
        - orchestrator
        - edge

    ClusterOrchestratorRole:
      type: string
      description: Capability of the orchestrator
      enum:
        - orchestrator
        - templateManager

    ClusterOrchestratorNode:
      properties:
        id:
          type: string
          description: Service ID
        nodeId:
          type: string
          description: Node ID
        version:
          type: string
          description: Service Version
        commit:
          type: string
          description: Service Version
        host:
          type: string
          description: Node private host address and service port
        startedAt:
          type: string
          format: date-time
          description: Time when the node was registered
        roles:
          type: array
          items:
            $ref: '#/components/schemas/ClusterOrchestratorRole'
        status:
          $ref: "#/components/schemas/ClusterNodeStatus"
        metricVCpuUsed:
          type: integer
          format: int64
          description: Number of vCPUs currently in use
        metricRamMBUsed:
          type: integer
          format: int64
          description: Amount of RAM currently used in MB
        metricDiskMBUsed:
          type: integer
          format: int64
          description: Amount of disk space currently used in MB
        metricSandboxesRunning:
          type: integer
          format: int64
          description: Amount of disk space currently used in MB
      required:
        - id
        - nodeId
        - version
        - commit
        - host
        - startedAt
        - status
        - roles
        - metricVCpuUsed
        - metricRamMBUsed
        - metricDiskMBUsed
        - metricSandboxesRunning

    Error:
      required:
        - code
        - message
      properties:
        code:
          type: integer
          format: int32
          description: Error code
        message:
          type: string
          description: Error

    SandboxCreateRequest:
      type: object
      properties:
        sandbox:
          $ref: '#/components/schemas/SandboxConfig'
        startTime:
          $ref: '#/components/schemas/Timestamp'
        endTime:
          $ref: '#/components/schemas/Timestamp'
      required: [sandbox, startTime, endTime]

    SandboxCreateResponse:
      type: object
      properties:
        clientId:
          type: string
      required: [clientId]

    SandboxUpdateRequest:
      type: object
      properties:
        endTime:
          $ref: '#/components/schemas/Timestamp'
      required: [endTime]

    SandboxPauseRequest:
      type: object
      properties:
        templateId:
          type: string
        buildId:
          type: string
      required: [templateId, buildId]

    SandboxListResponse:
      type: object
      properties:
        sandboxes:
          type: array
          items:
            $ref: '#/components/schemas/RunningSandbox'
      required: [sandboxes]

    SandboxConfig:
      type: object
      properties:
        orchestratorId:
          type: string
        templateId:
          type: string
        buildId:
          type: string
        sandboxId:
          type: string
        teamId:
          type: string
        baseTemplateId:
          type: string
          nullable: true
        kernelVersion:
          type: string
        firecrackerVersion:
          type: string
        envdVersion:
          type: string
        alias:
          type: string
          nullable: true
        envdAccessToken:
          type: string
        snapshot:
          type: boolean
        autoPause:
          type: boolean
        hugePages:
          type: boolean
        ramMB:
          type: integer
          format: int64
        vCPU:
          type: integer
          format: int64
        totalDiskSizeMB:
          type: integer
          format: int64
        maxSandboxLength:
          type: integer
          format: int64
          description: Maximum duration in hours
        envVars:
          type: object
          nullable: true
          additionalProperties:
            type: string
        metadata:
          type: object
          nullable: true
          additionalProperties:
            type: string
      required: [orchestratorId, templateId, buildId, sandboxId, teamId, kernelVersion, firecrackerVersion, envdVersion, snapshot, hugePages, totalDiskSizeMB, vCPU, ramMB, maxSandboxLength]

    RunningSandbox:
      type: object
      properties:
        config:
          $ref: '#/components/schemas/SandboxConfig'
        client_id:
          type: string
        startTime:
          $ref: '#/components/schemas/Timestamp'
        endTime:
          $ref: '#/components/schemas/Timestamp'

    TemplateBuildMetadata:
      required:
        - rootfsSizeKey
        - envdVersionKey
      properties:
        rootfsSizeKey:
          type: integer
          format: int32
          description: Key for the root filesystem size
        envdVersionKey:
          type: string
          description: Key for the version of envd used in the build

    TemplateBuildCreateRequest:
      type: object
      properties:
        orchestratorId:
          type: string
        buildId:
          type: string
        templateId:
          type: string
        hugePages:
          type: boolean
        ramMB:
          type: integer
          format: int64
        vCPU:
          type: integer
          format: int64
        diskSizeMB:
          type: integer
          format: int64
        kernelVersion:
          type: string
        firecrackerVersion:
          type: string
        startCommand:
          type: string
        readyCommand:
          type: string
      required:
        - orchestratorId
        - templateId
        - buildId

        - hugePages
        - ramMB
        - vCPU
        - diskSizeMB

        - kernelVersion
        - firecrackerVersion

        - startCommand
        - readyCommand

    TemplateBuildStatusResponse:
      required:
        - templateID
        - buildID
        - status
      properties:
        templateID:
          type: string
          description: Identifier of the template
        buildID:
          type: string
          description: Identifier of the build
        status:
          type: string
          description: Status of the template
          enum:
            - building
            - waiting
            - ready
            - error
        metadata:
          $ref: '#/components/schemas/TemplateBuildMetadata'

    TemplateBuildLogsResponse:
      required:
        - logs
      properties:
        logs:
          default: []
          description: Build logs
          type: array
          items:
            type: string

    Timestamp:
      type: string
      format: date-time

tags:
  - name: service-discovery
  - name: sandboxes

paths:
  /health:
    get:
      operationId: healthCheck
      description: Health check
      responses:
        "200":
          description: Request was successful

  /health/traffic:
    get:
      operationId: healthCheckTraffic
      description: Health check for traffic proxy
      responses:
        "200":
          description: Request was successful

  /v1/info:
    get:
        operationId: v1Info
        description: Edge node information
        responses:
          "200":
            description: Successfully returned node information
            content:
              application/json:
                schema:
                  $ref: "#/components/schemas/ClusterNodeInfo"
          "401":
            $ref: "#/components/responses/401"
          "500":
            $ref: "#/components/responses/500"

  /v1/service-discovery/nodes:
    get:
        operationId: v1ServiceDiscoveryNodes
        description: Get the service discovery information
        security:
          - ApiKeyAuth: [ ]
        tags: [service-discovery]
        responses:
          "200":
            description: Successfully returned all cluster nodes
            content:
              application/json:
                schema:
                  type: array
                  items:
                    allOf:
                      - $ref: "#/components/schemas/ClusterNode"
          "401":
            $ref: "#/components/responses/401"
          "500":
            $ref: "#/components/responses/500"

  /v1/service-discovery/nodes/{node_id}/drain:
    post:
      operationId: v1ServiceDiscoveryNodeDrain
      description: Mark node as draining
      security:
        - ApiKeyAuth: [ ]
      tags: [service-discovery]
      parameters:
        - name: node_id
          in: path
          required: true
          description: Node ID
          schema:
            type: string
      responses:
        "200":
          description: Successfully start of node draining
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "404":
          $ref: "#/components/responses/404"
        "500":
          $ref: "#/components/responses/500"

  /v1/service-discovery/nodes/{node_id}/kill:
    post:
      operationId: v1ServiceDiscoveryNodeKill
      description: Kill the node
      security:
        - ApiKeyAuth: [ ]
      tags: [service-discovery]
      parameters:
        - name: node_id
          in: path
          required: true
          description: Node ID
          schema:
            type: string
      responses:
        "200":
          description: Successfully start of node kill
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "404":
          $ref: "#/components/responses/404"
        "500":
          $ref: "#/components/responses/500"

  /v1/service-discovery/nodes/orchestrators:
    get:
      operationId: v1ServiceDiscoveryGetOrchestrators
      summary: Get the orchestrators
      security:
        - ApiKeyAuth: [ ]
      tags: [service-discovery]
      responses:
        "200":
          description: Successfully returned all orchestrators
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/ClusterOrchestratorNode"
        "401":
          $ref: "#/components/responses/401"
        "500":
          $ref: "#/components/responses/500"

  /v1/sandboxes:
    post:
      operationId: v1CreateSandbox
      summary: Create a new sandbox
      security:
        - ApiKeyAuth: [ ]
      tags: [sandboxes]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SandboxCreateRequest'
      responses:
        "200":
          description: Sandbox created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SandboxCreateResponse'
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "500":
          $ref: "#/components/responses/500"

    get:
      operationId: v1ListSandboxes
      summary: List running sandboxes for an orchestrator
      security:
        - ApiKeyAuth: [ ]
      tags: [sandboxes]
      parameters:
        - name: orchestratorId
          in: query
          required: true
          schema:
            type: string
      responses:
        "200":
          description: List of sandboxes
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SandboxListResponse'
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "500":
          $ref: "#/components/responses/500"

  /v1/sandboxes/{sandbox_id}:
    parameters:
      - $ref: '#/components/parameters/sandbox_id'
    patch:
      operationId: v1UpdateSandbox
      summary: Update an existing sandbox
      security:
        - ApiKeyAuth: [ ]
      tags: [sandboxes]
      parameters:
        - name: sandbox_id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SandboxUpdateRequest'
      responses:
        "204":
          description: Updated (no content)
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "500":
          $ref: "#/components/responses/500"

    delete:
      operationId: v1DeleteSandbox
      summary: Delete a sandbox
      security:
        - ApiKeyAuth: [ ]
      tags: [sandboxes]
      parameters:
        - name: sandbox_id
          in: path
          required: true
          schema:
            type: string
      responses:
        "204":
          description: Deleted (no content)
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "500":
          $ref: "#/components/responses/500"

  /v1/sandboxes/{sandbox_id}/pause:
    parameters:
      - $ref: '#/components/parameters/sandbox_id'
    post:
      operationId: v1PauseSandbox
      summary: Pause a running sandbox
      security:
        - ApiKeyAuth: [ ]
      tags: [sandboxes]
      parameters:
        - name: sandbox_id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SandboxPauseRequest'
      responses:
        "204":
          description: Paused (no content)
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "500":
          $ref: "#/components/responses/500"

  /v1/templates/builds:
    post:
      operationId: v1TemplateBuildCreate
      summary: Create a new template build
      security:
        - ApiKeyAuth: [ ]
      tags: [templates]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TemplateBuildCreateRequest'
      responses:
        "201":
          description: Build started
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "500":
          $ref: "#/components/responses/500"

  /v1/templates/builds/{build_id}:
    get:
      operationId: v1TemplateBuildStatus
      summary: Template build status
      security:
        - ApiKeyAuth: [ ]
      tags: [templates]
      parameters:
        - name: build_id
          in: path
          required: true
          schema:
            type: string
        - in: query
          name: orchestrator_id
          required: true
          schema:
            type: string
        - in: query
          name: template_id
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Successfully returned the template build status
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TemplateBuildStatusResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "500":
          $ref: "#/components/responses/500"

  /v1/templates/builds/{build_id}/logs:
    get:
      operationId: v1TemplateBuildLogs
      summary: Template build logs
      security:
        - ApiKeyAuth: [ ]
      tags: [templates]
      parameters:
        - name: build_id
          in: path
          required: true
          schema:
            type: string
        - in: query
          name: orchestrator_id
          required: true
          schema:
            type: string
        - in: query
          name: template_id
          required: true
          schema:
            type: string
        - in: query
          name: offset
          schema:
            default: 0
            type: integer
            format: int32
            minimum: 0
          description: Index of the starting build log that should be returned with the template
      responses:
        "200":
          description: Successfully returned the template build logs
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TemplateBuildLogsResponse"
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "500":
          $ref: "#/components/responses/500"

    delete:
      operationId: v1TemplateBuildDelete
      summary: Template build delete
      security:
        - ApiKeyAuth: [ ]
      tags: [templates]
      parameters:
        - name: build_id
          in: path
          required: true
          schema:
            type: string
        - in: query
          name: template_id
          required: true
          schema:
            type: string
        - in: query
          name: orchestrator_id
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Successfully deleted template build
        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "500":
          $ref: "#/components/responses/500"
