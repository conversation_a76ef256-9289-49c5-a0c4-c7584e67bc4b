{
  "search.exclude": {
    "**/.git": true,
    "**/.svn": true,
    "**/.hg": true,
    "**/CVS": true,
    "**/.DS_Store": true,
    ".terraform/**": true,
    ".vscode/**": true,
    ".git/**": true,
    "packages/orchestrator/.shared/**": true,
    "packages/orchestrator/.block-storage/**": true,
    "packages/fc-kernels/linux/**": true,
    "packages/fc-versions/firecracker/**": true,
    "packages/fc-versions/builds/**": true,
  },
  "files.watcherExclude": {
    "**/.git": true,
    "**/.svn": true,
    "**/.hg": true,
    "**/CVS": true,
    "**/.DS_Store": true,
    ".terraform/**": true,
    ".vscode/**": true,
    ".git/**": true,
    "packages/fc-kernels/linux/**": true,
    "packages/fc-versions/firecracker/**": true,
    "packages/fc-versions/builds/**": true,
  },
  "editor.quickSuggestions": {
    "strings": true
  },
  "editor.formatOnSave": true,
  "[terraform]": {
    "editor.tabSize": 2
  },
  "go.delveConfig": {
    "debuggerMode": "native",
  },
  "[go]": {
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.organizeImports": "explicit"
    },
  },
  "[go.mod]": {
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.organizeImports": "explicit"
    },
  },
  "editor.tabCompletion": "on",
  "go.lintTool": "golangci-lint",
  "go.lintFlags": [
    "--fast"
  ],
  "go.liveErrors": {
    "enabled": true,
    "delay": 500
  },
  "go.lintOnSave": "workspace",
  "editor.codeActionsOnSave": {
    "source.organizeImports": "explicit",
  },
  "go.useLanguageServer": true,
  "go.languageServerExperimentalFeatures": {
    "diagnostics": true
  },
  "remote.SSH.defaultExtensions": [
    "golang.go",
  ],
  "go.autocompleteUnimportedPackages": true,
  "go.toolsManagement.autoUpdate": true,
  "go.testOnSave": false,
  "go.testTimeout": "20s",
  "go.inlayHints.assignVariableTypes": true,
  "go.inlayHints.compositeLiteralFields": true,
  "go.inlayHints.compositeLiteralTypes": true,
  "go.inlayHints.constantValues": true,
  "go.inlayHints.functionTypeParameters": true,
  "go.inlayHints.rangeVariableTypes": true,
  "go.inlayHints.parameterNames": true,
  "go.buildOnSave": "off",
  "go.formatTool": "gofumpt",
  "go.survey.prompt": false,
  "go.useCodeSnippetsOnFunctionSuggestWithoutType": true,
  "go.useCodeSnippetsOnFunctionSuggest": true,
  "[go][go.mod]": {
    "editor.codeActionsOnSave": {
      "source.organizeImports": "explicit"
    }
  },
  "deepscan.enable": true
}