PREFIX="e2b-" # prefix identifier for all resources

# your GCP project ID
GCP_PROJECT_ID=
# GCP region where the resources will be deployed, e.g. us-west1
GCP_REGION=
# GCP zone where the resources will be deployed, e.g. us-west1-a
GCP_ZONE=

# prod, staging, dev
TERRAFORM_ENVIRONMENT=
# GCS bucket name
TERRAFORM_STATE_BUCKET=

# This is for the nomad and consul client (all jobs are running on client)
# e.g. n1-standard-8
CLIENT_MACHINE_TYPE=
# e.g. 1
CLIENT_CLUSTER_SIZE=
# Max number of additional instances if the CPU usage is above 80%, e.g. 0
CLIENT_CLUSTER_AUTO_SCALING_MAX=
# e.g. 1
CLIENT_REGIONAL_CLUSTER_SIZE=

# This is the nomad and consul server (only for scheduling and service discovery)
# eg e2-standard-2
SERVER_MACHINE_TYPE=
# e.g. 3
SERVER_CLUSTER_SIZE=

# eg e2-standard-4
API_MACHINE_TYPE=
# e.g. 1
API_CLUSTER_SIZE=

# eg n1-standard-8
BUILD_MACHINE_TYPE=
# e.g. 1
BUILD_CLUSTER_SIZE=

# e.g. e2-standard-4
CLICKHOUSE_MACHINE_TYPE=
# e.g. 2
CLICKHOUSE_CLUSTER_SIZE=

# your domain name, eg great-innovations.dev
DOMAIN_NAME=

# your Postgres connection string,
# for Supabase format postgresql://postgres.<username>:<password>.<host>@<your-full-url-domain.com>:<port-number>/postgres
POSTGRES_CONNECTION_STRING=

# ------------------------------------------- Optional block -----------------------------------------------------------
# Managed Redis (default: false)
REDIS_MANAGED=false
# Managed Grafana (default: false), read more in ./terraform/grafana/README.md
MANAGED_GRAFANA=false
# Additional domains (separated by commas)
ADDITIONAL_DOMAINS=
# Template bucket name (if you want to use a different bucket for templates then the default one)
TEMPLATE_BUCKET_NAME=
# Hash seed used for generating sandbox access tokens, not needed if you are not using them
SANDBOX_ACCESS_TOKEN_HASH_SEED=abcdefghijklmnopqrstuvwxyz

# Integration tests variables (only for running integration tests locally)
# your domain name, e.g. https://api.great-innovations.dev
TESTS_API_SERVER_URL=
# Host of the orchestrator, e.g. localhost:5008
# If connecting remotely, you might need to bridge the orchestrator connection as it's not publicly available
TESTS_ORCHESTRATOR_HOST=
# Envd proxy, e.g. https://client-proxy.great-innovations.dev
# This can be either session proxy or client proxy, depending on your local setup
TESTS_ENVD_PROXY=
# your sandbox template ID, e.g. base
TESTS_SANDBOX_TEMPLATE_ID=base
# your Team API key
TESTS_E2B_API_KEY=
